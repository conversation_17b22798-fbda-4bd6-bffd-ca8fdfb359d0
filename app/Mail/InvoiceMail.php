<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;

class InvoiceMail extends Mailable
{
    use Queueable, SerializesModels;

    public array  $data;      // table rows, totals, etc.
    public string $filePath;  // absolute path to attached PDF

    public function __construct(array $data, string $filePath)
    {
        $this->data     = $data;
        $this->filePath = $filePath;
    }

    public function build(): self
    {
        return $this->subject('Your Invoice')
                    ->view('emails.invoice')          // create a simple view
                    ->attach($this->filePath, [
                        'as'   => basename($this->filePath),
                        'mime' => 'application/pdf',
                    ]);
    }
}
