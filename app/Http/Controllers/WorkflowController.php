<?php

namespace App\Http\Controllers;

use App\Models\Booking;
use App\Models\BookingWorkflowApproverStatus;
use App\Models\Client;
use App\Models\ClientUnitChat;
use App\Models\Rights\Role;
use App\Models\User;
use App\Models\Workflow;
use App\Models\WorkflowApprover;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class WorkflowController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $workflows = Workflow::where('portal_type', $user->portal_type)->where('portal_id', $user->portal_id)->get();
        $userHasWorkflow = Workflow::where('created_by', Auth::id())->exists();
        return view('workflows.index', compact('workflows', 'userHasWorkflow'));
    }

    public function create()
    {
        $user = Auth::user();

        $roles = Role::where(function ($query) use ($user) {
            $type = $user->portal_type === Client::class ? 'client' : 'unit';
            $query->where('type', $type)
                ->where(function ($query) use ($user) {
                    $query->where('type_id', $user->portal_id)
                        ->orWhere('type_id', 0);
                });
        })->get();

        return view('workflows.create', compact('roles'));
    }

    public function store(Request $request)
    {
        // Start a database transaction
        DB::beginTransaction();

        try {
            // Validate incoming request data
            $validated = $request->validate([
                'name' => ['required', 'string', 'max:255'],
                'type' => ['required', 'in:sequential,parallel'],
                'client_approval' => ['nullable', 'boolean'],
                'approval_steps' => ['required', 'array'],
                'approval_steps.*.order' => ['required', 'integer'],
                'approval_steps.*.role' => ['required', 'string', 'max:255'],
                'approval_steps.*.mandatory' => ['nullable', 'boolean'],
            ]);

            // Create the workflow record
            $workflow = new Workflow();
            $workflow->name = $validated['name'];
            $workflow->type = $validated['type'];
            $workflow->requires_client_approval = $validated['client_approval'] ?? false;
            $workflow->created_by = Auth::user()->id;
            $workflow->portal_type = Auth::user()->portal_type;
            $workflow->portal_id = Auth::user()->portal_id;
            $workflow->save();

            // Insert approval steps
            foreach ($validated['approval_steps'] as $step) {
                $workflowApprover = new WorkflowApprover();
                $workflowApprover->workflow_id = $workflow->id;
                $workflowApprover->role_id = $step['role']; // assuming role is an ID
                $workflowApprover->approval_order = $step['order'];
                $workflowApprover->is_mandatory = $step['mandatory'] ?? false;
                $workflowApprover->save();
            }

            // Commit the transaction if all queries succeed
            DB::commit();

            // Redirect with success message
            return redirect()
                ->route('workflows.index')
                ->with('success', 'Workflow created successfully.');
        } catch (\Exception $e) {
            // Rollback the transaction in case of failure
            DB::rollBack();

            // You can log the exception or return an error message
            return redirect()->back()->with('error', 'Failed to create the workflow. Please try again.');
        }
    }

    public function edit($id)
    {
        $workflow = Workflow::with('approvers')->findOrFail($id);
        $user = Auth::user();

        // $workflow = WorkflowApprover::with('workflow')->findOrFail($id);
        $roles = Role::where(function ($query) use ($user) {
            $type = $user->portal_type === Client::class ? 'client' : 'unit';
            $query->where('type', $type)
                ->where(function ($query) use ($user) {
                    $query->where('type_id', $user->portal_id)
                        ->orWhere('type_id', 0);
                });
        })->get();

        // Return the edit view with the workflow data and available roles
        return view('workflows.edit', compact('workflow', 'roles'));
    }

    public function update(Request $request, $id)
    {
        // Validate the incoming data
        try {
            $request->validate([
                'name' => 'required|string|max:255',
                'type' => 'required|in:sequential,parallel',
                'client_approval' => 'nullable|boolean',
                'approval_steps' => 'nullable|array',
                'approval_steps.*.order' => 'required|integer',
                'approval_steps.*.role' => 'required|exists:roles,role_id',
                'approval_steps.*.mandatory' => 'nullable|boolean',
            ]);
        } catch (\Illuminate\Validation\ValidationException $e) {
            return redirect()->back()
                ->withErrors($e->validator)
                ->withInput();
        }

        // Find the workflow by ID
        $workflow = Workflow::findOrFail($id);

        // Update the workflow attributes
        $workflow->update([
            'name' => $request->input('name'),
            'type' => $request->input('type'),
            'requires_client_approval' => $request->input('client_approval', 0),
        ]);

        // Update the approval steps
        if ($request->has('approval_steps')) {
            $updated = [];
            foreach ($request->input('approval_steps') as $index => $stepData) {
                // Check if the approver step exists, and update it
                $step = WorkflowApprover::find($stepData['id'] ?? null);

                if ($step) {
                    $updated[] = $step->id;
                    $step->update([
                        'approval_order' => $stepData['order'],
                        'role_id' => $stepData['role'],
                        'is_mandatory' => $stepData['mandatory'] ?? false,
                    ]);
                } else {
                    // If the step does not exist, create a new one
                    $step = $workflow->approvers()->create([
                        'approval_order' => $stepData['order'],
                        'role_id' => $stepData['role'],
                        'is_mandatory' => $stepData['mandatory'] ?? false,
                    ]);
                    $updated[] = $step->id;
                }
            }
            WorkflowApprover::where('workflow_id', $workflow->id)
                ->whereNotIn('id', $updated)
                ->delete();
        }

        // Return back with a success message
        return redirect()->route('workflows.index')->with('success', 'Workflow updated successfully.');
    }

    public function destroy(Workflow $workflow)
    {
        $workflow->delete();
        return redirect()->route('workflows.index')->with('success', 'Workflow deleted successfully.');
    }
    public function approve()
    {
        $validated = request()->validate([
            'id' => ['required', 'integer'],
            'note' => ['required_if:action,false', 'string'],
            'action' => ['required', 'boolean'],
        ]);

        $booking = Booking::with(['bookingWorkflows', 'bookingWorkflowStatus', 'unit.client'])->findOrFail($validated['id']);
        $user = Auth::user();

        // Find the correct workflow based on portal_type and portal_id
        $bookingWorkflows = $booking->bookingWorkflows
            ->where('approver_type', $user->portal_type)
            ->where('approver_id', $user->portal_id)->first();

        // If no direct workflow found and user is client, try to find unit workflow
        // This handles cases where client users are approving based on budget permissions
        if (!$bookingWorkflows && $user->portal_type === \App\Models\Client::class) {
            $bookingWorkflows = $booking->bookingWorkflows->first();
        }

        if (!$bookingWorkflows) {
            return response()->json([
                'message' => 'No applicable workflow found for this booking.'
            ], 404);
        }

        $workflow = $bookingWorkflows->workflow;

        if (!$workflow) {
            return response()->json([
                'message' => 'No applicable workflow found for your portal access.'
            ], 404);
        }
        // Find the approver record for the current user
        $workflowApprover = collect($workflow->get('approvers'))->filter(function ($approver) use ($user, $booking) {
            // Check for standard role match
            if ($approver['role_id'] == $user->role->role_id) {
                return true;
            }

            // Check for additional conditions (budget approval permissions)
            if (isset($approver['additional_conditions']) && $user->role) {
                switch($approver['additional_conditions']) {
                    case 'able_to_approve_within_budget':
                        return $user->role->able_to_approve_within_budget == 1 &&
                               (
                                   ($user->role->type === 'unit' && ($user->role->type_id == 0 || $user->role->type_id == $user->portal_id)) ||
                                   ($user->role->type === 'client' && $booking->unit && $booking->unit->client && ($user->role->type_id == 0 || $user->role->type_id == $booking->unit->client->clientId))
                               );
                    case 'able_to_approve_weekly_budget':
                        return $user->role->able_to_approve_weekly_budget == 1 &&
                               (
                                   ($user->role->type === 'unit' && ($user->role->type_id == 0 || $user->role->type_id == $user->portal_id)) ||
                                   ($user->role->type === 'client' && $booking->unit && $booking->unit->client && ($user->role->type_id == 0 || $user->role->type_id == $booking->unit->client->clientId))
                               );
                    case 'able_to_approve_special_allowance_budget':
                        return $user->role->able_to_approve_special_allowance_budget == 1 &&
                               (
                                   ($user->role->type === 'unit' && ($user->role->type_id == 0 || $user->role->type_id == $user->portal_id)) ||
                                   ($user->role->type === 'client' && $booking->unit && $booking->unit->client && ($user->role->type_id == 0 || $user->role->type_id == $booking->unit->client->clientId))
                               );
                }
            }

            // Check if current user has same budget approval permissions as the approver role
            if ($user->role) {
                $approverRole = \App\Models\Rights\Role::where('role_id', $approver['role_id'])->first();

                if ($approverRole) {
                    // Check if current user has the same budget approval permissions as the approver role
                    $hasSamePermissions = false;

                    if ($approverRole->able_to_approve_within_budget && $user->role->able_to_approve_within_budget) {
                        $hasSamePermissions = true;
                    } elseif ($approverRole->able_to_approve_weekly_budget && $user->role->able_to_approve_weekly_budget) {
                        $hasSamePermissions = true;
                    } elseif ($approverRole->able_to_approve_special_allowance_budget && $user->role->able_to_approve_special_allowance_budget) {
                        $hasSamePermissions = true;
                    }

                    if ($hasSamePermissions) {
                        return (
                            ($user->role->type === 'unit' && ($user->role->type_id == 0 || $user->role->type_id == $user->portal_id)) ||
                            ($user->role->type === 'client' && $booking->unit && $booking->unit->client && ($user->role->type_id == 0 || $user->role->type_id == $booking->unit->client->clientId))
                        );
                    }
                }
            }

            return false;
        })->first();

        if (!$workflowApprover) {
            return response()->json([
                'message' => 'You are not authorized to approve this booking.'
            ], 403);
        }

        // Check sequential approval rules
        if ($workflow->get('type') === Workflow::SEQUENTIAL) {
            $lastApproved = $booking->bookingWorkflowStatus()
                ->where('workflow_id', $workflow['id'])
                ->latest()
                ->first();

            if ($lastApproved) {
                $lastApprovalorder = collect($workflow->get('approvers'))->where('id',$lastApproved->workflow_approvers_id)->first();
                if ($lastApprovalorder['approval_order'] >= $workflowApprover['approval_order']) {
                    return response()->json([
                        'message' => 'You are not authorized to approve this booking.'
                    ], 403);
                }
            } else {
                // No one has approved yet — current approver must be the first in order
                if ($workflowApprover['approval_order'] != 1) {
                    return response()->json([
                        'message' => 'You are not authorized to approve this booking.'
                    ], 403);
                }
            }
        }

        // Save the approval or decline
        $bookingWorkflowStatus = new BookingWorkflowApproverStatus();
        $bookingWorkflowStatus->workflow_id = $workflow['id'];
        $bookingWorkflowStatus->workflow_approvers_id = $workflowApprover['id'];
        $bookingWorkflowStatus->booking_id = $booking->bookingId;
        $bookingWorkflowStatus->approval_status = $validated['action'] ? BookingWorkflowApproverStatus::APPROVED : BookingWorkflowApproverStatus::DECLINED;
        $bookingWorkflowStatus->user_id = $user->id;
        $bookingWorkflowStatus->notes = isset($validated['note']) ? $validated['note'] : null;
        $bookingWorkflowStatus->save();
        $status = "";
        if($validated['action']){
        $status =  BookingWorkflowApproverStatus::APPROVED;
        }else{
            $status =  BookingWorkflowApproverStatus::DECLINED;
        }
        //change first letter upercase
        $status = ucfirst($status);

        ClientUnitChat::chatSave($booking->unitId,$status.' the shift', $booking->bookingId, true);

        return response()->json([
            'message' => 'Booking approval status updated successfully.'
        ]);
    }
}
