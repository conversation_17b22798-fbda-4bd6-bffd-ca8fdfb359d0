<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\Holiday;
use App\Models\InvoiceUnit;
use Carbon\Carbon;
use App\Models\Quotation;
use Carbon\CarbonImmutable;
use App\Services\UnitHelperService;
use Illuminate\Http\Request;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\TaxYear;
use App\Models\TaxWeek;
use App\Models\Booking;

class InvoiceUnitController extends Controller
{
  protected $unitHelper;
  protected $clientUnitId; 
  
  public function __construct(UnitHelperService $unitHelper)
  {
      $this->unitHelper = $unitHelper;
     
  }
    public function index(){

        $threeMonthsAgo = Carbon::now()->subMonths(3);
        
        $invoices = InvoiceUnit::with('unit')
        ->where('client_id',request('client'))
        ->where('client_id',request('client'))
        ->whereDate('invoice_date', '>=', $threeMonthsAgo)
        ->orderBy('unit_id')
        ->orderBy('invoice_date')
        ->get();

        foreach ($invoices as $invoiceItem) {
            $diffDays = Carbon::now()->diffInDays(Carbon::parse($invoiceItem->due_date),false);
            $invoiceItem->diffDays = $diffDays;
            if($invoiceItem->status == 0 && $diffDays < 0){
              $invoiceItem->redBg = 1;
            }elseif($invoiceItem->status == 0 && $diffDays > 0){
              $invoiceItem->redBg = 2;
            }else{
              $invoiceItem->redBg = 0;
            }
          }
    	  $units = ClientUnit::where('clientId',request('client'))->where('clientUnitId','<>',81)->get();
        $events = Holiday::where('date','>=',date('Y-m-d'))->orderBy('date','DESC')->take(5)->get();
        $client = Client::find(request('client'));
      	return ['status'=>200,'invoices'=>$invoices,'units'=>$units,'events'=>$events,'client'=>$client];
    }
    public function invoiceslist(){
      $invoice = InvoiceUnit::where('unit_id',auth()->user()->portal_id)->orderBy('invoice_id','DESC')->get();
      foreach ($invoice as $invoiceItem) {
        $diffDays = CarbonImmutable::now()->diffInDays(CarbonImmutable::parse($invoiceItem->due_date), false);
        $invoiceItem->diffDays = $diffDays;
        if($invoiceItem->status == 0 && $diffDays < 0){
          $invoiceItem->redBg = 1;
        }elseif($invoiceItem->status == 0 && $diffDays > 0){
          $invoiceItem->redBg = 2;
        }else{
          $invoiceItem->redBg = 0;
        }
      }
      $unitHelper = app(UnitHelperService::class);
      $events = $unitHelper->getComingEvents();
      return view('unitPortal.invoicelist',compact('invoice','events'));
  }
    public function invoice_listing(){

    $taxyearId = TaxYear::where('default', 1)->first();
    $TaxYear = TaxYear::get();
    $TaxWeek = TaxWeek::selectRaw('MIN(taxWeekId) as taxWeekId, weekNumber')
        ->where('taxYearId', $taxyearId->taxYearId)
        ->groupBy('weekNumber')
        ->orderBy('weekNumber')
        ->get();

    $loggedId = auth()->user()->portal_id;
    $actionWeek = request('action_week');
    $filterWeek = request('filter_tax_week');
    $filterYear = request('filter_tax_year') ?: $taxyearId->taxYearFrom;
    //APPROVED SECTION
    $oneMonthAgo = Carbon::now()->subMonth()->startOfDay();
    $today = Carbon::now()->endOfDay();
    $startPrevWeek = Carbon::now()->subWeeks(7)->startOfWeek(Carbon::MONDAY)->toDateString();//2025-05-19
    $endPrevWeek = Carbon::now()->subWeek(7)->endOfWeek(Carbon::SUNDAY)->toDateString();//2025-05-25
   if (auth()->user()->portal_type == 'App\\Models\\Client') {
            $summaryQuery = Booking::from('bookings as bookings')
                ->leftJoin('timesheets as timesheets', 'timesheets.bookingId', '=', 'bookings.bookingId')
                ->join('client_units as client_units', 'client_units.clientUnitId', '=', 'bookings.unitId')
                ->leftJoin('quotations as quotations', 'quotations.timesheetId', '=', 'timesheets.timesheetId')
                ->whereIn('bookings.staffStatus', [2, 3])
                ->whereIn('bookings.unitStatus', [1, 4])
                ->where('client_units.clientId', $loggedId)
                ->whereNotNull('quotations.week')
                ->where('quotations.year',date('Y'))
                ->whereNotNull('bookings.staffId')
                ->where('timesheets.invoice_status',1)
                ->where('quotations.year',date('Y'))
                ->whereBetween('bookings.date', [$startPrevWeek, $endPrevWeek])
                ->orWhereBetween('timesheets.isMovedFromPreviousWeek', [$startPrevWeek, $endPrevWeek]);
   }

      $loggedId = auth()->user()->portal_id;
      if (auth()->user()->portal_type == 'App\\Models\\Client') {
          $query = Quotation::with(['booking.unit', 'timesheet','booking.staff','booking.shift','booking.unit.client','booking.category'])
              ->whereHas('booking.unit', function ($q) use ($loggedId) {
                  $q->where('clientId', $loggedId);
              })
              ->where('status', 1)
              ->orderBy('created_at', 'DESC');
              
      }
      $quotation = $query->first();
      //echo '<pre>'; print_r($quotation); exit;
      $units = ClientUnit::where('clientId',auth()->user()->portal_id)->where('clientUnitId','<>',81)->get();
      return view('unitPortal.invoice_listing',compact('units','quotation'));
  }


      public function invoices(Request $request)
{
    $units = ClientUnit::where('clientId', auth()->user()->portal_id)
        ->where('clientUnitId', '<>', 81)
        ->get();

    $threeMonthsAgo = Carbon::now()->subMonths(3);
    $invoiceQuery = InvoiceUnit::where('client_id', auth()->user()->portal_id)
        ->whereDate('invoice_date', '>=', $threeMonthsAgo)
        ->orderBy('invoice_id', 'DESC');

    if (request('unit_id')) {
        $invoiceQuery->where('unit_id', request('unit_id'));
    }
    if (request('status')) {
        if (request('status') == 2) {
            $invoiceQuery->where('status', 0)
                         ->whereDate('due_date', '<', Carbon::now());
        } else {
            $invoiceQuery->where('status', request('status'));
        }
    }
    $invoice = $invoiceQuery->get();
    foreach ($invoice as $invoiceItem) {
        $diffDays = CarbonImmutable::now()->diffInDays(CarbonImmutable::parse($invoiceItem->due_date), false);
        $invoiceItem->diffDays = $diffDays;
        if ($invoiceItem->status == 0 && $diffDays < 0) {
            $invoiceItem->redBg = 1;
        } elseif ($invoiceItem->status == 0 && $diffDays > 0) {
            $invoiceItem->redBg = 2;
        } else {
            $invoiceItem->redBg = 0;
        }
    }

    $unitHelper = app(UnitHelperService::class);
    $events = $unitHelper->getComingEvents();

    return view('unitPortal.invoice', compact('invoice', 'events', 'units'));
}
 public function archived(Request $request)
{
    $units = ClientUnit::where('clientId', auth()->user()->portal_id)
        ->where('clientUnitId', '<>', 81)
        ->get();

    $threeMonthsAgo = Carbon::now()->subMonths(3);
    $invoiceQuery = InvoiceUnit::where('client_id', auth()->user()->portal_id)
        ->where('status',1)
        ->whereDate('invoice_date', '<=', $threeMonthsAgo)
        ->orderBy('invoice_id', 'DESC');

    if (request('unit_id')) {
        $invoiceQuery->where('unit_id', request('unit_id'));
    }
    
    $invoice = $invoiceQuery->get();
    foreach ($invoice as $invoiceItem) {
        $diffDays = CarbonImmutable::now()->diffInDays(CarbonImmutable::parse($invoiceItem->due_date), false);
        $invoiceItem->diffDays = $diffDays;
        if ($invoiceItem->status == 0 && $diffDays < 0) {
            $invoiceItem->redBg = 1;
        } elseif ($invoiceItem->status == 0 && $diffDays > 0) {
            $invoiceItem->redBg = 2;
        } else {
            $invoiceItem->redBg = 0;
        }
    }

    $unitHelper = app(UnitHelperService::class);
    $events = $unitHelper->getComingEvents();

    return view('unitPortal.archived-invoice', compact('invoice', 'events', 'units'));
}

}