<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Client;
use App\Models\ClientReminder;
use App\Models\ClientTodos;
use App\Models\ClientUnit;
use App\Models\ClientUnitBudget;
use App\Models\ClientUnitPayment;
use App\Models\ClientUnitSchedule;
use App\Models\Holiday;
use App\Models\StaffUnitPayment;
use App\Models\ClientUnitAnnualBudget;
use Carbon\Carbon;
use App\Models\TaxYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
class DashboardControllerCopy extends Controller
{
    //EXPENSE BUDGET AVAILABLE TOP OF THE PAGE SECTION
    public function getBudgets(){
        $bookingcost=0;
        $budget = ClientUnitAnnualBudget::whereIn('client_unit_id', function($query) {
            $query->select('clientUnitId')
                  ->from('client_units')
                  ->where('clientId', request('client'));
        })->first();


        $units = ClientUnit::select('clientUnitId')->where('clientId',request('client'))
        ->where('clientUnitId','<>',81)->get()->toArray();
        
        $clientUnitId = array_column($units, 'clientUnitId');
        $expense = Booking::whereHas('unit',function($query){
            $query->where('clientId',request('client'));
        })->whereIn('unitStatus',[4,1])->get();
        
        if(request('filter')){
           
            if(request('filter') ==1){
                if(isset($budget->annual_budget)){
                   $budget =  $budget->annual_budget/12;
                }else{
                    $budget=0;
                }
                $expense = Booking::whereIn('unitId', function ($query) {
                    $query->select('clientUnitId')
                          ->from('client_units') // Specify the table name
                          ->where('clientId', request('client'));
                })
                ->whereIn('unitStatus',[4,1])->whereMonth('date',date('n'))->whereYear('date',date('Y'))->get();
               
            }
            if(request('filter') ==2){
                if(isset($budget->annual_budget)){
                   $budget =  $budget->annual_budget/12;
                }else{
                    $budget=0;
                } 
                $expense = Booking::whereIn('unitId', function ($query) {
                    $query->select('clientUnitId')
                          ->from('client_units') // Specify the table name
                          ->where('clientId', request('client'));
                })->whereIn('unitStatus',[4,1])->whereMonth('date',date('n')-1)->whereYear('date',date('Y'))->get();
            }
            if(request('filter') ==4){
                if(isset($budget->annual_budget)){
                   $budget =  $budget->annual_budget/12;
                }else{
                    $budget=0;
                } 
                $expense = Booking::whereIn('unitId', function ($query) {
                    $query->select('clientUnitId')
                          ->from('client_units') // Specify the table name
                          ->where('clientId', request('client'));
                })->whereIn('unitStatus',[4,1])->whereMonth('date',date('n')+1)->whereYear('date',date('Y'))->get();
            }
        }
         //print_r($budget); exit;
       
        $bookingcost=0;
        foreach ($expense as $thismo) {
            $selectColumn = Carbon::parse($thismo->date)->format('l'); // Get the day name
            $columnName = $this->determineColumnName($thismo, $selectColumn);
            $payment = $this->getpayment($columnName, $thismo->categoryId, $thismo->unitId);
            
            $schedule = $this->getschedule($columnName, $thismo->categoryId, $thismo->shiftId,$thismo->unitId);
                if (isset($schedule)) {
                    if(isset($payment->timesheet->unitHours)){
                        $totalHoursUnit = $payment->timesheet->unitHours;
                    }else{
                        $totalHoursUnit = $schedule->totalHoursUnit;
                    }
                    if(isset($payment->$columnName)){
                        $bookingcost += $totalHoursUnit * $payment->$columnName;
                    }
              }
        }

        $balance = $budget - $bookingcost;
            
        $units = ClientUnit::where('clientId',request('client'))->where('clientUnitId','<>',81)->get();
        $events = Holiday::where('date','>=',date('Y-m-d'))->orderBy('date','ASC')->take(5)->get();
        $client = Client::find(request('client'));
    	return ['status'=>200,'budget'=>$budget,'units'=>$units,'events'=>$events,'expense'=>$bookingcost,'balance'=>$balance,'client'=>$client];
        

        // return view('clientPortal.dashboard',compact('budget','units','events','expense','balance','client'));
    }
    public function getpayment($column,$category,$unitId){
        $TaxYear = TaxYear::where('default',1)->first();
        $taxYearId = $TaxYear->taxYearId;
        return $clientPayments = ClientUnitPayment::select($column)->
         where('clientUnitId',$unitId)
         ->where('rateType',1)
         ->where('status',1)
         ->where('staffCategoryId',$category)
         ->where('tax_year_id',$taxYearId)
         ->
         first();
       }
       public function getschedule($column,$category,$shiftId,$unitId){
        return $scheduleAll = ClientUnitSchedule::select('totalHoursUnit')->where('clientUnitId',$unitId)
         ->where('staffCategoryId',$category)
         ->where('status',1)
         ->where('shiftId',$shiftId)
         ->first();
     
         }
         public function determineColumnName($thismo, $selectColumn)
         {
         if (in_array($thismo->shiftId, [1, 2, 3])) {
             return "day" . $selectColumn;
         } elseif ($thismo->shiftId == 5) {
             return "twilight_" . strtolower($selectColumn);
         } else {
             return "night" . $selectColumn;
         }
       }
       //STARTING OF BUDGET GRAPH
       public function getDonutGraph()
       {
           $filter = request('filter');
           $clientId = request('client');
       
           // Determine the month and year based on the filter
           switch ($filter) {
               case 1:
                   $month = date('n');
                   $year = date('Y');
                   break;
               case 2:
                   $month = date('n') - 1;
                   $year = date('Y');
                   break;
               case 4:
                   $month = date('n') + 1;
                   $year = date('Y');
                   break;
               default:
                   $month = date('n');
                   $year = date('Y');
           }
       
           // Fetch all units for the client excluding unitId 81
           $units = ClientUnit::where('clientId', $clientId)
               ->where('clientUnitId', '<>', 81)
               ->get();
       
           // Fetch budgets for each unit for the specified month and year
           $unitBudgets = ClientUnitBudget::whereIn('clientUnitId', $units->pluck('clientUnitId'))
               ->where('month', $month)
               ->where('year', $year)
               ->get()
               ->keyBy('clientUnitId');
       
           // Fetch bookings for each unit for the specified month and year
           $bookings = Booking::whereIn('unitId', $units->pluck('clientUnitId'))
               ->whereIn('unitStatus', [1, 4])
               ->whereMonth('date', $month)
               ->whereYear('date', $year)
               ->get();
       
           // Initialize variables to store data for the graph and table
           $unitBudgetData = [];
           $unitExpenseData = [];
           $tableBudgetData = [];
           $tableExpenseData = [];
           $totalBudget = 0;
           $totalExpense = 0;
       
           // Iterate over each unit to calculate budget and expense
           foreach ($units as $unit) {
               // Get the budget for the unit
               $budget = $unitBudgets->get($unit->clientUnitId);
               $budgetAmount = $budget ? $budget->budget : 0;
       
               // Initialize booking cost for the unit
               $bookingCost = 0;
       
               // Calculate the total booking cost for the unit
               foreach ($bookings->where('unitId', $unit->clientUnitId) as $booking) {
                   $dayName = Carbon::parse($booking->date)->format('l');
                   $columnName = $this->determineColumnName($booking, $dayName);
                   $payment = $this->getpayment($columnName, $booking->categoryId, $booking->unitId);
                   $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId,$booking->unitId);
       
                   if ($schedule) {
                       $unitHours = $payment->timesheet->unitHours ?? $schedule->totalHoursUnit;
                       $rate = $payment->$columnName ?? 0;
                       $bookingCost += $unitHours * $rate;
                   }
               }
       
               // Calculate the percentage of budget used
               $percentage = $budgetAmount > 0 ? round(($bookingCost / $budgetAmount) * 100, 2) : 0;
       
               // Add data for the graph and table
               $unitBudgetData[] = ['name' => $unit->name, 'y' => $budgetAmount];
               $unitExpenseData[] = ['name' => $unit->name, 'y' => $bookingCost];
               $tableBudgetData[] = ['unit' => $unit->name, 'amount' => $budgetAmount, 'percentage' => $percentage];
               $tableExpenseData[] = ['unit' => $unit->name, 'amount' => $bookingCost, 'percentage' => $percentage];
       
               // Accumulate totals
               $totalBudget += $budgetAmount;
               $totalExpense += $bookingCost;
           }
       
           // Calculate overall percentage
           $overallPercentage = $totalBudget > 0 ? round(($totalExpense / $totalBudget) * 100, 2) : 0;
       
           // Prepare the final graph data
           $graph = [
               'budgetGraph' => [
                   'data' => $unitBudgetData,
                   'title' => date("F", mktime(0, 0, 0, $month, 10)),
                   'table' => [
                       'rows' => $tableBudgetData,
                       'tfoot' => [
                           'amount' => $totalBudget,
                           'percentage' => $overallPercentage
                       ]
                   ]
               ],
               'expenseGraph' => [
                   'data' => $unitExpenseData,
                   'title' => date("F", mktime(0, 0, 0, $month, 10)),
                   'table' => [
                       'rows' => $tableExpenseData,
                       'tfoot' => [
                           'amount' => $totalExpense,
                           'percentage' => $overallPercentage
                       ]
                   ]
               ]
           ];
       
           return response()->json(['status' => 200, 'graph' => $graph]);
       }
       
       
       public function getBarBudgetUsage()
       {
           $units = ClientUnit::where('clientId', request('client'))
               ->where('clientUnitId', '<>', 81)
               ->get();
       
           $budgets = [];
           $expenses = [];
           $bookingsItems = collect(); // Initialize an empty collection for bookings
       
           foreach ($units as $unit) {
               $filter = request('filter');
               $month = $this->getMonthForFilter($filter);
               $year = date('Y');
       
               // Fetch bookings for the current unit based on the filter
               $bookingsItems = $this->getBookingsForUnit($unit, $month, $year);
       
               // Calculate the budget amount for the current unit
               $budgetAmount = ClientUnitBudget::where('clientUnitId', $unit->clientUnitId)
                   ->where('month', $month)
                   ->where('year', $year)
                   ->sum('budget');
       
               // Calculate the booking cost (expense) for the current unit
               $bookingCost = $this->calculateBookingCost($bookingsItems, $unit);
       
               $budgets[] = round($budgetAmount, 2);
               $expenses[] = round($bookingCost, 2);
           }
       
           $bar = [
               'categories' => $units->pluck('name'),
               'data' => [
                   ['name' => 'Budget', 'data' => $budgets],
                   ['name' => 'Expense', 'data' => $expenses],
               ]
           ];
       
           // Calculate reason-wise expenses
           $reasonCategories = [
               'sickness' => 1,
               'holiday' => 2,
               'vacant' => 3,
               'resident_admission' => 4,
               'one_to_one' => 5,
               'extra_staff' => 6,
               'management' => 7,
               'others' => null,
           ];
       
           $totalCosts = $this->calculateReasonWiseCosts($bookingsItems, $reasonCategories);
       
           return ['status' => 200, 'graph' => $bar, 'reason' => $totalCosts];
       }
       
       private function getMonthForFilter($filter)
       {
           switch ($filter) {
               case 1: return date('n'); // Current month
               case 2: return date('n') - 1; // Previous month
               case 4: return date('n') + 1; // Next month
               default: return date('n');
           }
       }
       
       private function getBookingsForUnit($unit, $month, $year)
       {
           return Booking::where('unitId', $unit->clientUnitId)
               ->whereIn('unitStatus', [4, 1])
               ->whereMonth('date', $month)
               ->whereYear('date', $year)
               ->get();
       }
       
       private function calculateBookingCost($bookings, $unit)
       {
           $bookingCost = 0;
       
           foreach ($bookings as $booking) {
               $dayName = Carbon::parse($booking->date)->format('l');
               $columnName = $this->determineColumnName($booking, $dayName);
               $payment = $this->getpayment($columnName, $booking->categoryId, $unit->clientUnitId);
               $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId,$booking->unitId);
       
               if ($schedule) {
                   $unitHours = $payment->timesheet->unitHours ?? $schedule->totalHoursUnit;
                   $rate = $payment->$columnName ?? 0;
                   $bookingCost += $unitHours * $rate;
               }
           }
       
           return $bookingCost;
       }
       
       private function calculateReasonWiseCosts($bookings, $reasonCategories)
       {
           $totalCosts = array_fill_keys(array_keys($reasonCategories), ['cost' => 0, 'count' => 0]);
       
           foreach ($bookings as $booking) {
               $reason = array_search($booking->reason, $reasonCategories);
       
               if ($reason === false) {
                   continue;
               }
       
               $dayName = Carbon::parse($booking->date)->format('l');
               $columnName = $this->determineColumnName($booking, $dayName);
               $payment = $this->getpayment($columnName, $booking->categoryId, $booking->unitId);
               $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId,$booking->unitId);
       
               if ($schedule) {
                   $unitHours = $payment->timesheet->unitHours ?? $schedule->totalHoursUnit;
                   $rate = $payment->$columnName ?? 0;
                   $paymentAmount = $unitHours * $rate;
       
                   $totalCosts[$reason]['cost'] += $paymentAmount;
                   $totalCosts[$reason]['count']++;
               }
           }
       
           return $totalCosts;
       }
       


       public function getBookingCountsAndCosts(Request $request)
      {
        $clientId = $request->input('clientId');
     //  return $clientId 
    // Define the reason categories
    $reasonCategories = [
        'sickness' => 1,
        'holiday' => 2,
        'vacant' => 3,
        'resident_admission' => 4,
        'one_to_one' => 5,
        'extra_staff' => 6,
        'management' => 7,
        'others' => null,
    ];

    // Initialize an array to hold the counts and costs
    $bookingData = [];
//return request('client');
    // Loop through each reason category
    foreach ($reasonCategories as $reason => $categoryId) {
        // Fetch bookings for the current reason category
        $bookings = Booking::where('reason', $categoryId)
        ->whereIn('unitId', function ($query) use ($clientId){
            $query->select('clientUnitId')
                  ->from('client_units') // Specify the table name
                  ->where('clientId', $clientId);
        })->get();

        // Initialize variables for count and total cost
        $count = 0;
        $totalCost = 0;
        // Iterate over each booking to calculate count and cost
        foreach ($bookings as $booking) {
            // Determine the column name based on the booking details
            $dayName = Carbon::parse($booking->date)->format('l');
            $columnName = $this->determineColumnName($booking, $dayName);

            // Fetch payment and schedule details
            $payment = $this->getpayment($columnName, $booking->categoryId, $booking->unitId);
            $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId,$booking->unitId);

            // If schedule exists, calculate the booking cost
            if ($schedule) {
                $unitHours = $payment->timesheet->unitHours ?? $schedule->totalHoursUnit;
                $rate = $payment->$columnName ?? 0;
                $totalCost += $unitHours * $rate;
            }

            // Increment the count for the current reason category
            $count++;
        }

        // Store the data in the array
        $bookingData[$reason] = [
            'count' => $count,
            'cost' => round($totalCost, 2)
        ];
    }
  //  return $bookingData; exit;

    // Return the data
    return response()->json($bookingData);
}


  
public function getLineCharts()
{
    ini_set('max_execution_time', '300'); // 5 minutes

    $months = [];
    for ($m = 0; $m <= 6; $m++) {
        $months[] = date("M-y", strtotime(date('Y-m-01') . " -$m months"));
    }

    $units = ClientUnit::where('clientId', request('client'))
        ->where('clientUnitId', '<>', 81)
        ->get();

    $dateMonthStart = date("Y-m-01", strtotime(date('Y-m-01') . " -6 months"));
    $bookAll = Booking::whereIn('unitStatus', [4, 1])
        ->whereDate('date', '>', $dateMonthStart)
        ->whereIn('unitId', $units->pluck('clientUnitId'))
        ->get();

    $usageLineData = [];

    foreach ($units as $unit) {
        $usageLineData[] = [
            'name' => $unit->name,
            'data' => $this->calculateBookingCosts($bookAll, $unit)
        ];
    }

    $usageLine = [
        'data' => $usageLineData,
        'categories' => array_reverse($months),
    ];

    return ['status' => 200, 'usageLine' => $usageLine];
}

private function calculateBookingCosts($bookings, $unit)
{
    $data = [];
    for ($m = 6; $m >= 0; $m--) {
        $dateMonthStart = date("Y-m-01", strtotime(date('Y-m-01') . " -$m months"));
        $dateMonthEnd = date("Y-m-t", strtotime(date('Y-m-01') . " -$m months"));

        $bookingCost = 0;

        foreach ($bookings->where('unitId', $unit->clientUnitId)
                         ->whereBetween('date', [$dateMonthStart, $dateMonthEnd]) as $booking) {
            $dayName = Carbon::parse($booking->date)->format('l');
            $columnName = $this->determineColumnName($booking, $dayName);
            $payment = $this->getpayment($columnName, $booking->categoryId, $unit->clientUnitId);
            $schedule = $this->getschedule($columnName, $booking->categoryId, $booking->shiftId,$booking->unitId);

            if ($schedule) {
                $unitHours = $payment->timesheet->unitHours ?? $schedule->totalHoursUnit;
                $rate = $payment->$columnName ?? 0;
                $bookingCost += $unitHours * $rate;
            }
        }

        $data[] = round($bookingCost, 2);
    }

    return $data;
}

    public function getTableBookings(){
        $query = Booking::with('unit','category','shift','staff')->whereHas('unit',function($query){
            $query->where('clientId',request('client'));
        });
        if(request('unitId')){
            $query = $query->where('unitId',request('unitId'));
        }
        if(request('filter')){
            if(request('filter') ==1){
                $bookings = $query->where('unitStatus',4)->whereMonth('date',date('n'))->whereYear('date',date('Y'))->paginate(15);
            }
            if(request('filter') ==2){
                $bookings = $query->where('unitStatus',4)->whereMonth('date',date('n')-1)->whereYear('date',date('Y'))->paginate(15);
            }
            
            if(request('filter') ==4){
                $bookings = $query->where('unitStatus',4)->whereMonth('date','<=',date('n'))->whereMonth('date','>=',date('n')-5)->whereYear('date',date('Y'))->paginate(15);
            }
            
        }else{
            $bookings = $query->where('unitStatus',4)->whereMonth('date',date('n'))->whereYear('date',date('Y'))->paginate(15);
        }
        return ['results'=>$bookings];
    }
    public function addReminder(){
        $date=date("Y-m-d",strtotime(request('reminder_date')));
        $clientReminder = ClientReminder::updateOrCreate(['reminder_id' => request('edit_id')],['client_id'=>request('client_id'),'content'=>request('content'),'date'=>$date]);
        if($clientReminder){
              return ['status'=>200,'message'=>'Reminders noted'];
          }else{
            return ['status'=>201,'message'=>'No Reminders to be noted'];
          }
        
    }
    public function listReminders(){
        $reminders = ClientReminder::where('client_id',request('client'))->orderBy('reminder_id','DESC')->get();
        if($reminders){
              return ['status'=>200,'reminders'=>$reminders];
          }else{
            return ['status'=>201,'message'=>'No Reminders to be noted'];
          }
        
    }
    public function getReminders(){

        $reminders = ClientReminder::where('client_id',request('client_id'))->where('reminder_id',request('reminder_id'))
        ->orderBy('reminder_id','DESC')
        ->first();
        if($reminders){
            return ['status'=>200,'reminder'=>$reminders];
          }else{
            return ['status'=>201,'message'=>'No To Do to be noted'];
          }
        
    }
    public function removeReminders(){

        $reminders = ClientReminder::where('client_id',request('client_id'))->where('reminder_id',request('reminder_id'))->delete();
        if($reminders){
            return ['status'=>200,'todo'=>$reminders];
          }else{
            return ['status'=>201,'message'=>'No To Do to be noted'];
          }
        
    }
    public function listTodos(){

        $todos = ClientTodos::where('client_id',request('client'))->orderBy('todo_id','DESC')->get();
        if($todos){
              return ['status'=>200,'todos'=>$todos];
          }else{
            return ['status'=>201,'message'=>'No To Do to be noted'];
          }
        
    }
    public function overview(){
        $clientPaymentsEnic = ClientUnitPayment::whereHas('unit',function($query){
                    $query->where('clientId',request('client'));
                })->where('rateType',2)->first();

        if(isset($clientPaymentsEnic->dayMonday)) {
            $enic = "1";
        } else {
            $enic = '0';
        }
      
        $unitId = 4;
        $allClientPayments = ClientUnitPayment::whereHas('unit',function($query){
                    $query->where('clientId',request('client'));
                })->get();
        $clientPaymentsEnic = $allClientPayments->where('rateType',2)->first();

        $currentMonthFirstDate = date("Y-m-01", strtotime(request('month')));
        $currentMonthLastDate = date("Y-m-t", strtotime(request('month')));
        if(request()->has('unit')){
            $query = Booking::where('unitId',request('unit'))->with('category','staff','unit','shift')->whereIn('unitStatus',[4,1]);
        }else{
            $query = Booking::whereHas('unit',function($query){
                $query->where('clientId',request('client'));
            })->with('category','staff','unit','shift')->whereIn('unitStatus',[4,1]);
        }
        $query = $query->orderBy('date','DESC');
        $bookings = $query->take(20)->get();


        foreach($bookings as $booking){
            $booking->lineTotal = $this->bookLineTotalGet($booking,$allClientPayments);
            $booking->hourlyRate = $this->getHourlyRateBook($booking,$allClientPayments);
            $booking->enic = $this->getEnicColumn($booking,$allClientPayments);
            $booking->noOfHrs = $this->getNoOfHoursColumn($booking,$allClientPayments);
            $booking->ta = $this->getTaColumn($booking,$allClientPayments);
            $booking->timesheet_btn = $this->getTimesheetColumn($booking);
            $booking->profile = $this->profileBtn($booking);
            $booking->unitStatus = $this->getStatusColumn($booking);
        }
        // BOOKINGS
        return ['status'=>200,'bookings'=>$bookings];
       
    }
    public function addTodo(){
        $clientTodos = ClientTodos::updateOrCreate(['todo_id' => request('edit_id')],['client_id'=>request('client_id'),'content'=>request('content')]);
        if($clientTodos){
              return ['status'=>200,'message'=>'Reminders noted'];
          }else{
            return ['status'=>201,'message'=>'No Reminders to be noted'];
          }
        
    }
    public function getTodos(){
        $todo = ClientTodos::where('client_id',request('client_id'))->where('todo_id',request('todo_id'))->first();
        if($todo){
            return ['status'=>200,'todo'=>$todo];
          }else{
            return ['status'=>201,'message'=>'No To Do to be noted'];
          }
        
    }
    public function removeTodos(){

        $todo = ClientTodos::where('client_id',request('client_id'))->where('todo_id',request('todo_id'))->delete();
        if($todo){
            return ['status'=>200,'todo'=>$todo];
          }else{
            return ['status'=>201,'message'=>'No To Do to be noted'];
          }
        
    }
    public function unitBudgetEdit(){
        $date = date( 'Y-m-d' );
        $budget = ClientUnitBudget::firstOrCreate([
            'clientUnitId'=>request('unit_id'),
            'year'=> date('Y',strtotime($date)),
            'month'=> date('m',strtotime($date))
        ]);
        $budget->update(['budget'    => request('amount')]);
        if($budget){
              return ['status'=>200,'message'=>'Reminders noted'];
          }else{
            return ['status'=>201,'message'=>'No Reminders to be noted'];
          }
        
    }
    public function bookLineTotalGet($payment,$allClientPayments){
        $ta=0;  
        $day = strtolower(date('D',strtotime($payment->date)));
        $shiftId = $payment->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId',$payment->categoryId)->where('rateType',1);
        $filteredData = $clientPayment->first();

        $getTaMile = StaffUnitPayment::where('clientUnitId',$payment->unit->clientUnitId)
        ->where('tax_year_id',$filteredData->tax_year_id
        )->where('staffId',$payment->staff->staffId)->first(); 
         
        if($payment->date > '2022-03-31'){
            if($filteredData->taType==1){
                $ta = 0;
            }else if($filteredData->taType==2){
                if(isset($filteredData->taPerMile)){
                $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
                }else{
                $ta = 0;
                }
            }else if($filteredData->taType==3){
                if(isset($getTaMile->invoiceTaEditable)){
                $ta = $getTaMile->invoiceTaEditable;
                }else{
                    if(isset($getTaMile)){
                        $ta = $getTaMile->invoiceTa;
                    }    
                }
            }     
        }else{
                $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
        }

        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId',$payment->categoryId)->where('rateType',2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
            }

        if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day".$selectColumn;
        } else if($shiftId == 5) {
          $columnName = "twilight_".strtolower($selectColumn);
      } else {
            $columnName = "night".$selectColumn;
        }

        $payAmountPerHr = $filteredData[$columnName];
        if($payment->date=="2019-04-18" && $shiftId == 4){
          $payAmountPerHr = $payAmountPerHr*1.5;
        }
        if($payment->date=="2019-04-19"){
          $payAmountPerHr = $payAmountPerHr*1.5;
        }
        if($payment->date=="2019-04-21" && $shiftId == 4){
          $payAmountPerHr = $payAmountPerHr*1.5;
        }
        if($payment->date=="2019-04-22"){
          $payAmountPerHr = $payAmountPerHr*1.5;
        }
        $clientPaymentsEnicRate = $filteredDataEnic[$columnName];

        $schedule = ClientUnitSchedule::where('clientUnitId',$payment->unitId)->where('staffCategoryId',$payment->categoryId)->where('shiftId',$payment->shiftId)->first();
        $inTotal = 0;
        if(isset($schedule)){
            $totalHoursUnit = $payment->timesheet->unitHours;
            //$totalHoursUnit = $schedule->totalHoursUnit;
            
            $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate)+$ta;
        }
        return $payment['lineTotal'] = number_format($inTotal,2);
    }
    public function getStatusColumn($booking){
        $html = '';
      switch ($booking->unitStatus) {
        case '2': 
          $html .= "<a href='#' class='cancel'>Cancelled</a>";
          
          break;
        case '4':
          $html .= "<a href='#' class='confrimd'>Confirmed</a> ";
          
          break;
        case '1':
          $html .= "<a href='#' class='confrimd'>Confirmed</a> ";
          
          break;
      }
      return $html;
    }
    public function profileBtn($booking){
        if($booking->staffId){
          if($booking->unitStatus == 2 || $booking->unitStatus == 3) { // if cancelled or unable to cover
            $html = "<a href='javascript:void(0)' class='profile'>No Profile</a>";
          } else {
            if($booking->unit->clientId == 29){
              $html = "<a target='_blank' href='".s3_url('staff/sw_profile/'.$booking->staff->sw_profile)."' class='profile'>Profile></a>";
            }else{
              $html = "<a target='_blank' href='".asset('storage/app/staff/staff_profile/'.$booking->staff->profileDocumentFile)."' class='profile'>Profile</a>";
            }
          }        
        }else{
          $profile = "No";
          $html = "<a href='javascript:void(0)' class='profile' >".$profile."</a>";
        }
        return $html;
    } 
    public function getTimesheetColumn($payment){
        if(isset($payment->timesheet->image) && !empty($payment->timesheet->image)){
            return  "<a href='".$payment->timesheet->image_url."' class='btn btn-success btn-xs mrs ' target='_blank'><span>View</span></a>"; 
        }else{
            return  "<a href='javascript:void(0)' class='btn btn-danger btn-xs mrs' style='margin: 0 5px;'><span>Waiting</span></a>"; 
        }
    }
    public function getTaColumn($payment,$allClientPayments){
        $ta = 0;
        $clientPayment  = $allClientPayments->where('staffCategoryId',$payment->categoryId)->where('rateType',1);        
        $filteredData   = $clientPayment->first();                
        $getTaMile = StaffUnitPayment::where('clientUnitId',$payment->unit->clientUnitId)
        ->where('tax_year_id',$filteredData->tax_year_id
        )->where('staffId',$payment->staff->staffId)->first();     
        if($payment->date > '2022-03-31'){
        if($filteredData->taType==1){
            
          }else if($filteredData->taType==2){
            if(isset($filteredData->taPerMile)){
              $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
            }else{
              $ta = 0;
            }
          }else if($filteredData->taType==3){
            if(isset($getTaMile->invoiceTaEditable)){
              $ta = $getTaMile->invoiceTaEditable;
            }else{
                if(isset($getTaMile->invoiceTa)){
                    $ta = $getTaMile->invoiceTa;
                }
            }
          }            
        }else{
            $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
        }
        return "£ ". number_format($ta,2);
    } 
    public function getNoOfHoursColumn($payment,$allClientPayments){
        $shiftId = $payment->shift->shiftId;
        $schedule = ClientUnitSchedule::where('clientUnitId',$payment->unitId)->where('staffCategoryId',$payment->categoryId)->where('shiftId',$shiftId)->first();
        $totalHoursUnit = 0;
        //print_r($payment->timesheet->unitHours); exit;
        if(isset($schedule)){
            $totalHoursUnit = $payment->timesheet->unitHours;
            //$totalHoursUnit = $schedule->totalHoursUnit;
        }
        
        return number_format($totalHoursUnit,2);
    }
    public function getHourlyRateBook($payment,$allClientPayments){
        $day = strtolower(date('D',strtotime($payment->date)));
            $shiftId = $payment->shift->shiftId;
            $clientPayment = $allClientPayments->where('staffCategoryId',$payment->category->categoryId)->where('rateType',1);
            $filteredData = $clientPayment->first();

            switch ($day) {
                case "mon":
                    $selectColumn = "Monday";
                    break;
                case "tue":
                    $selectColumn = "Tuesday";
                    break;
                case "wed":
                    $selectColumn = "Wednesday";
                    break;
                case "thu":
                    $selectColumn = "Thursday";
                    break;
                case "fri":
                    $selectColumn = "Friday";
                    break;
                case "sat":
                    $selectColumn = "Saturday";
                    break;
                case "sun":
                    $selectColumn = "Sunday";
                    break;
                }

            if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
                $columnName = "day".$selectColumn;
            } else if($shiftId == 5) {
                $columnName = "twilight_".strtolower($selectColumn);
            } else {
                $columnName = "night".$selectColumn;
            }

            $payAmountPerHr = $filteredData[$columnName];
            if($payment->date=="2019-04-18" && $shiftId == 4){
              $payAmountPerHr = $payAmountPerHr*1.5;
            }
            if($payment->date=="2019-04-19"){
              $payAmountPerHr = $payAmountPerHr*1.5;
            }
            if($payment->date=="2019-04-21" && $shiftId == 4){
              $payAmountPerHr = $payAmountPerHr*1.5;
            }
            if($payment->date=="2019-04-22"){
              $payAmountPerHr = $payAmountPerHr*1.5;
            }

            return $payment['hourlyRate'] = "£ ". number_format(($payAmountPerHr),2);
    } 
    public function getEnicColumn($payment,$allClientPayments){
        $day = strtolower(date('D',strtotime($payment->date)));
            $shiftId = $payment->shift->shiftId;

            $clientPaymentsEnic = $allClientPayments->where('staffCategoryId',$payment->category->categoryId)->where('rateType',2);
            $filteredDataEnic = $clientPaymentsEnic->first();

            switch ($day) {
                case "mon":
                    $selectColumn = "Monday";
                    break;
                case "tue":
                    $selectColumn = "Tuesday";
                    break;
                case "wed":
                    $selectColumn = "Wednesday";
                    break;
                case "thu":
                    $selectColumn = "Thursday";
                    break;
                case "fri":
                    $selectColumn = "Friday";
                    break;
                case "sat":
                    $selectColumn = "Saturday";
                    break;
                case "sun":
                    $selectColumn = "Sunday";
                    break;
                }

            if($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
                $columnName = "day".$selectColumn;
            } else {
                $columnName = "night".$selectColumn;
            }
            $clientPaymentsEnicRate = $filteredDataEnic[$columnName];

            return $payment['enic'] = "£ ". number_format($clientPaymentsEnicRate,2);
    }
    public function changePassword(){
        if(request('new_password')==request('confirm_password')){
            $client = Client::where('clientId',request('client'))->first();
                if(Hash::check(request('current_password'), $client->password)){
                  $passwordChange = Client::where('clientId', $client->clientId)->update(['password' => Hash::make(request('new_password'))]);
                    return ['status'=>200,'message'=>'Password Changed Successfully'];
                }else{
                  return ['status'=>201,'message'=>'Your Current Password is incorrect'];
                }
        }else{
            return ['status'=>201,'message'=>'New Password and Confirm Password Doesnot match'];
        }
    
}
}