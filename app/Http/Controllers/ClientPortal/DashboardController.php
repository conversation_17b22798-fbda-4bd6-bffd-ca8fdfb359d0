<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Client;
use App\Models\ClientReminder;
use App\Models\ClientTodos;
use App\Models\ClientUnit;
use App\Models\ClientUnitBudget;
use App\Models\ClientUnitPayment;
use App\Models\ClientUnitSchedule;
use App\Models\Holiday;
use App\Models\StaffUnitPayment;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use Carbon\Carbon;
use App\Models\TaxYear;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
class DashboardController extends Controller
{
   public function index(Request $request)
   {
    $user = Auth::user();
    
    // Check if user is authenticated
    if (!$user) {
        Log::warning('Unauthenticated user trying to access dashboard');
        return redirect()->route('login')->with('error', 'Please log in to access the dashboard.');
    }
    
    // Check if user has portal_id
    if (!$user->portal_id) {
        Log::warning('User has no portal_id', ['user_id' => $user->id]);
        return redirect()->route('login')->with('error', 'Your account is not properly configured. Please contact administrator.');
    }
    
    $clientId = $user->portal_id;
    
    Log::info('Dashboard access', [
        'user_id' => $user->id,
        'client_id' => $clientId,
        'portal_type' => $user->portal_type
    ]);
    
    // Get units for the logged in client
    $units = $this->getUnitsForClient($clientId);
    
    // Get financial years for selected units (if any unit is selected)
    $selectedUnitId = $request->input('unit_id');
    $selectedFinancialYear = $request->input('financial_year');
    $financialYears = collect(); // Initialize as empty collection
    
    if ($selectedUnitId) {
        $financialYears = $this->getFinancialYearsForUnit($selectedUnitId);
    }
    
    // Get dashboard data based on filters with error handling
    try {
        $dashboardData = $this->getDashboardData($clientId, $selectedUnitId, $selectedFinancialYear);
        
        // Debug: Log the dashboard data to see what's being passed
        Log::info('Dashboard Data for Debug:', [
            'client_id' => $clientId,
            'selected_unit_id' => $selectedUnitId,
            'selected_financial_year' => $selectedFinancialYear,
            'dashboard_data' => $dashboardData
        ]);
        
    } catch (\Exception $e) {
        // Log the error and provide default data
        Log::error('Dashboard data error: ' . $e->getMessage(), [
            'user_id' => $user->id,
            'client_id' => $clientId,
            'selected_unit_id' => $selectedUnitId,
            'selected_financial_year' => $selectedFinancialYear,
            'trace' => $e->getTraceAsString()
        ]);
        $dashboardData = $this->getDefaultDashboardData();
    }
    
    return view('dashboard.main', compact('units', 'financialYears', 'selectedUnitId', 'selectedFinancialYear', 'dashboardData'));
   }
   
   /**
    * Get units for the logged in client
    */
   private function getUnitsForClient($clientId)
   {        
       $user = Auth::user();
       
       if (!$user) {
           Log::warning('No authenticated user found in getUnitsForClient');
           return collect(); // Return empty collection if no user
       }
       
       if (!$clientId) {
           Log::warning('No client ID provided in getUnitsForClient', ['user_id' => $user->id]);
           return collect();
       }
       
       try {
           // Check if user belongs to a specific unit
           if ($user->portal_type === 'App\\Models\\ClientUnit') {
               // User belongs to a specific unit, show only that unit
               Log::info('User belongs to specific unit', ['user_id' => $user->id, 'portal_id' => $user->portal_id]);
               return ClientUnit::where('clientUnitId', $user->portal_id)
                                ->where('status', 1)
                                ->get(['clientUnitId', 'name']);
           } else {
               // User is a client admin, show all units for the client
               Log::info('User is client admin', ['user_id' => $user->id, 'client_id' => $clientId]);
               return ClientUnit::where('clientId', $clientId)
                                ->where('status', 1)
                                ->get(['clientUnitId', 'name']);
           }
       } catch (\Exception $e) {
           Log::error('Error in getUnitsForClient: ' . $e->getMessage(), [
               'user_id' => $user->id,
               'client_id' => $clientId,
               'user_portal_type' => $user->portal_type,
               'user_portal_id' => $user->portal_id
           ]);
           return collect();
       }
   }
   
   /**
    * Get financial years for a specific unit
    */
   private function getFinancialYearsForUnit($unitId)
   {
       return ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                                   ->orderBy('financial_year', 'desc')
                                   ->pluck('financial_year')
                                   ->unique()
                                   ->values();
   }
   
   /**
    * AJAX endpoint to get financial years for selected unit
    */
   public function getFinancialYears(Request $request)
   {
       $request->validate([
           'unit_id' => 'required|integer|exists:client_units,clientUnitId'
       ]);
       
       $unitId = $request->input('unit_id');
       
       // Verify user has access to this unit
       $user = Auth::user();
       if (!$this->userHasAccessToUnit($user, $unitId)) {
           return response()->json(['error' => 'Unauthorized access to unit'], 403);
       }
       
       $financialYears = $this->getFinancialYearsForUnit($unitId);
       
       return response()->json(['financial_years' => $financialYears]);
   }
   
   /**
    * Get filtered dashboard data based on unit and financial year
    */
   public function getFilteredData(Request $request)
   {
       $request->validate([
           'unit_id' => 'nullable|integer|exists:client_units,clientUnitId',
           'financial_year' => 'nullable|integer|min:2000|max:2099'
       ]);
       
       $user = Auth::user();
       $clientId = $user->portal_id;
       $unitId = $request->input('unit_id');
       $financialYear = $request->input('financial_year');
       
       // Verify user has access to this unit if specified
       if ($unitId && !$this->userHasAccessToUnit($user, $unitId)) {
           return response()->json(['error' => 'Unauthorized access to unit'], 403);
       }
       
       $data = [
           'status' => 200,
           'message' => 'Data filtered successfully',
           'filters' => [
               'unit_id' => $unitId,
               'financial_year' => $financialYear
           ]
       ];
       
       // Add logic here to fetch filtered data based on unit and financial year
       // This is where you would add queries to get budgets, expenses, etc.
       
       return response()->json($data);
   }
   
   /**
    * Check if user has access to a specific unit
    */
   private function userHasAccessToUnit($user, $unitId)
   {
       if (!$user) {
           return false;
       }
       
       // If user belongs to a specific unit, they can only access that unit
       if ($user->portal_type === 'App\\Models\\ClientUnit') {
           return $user->portal_id == $unitId;
       } else {
           // If user is a client admin, check if unit belongs to their client
           $unit = ClientUnit::find($unitId);
           return $unit && $unit->clientId == $user->portal_id;
       }
   }
   
   /**
    * Get dashboard data based on filters
    */
   private function getDashboardData($clientId, $unitId = null, $financialYear = null)
   {
       // Validate inputs
       if (!$clientId) {
           Log::warning('No client ID provided to getDashboardData');
           return $this->getDefaultDashboardData();
       }
       
       // Get units for data calculation
       $unitsForData = [];
       if ($unitId) {
           // Verify the unit exists and belongs to the client
           $unit = ClientUnit::where('clientUnitId', $unitId)
                             ->where('clientId', $clientId)
                             ->first();
           if ($unit) {
               $unitsForData = [$unitId];
           } else {
               Log::warning('Unit not found or does not belong to client', [
                   'unit_id' => $unitId,
                   'client_id' => $clientId
               ]);
           }
       } else {
           $units = $this->getUnitsForClient($clientId);
           $unitsForData = $units && $units->count() > 0 ? $units->pluck('clientUnitId')->toArray() : [];
       }
       
       if (empty($unitsForData)) {
           Log::info('No units found for data calculation', [
               'client_id' => $clientId,
               'unit_id' => $unitId
           ]);
           return $this->getDefaultDashboardData();
       }
       
       Log::info('Calculating dashboard data', [
           'client_id' => $clientId,
           'units_for_data' => $unitsForData,
           'financial_year' => $financialYear
       ]);
       
       // Calculate budget data
       $budgetData = $this->calculateBudgetData($unitsForData, $financialYear);
       
       // Calculate expense data
       $expenseData = $this->calculateExpenseData($unitsForData, $financialYear);
       
       // Calculate balance from ClientUnitWeeklyBudget
       $balanceData = $this->calculateBalanceData($unitsForData, $financialYear);
       
       // Calculate booking reasons
       $bookingReasons = $this->calculateBookingReasons($unitsForData, $financialYear);
       
       $budgetTotal = isset($budgetData['total']) && is_numeric($budgetData['total']) ? (float)$budgetData['total'] : 0;
       $expenseTotal = isset($expenseData['total']) && is_numeric($expenseData['total']) ? (float)$expenseData['total'] : 0;
       $balanceTotal = isset($balanceData['total']) && is_numeric($balanceData['total']) ? (float)$balanceData['total'] : 0;
       
       $result = [
           'budget' => $budgetData,
           'expense' => $expenseData,
           'balance' => $balanceTotal,
           'balance_data' => $balanceData,
           'expense_percentage' => $expenseData['percentage'] ?? 0,
           'balance_percentage' => 0, // As requested
           'booking_reasons' => $bookingReasons,
           'units_count' => count($unitsForData),
           'selected_unit_id' => $unitId,
           'selected_financial_year' => $financialYear
       ];
       
       Log::info('Dashboard data calculated successfully', [
           'budget_total' => $budgetTotal,
           'expense_total' => $expenseTotal,
           'balance_total' => $balanceTotal
       ]);
       
       return $result;
   }
   
   /**
    * Calculate budget data for units
    */
   private function calculateBudgetData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral',
               'by_unit' => []
           ];
       }
       
       // Get annual budget from ClientUnitAnnualBudget
       $annualBudgetQuery = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds);
       
       if ($financialYear) {
           $annualBudgetQuery->where('financial_year', $financialYear);
       } else {
           // If no financial year specified, get the most recent one for these units
           $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                              ->orderBy('financial_year', 'desc')
                                              ->value('financial_year');
           if ($latestYear) {
               $annualBudgetQuery->where('financial_year', $latestYear);
           }
       }
       
       $annualBudgets = $annualBudgetQuery->get();
       $total = (float)($annualBudgets->sum('annual_budget') ?? 0);
       
       Log::info('Budget calculation debug', [
           'unit_ids' => $unitIds,
           'financial_year' => $financialYear,
           'annual_budgets_count' => $annualBudgets->count(),
           'total_budget' => $total,
           'annual_budgets' => $annualBudgets->toArray()
       ]);
       
       // For monthly comparison, we'll use a simple approach
       // You can enhance this with actual monthly data if available
       $currentMonth = $total / 12; // Monthly average
       $lastMonth = $currentMonth; // Placeholder - implement actual logic if needed
       
       $percentage = 0; // Since we don't have historical annual budget data
       
       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => 0,
           'trend' => 'neutral',
           'by_unit' => $this->getBudgetByUnit($unitIds, $financialYear)
       ];
   }
   
   /**
    * Calculate expense data for units
    */
   private function calculateExpenseData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get annual budget IDs for the units
       $annualBudgetIds = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
           ->when($financialYear, function($query) use ($financialYear) {
               return $query->where('financial_year', $financialYear);
           }, function($query) use ($unitIds) {
               // Get the most recent financial year for these units
               $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   return $query->where('financial_year', $latestYear);
               }
               return $query;
           })
           ->pluck('id');
       
       if ($annualBudgetIds->isEmpty()) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get weekly budgets and sum total_weekly_utilisation
       $weeklyBudgets = ClientUnitWeeklyBudget::whereIn('client_unit_annual_budget_id', $annualBudgetIds)->get();
       
       $total = (float)($weeklyBudgets->sum('total_weekly_utilisation') ?? 0);
       $totalAllocation = (float)($weeklyBudgets->sum('total_weekly_allocation') ?? 0);
       
       // Calculate percentage (expenses vs allocation)
       $percentage = ($totalAllocation > 0 && is_numeric($totalAllocation) && is_numeric($total)) 
                     ? ($total / $totalAllocation) * 100 
                     : 0;
       
       // For monthly comparison, we'll use a simple approach
       $currentMonth = $total / 52; // Weekly average as monthly placeholder
       $lastMonth = $currentMonth; // Placeholder
       
       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => is_numeric($percentage) ? round((float)$percentage, 1) : 0,
           'trend' => 'neutral', // Can be enhanced with historical data
           'allocation_total' => $totalAllocation
       ];
   }
   
   /**
    * Calculate balance data for units from ClientUnitWeeklyBudget
    */
   private function calculateBalanceData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get annual budget IDs for the units
       $annualBudgetIds = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
           ->when($financialYear, function($query) use ($financialYear) {
               return $query->where('financial_year', $financialYear);
           }, function($query) use ($unitIds) {
               // Get the most recent financial year for these units
               $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   return $query->where('financial_year', $latestYear);
               }
               return $query;
           })
           ->pluck('id');
       
       if ($annualBudgetIds->isEmpty()) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get weekly budgets and sum balance_fund
       $weeklyBudgets = ClientUnitWeeklyBudget::whereIn('client_unit_annual_budget_id', $annualBudgetIds)->get();
       
       $total = (float)($weeklyBudgets->sum('balance_fund') ?? 0);
       
       // For monthly comparison, we'll use a simple approach
       $currentMonth = $total / 52; // Weekly average as monthly placeholder
       $lastMonth = $currentMonth; // Placeholder
       
       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => 0, // As requested
           'trend' => $total >= 0 ? 'positive' : 'negative'
       ];
   }
   
   /**
    * Calculate booking reasons data
    */
   private function calculateBookingReasons($unitIds, $financialYear = null)
   {
       $query = Booking::whereIn('unitId', $unitIds)
                      ->whereIn('unitStatus', [4, 1]);
       
       if ($financialYear) {
           $query->whereYear('date', $financialYear);
       } else {
           $query->whereYear('date', date('Y'));
       }
       
       $bookings = $query->get();
       
       $reasons = [
           'sickness' => ['count' => 0, 'cost' => 0, 'reason_id' => 1],
           'holiday' => ['count' => 0, 'cost' => 0, 'reason_id' => 2],
           'vacant' => ['count' => 0, 'cost' => 0, 'reason_id' => 3],
           'resident_admission' => ['count' => 0, 'cost' => 0, 'reason_id' => 4],
           'one_to_one' => ['count' => 0, 'cost' => 0, 'reason_id' => 5],
           'extra_staff' => ['count' => 0, 'cost' => 0, 'reason_id' => 6],
           'management' => ['count' => 0, 'cost' => 0, 'reason_id' => 7],
           'others' => ['count' => 0, 'cost' => 0, 'reason_id' => null]
       ];
       
       foreach ($bookings as $booking) {
           $reasonKey = $this->getReasonKey($booking->reason);
           if (isset($reasons[$reasonKey])) {
               $reasons[$reasonKey]['count']++;
               $cost = is_numeric($booking->booking_unit_cost) ? (float)$booking->booking_unit_cost : 0;
               $reasons[$reasonKey]['cost'] += $cost;
           }
       }
       
       return $reasons;
   }
   
   /**
    * Get budget breakdown by unit
    */
   private function getBudgetByUnit($unitIds, $financialYear = null)
   {
       $units = ClientUnit::whereIn('clientUnitId', $unitIds)->get();
       $unitBudgets = [];
       
       foreach ($units as $unit) {
           $query = ClientUnitAnnualBudget::where('client_unit_id', $unit->clientUnitId);
           
           if ($financialYear) {
               $query->where('financial_year', $financialYear);
           } else {
               // Get the most recent financial year for this unit
               $latestYear = ClientUnitAnnualBudget::where('client_unit_id', $unit->clientUnitId)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   $query->where('financial_year', $latestYear);
               }
           }
           
           $budget = $query->value('annual_budget');
           $budget = is_numeric($budget) ? (float)$budget : 0;
           
           $unitBudgets[] = [
               'name' => $unit->name,
               'budget' => $budget,
               'unit_id' => $unit->clientUnitId
           ];
       }
       
       return $unitBudgets;
   }
   
   /**
    * Map reason ID to reason key
    */
   private function getReasonKey($reasonId)
   {
       $mapping = [
           1 => 'sickness',
           2 => 'holiday',
           3 => 'vacant',
           4 => 'resident_admission',
           5 => 'one_to_one',
           6 => 'extra_staff',
           7 => 'management',
           null => 'others'
       ];
       
       return $mapping[$reasonId] ?? 'others';
   }
   
   /**
    * Get default dashboard data when there's an error
    */
   private function getDefaultDashboardData()
   {
       return [
           'budget' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral',
               'by_unit' => []
           ],
           'expense' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ],
           'balance' => 0,
           'balance_data' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ],
           'expense_percentage' => 0,
           'balance_percentage' => 0,
           'booking_reasons' => [
               'sickness' => ['count' => 0, 'cost' => 0],
               'holiday' => ['count' => 0, 'cost' => 0],
               'vacant' => ['count' => 0, 'cost' => 0],
               'resident_admission' => ['count' => 0, 'cost' => 0],
               'one_to_one' => ['count' => 0, 'cost' => 0],
               'extra_staff' => ['count' => 0, 'cost' => 0],
               'management' => ['count' => 0, 'cost' => 0],
               'others' => ['count' => 0, 'cost' => 0]
           ],
           'units_count' => 0,
           'selected_unit_id' => null,
           'selected_financial_year' => null
       ];
   }
   
   /**
     * Test method to debug dashboard data (TEMPORARY - REMOVE AFTER DEBUGGING)
     */
    public function testData(Request $request)
    {
        $user = Auth::user();
        $clientId = $user->portal_id;
        
        // Test data retrieval
        $units = $this->getUnitsForClient($clientId);
        
        // Test with first unit if available
        $firstUnit = $units->first();
        if ($firstUnit) {
            $financialYears = $this->getFinancialYearsForUnit($firstUnit->clientUnitId);
            $firstYear = $financialYears->first();
            
            // Get test data
            $testData = $this->getDashboardData($clientId, $firstUnit->clientUnitId, $firstYear);
            
            return response()->json([
                'user' => [
                    'id' => $user->id,
                    'portal_id' => $user->portal_id,
                    'portal_type' => $user->portal_type
                ],
                'client_id' => $clientId,
                'units' => $units,
                'first_unit' => $firstUnit,
                'financial_years' => $financialYears,
                'first_year' => $firstYear,
                'test_data' => $testData,
                'raw_annual_budgets' => ClientUnitAnnualBudget::where('client_unit_id', $firstUnit->clientUnitId)->get(),
                'raw_weekly_budgets' => ClientUnitWeeklyBudget::whereIn('client_unit_annual_budget_id', 
                    ClientUnitAnnualBudget::where('client_unit_id', $firstUnit->clientUnitId)->pluck('id'))->get()
            ]);
        }
        
        return response()->json([
            'error' => 'No units found',
            'user' => $user,
            'units' => $units
        ]);
    }
}