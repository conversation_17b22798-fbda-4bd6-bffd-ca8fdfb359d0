<?php

namespace App\Http\Controllers\ClientPortal;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\ClientUnit;
use App\Models\ClientUnitAnnualBudget;
use App\Models\ClientUnitWeeklyBudget;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();

        if (!$user || !$user->portal_id) {
            return redirect()->route('login')->with('error', 'Please log in to access the dashboard.');
        }

        $clientId = $user->portal_id;
        $units = $this->getUnitsForClient($clientId);
        $selectedUnitId = $request->input('unit_id');
        $selectedFinancialYear = $request->input('financial_year');

        $financialYears = collect();
        if ($selectedUnitId) {
            $financialYears = $this->getFinancialYearsForUnit($selectedUnitId);
        }

        $dashboardData = $this->getDashboardData($clientId, $selectedUnitId, $selectedFinancialYear);

        return view('dashboard.main', compact('units', 'financialYears', 'selectedUnitId', 'selectedFinancialYear', 'dashboardData'));
    }
   
    /**
     * Get units for the logged in client
     */
    private function getUnitsForClient($clientId)
    {
        $user = Auth::user();

        if (!$user || !$clientId) {
            return collect();
        }

        if ($user->portal_type === 'App\\Models\\ClientUnit') {
            return ClientUnit::where('clientUnitId', $user->portal_id)
                            ->where('status', 1)
                            ->get(['clientUnitId', 'name']);
        }

        return ClientUnit::where('clientId', $clientId)
                        ->where('status', 1)
                        ->get(['clientUnitId', 'name']);
    }
   
   /**
    * Get financial years for a specific unit
    */
   private function getFinancialYearsForUnit($unitId)
   {
       return ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                                   ->orderBy('financial_year', 'desc')
                                   ->pluck('financial_year')
                                   ->unique()
                                   ->values();
   }
   
   /**
    * AJAX endpoint to get financial years for selected unit
    */
   public function getFinancialYears(Request $request)
   {
       $request->validate([
           'unit_id' => 'required|integer|exists:client_units,clientUnitId'
       ]);
       
       $unitId = $request->input('unit_id');
       
       // Verify user has access to this unit
       $user = Auth::user();
       if (!$this->userHasAccessToUnit($user, $unitId)) {
           return response()->json(['error' => 'Unauthorized access to unit'], 403);
       }
       
       $financialYears = $this->getFinancialYearsForUnit($unitId);
       
       return response()->json(['financial_years' => $financialYears]);
   }
   
    /**
     * Get filtered dashboard data based on unit and financial year
     */
    public function getFilteredData(Request $request)
    {
        $request->validate([
            'unit_id' => 'nullable|integer|exists:client_units,clientUnitId',
            'financial_year' => 'nullable|string'
        ]);

        $user = Auth::user();
        $unitId = $request->input('unit_id');
        $financialYear = $request->input('financial_year');

        if ($unitId && !$this->userHasAccessToUnit($user, $unitId)) {
            return response()->json(['error' => 'Unauthorized access to unit'], 403);
        }

        $dashboardData = $this->getDashboardData($user->portal_id, $unitId, $financialYear);

        return response()->json([
            'status' => 200,
            'message' => 'Data filtered successfully',
            'filters' => [
                'unit_id' => $unitId,
                'financial_year' => $financialYear
            ],
            'data' => $dashboardData
        ]);
    }
   
   /**
    * Check if user has access to a specific unit
    */
   private function userHasAccessToUnit($user, $unitId)
   {
       if (!$user) {
           return false;
       }
       
       // If user belongs to a specific unit, they can only access that unit
       if ($user->portal_type === 'App\\Models\\ClientUnit') {
           return $user->portal_id == $unitId;
       } else {
           // If user is a client admin, check if unit belongs to their client
           $unit = ClientUnit::find($unitId);
           return $unit && $unit->clientId == $user->portal_id;
       }
   }
   
    /**
     * Get dashboard data based on filters
     */
    private function getDashboardData($clientId, $unitId = null, $financialYear = null)
    {
        if (!$clientId) {
            return $this->getDefaultDashboardData();
        }

        $unitsForData = [];
        if ($unitId) {
            $unit = ClientUnit::where('clientUnitId', $unitId)
                            ->where('clientId', $clientId)
                            ->first();
            if ($unit) {
                $unitsForData = [$unitId];
            }
        } else {
            $units = $this->getUnitsForClient($clientId);
            $unitsForData = $units && $units->count() > 0 ? $units->pluck('clientUnitId')->toArray() : [];
        }

        if (empty($unitsForData)) {
            return $this->getDefaultDashboardData();
        }

        $budgetData = $this->calculateBudgetData($unitsForData, $financialYear);
        $expenseData = $this->calculateExpenseData($unitsForData, $financialYear);
        $balanceData = $this->calculateBalanceData($unitsForData, $financialYear);
        $bookingReasons = $this->calculateBookingReasons($unitsForData, $financialYear);

        return [
            'budget' => $budgetData,
            'expense' => $expenseData,
            'balance' => $balanceData['total'] ?? 0,
            'balance_data' => $balanceData,
            'expense_percentage' => $expenseData['percentage'] ?? 0,
            'balance_percentage' => 0,
            'booking_reasons' => $bookingReasons,
            'units_count' => count($unitsForData),
            'selected_unit_id' => $unitId,
            'selected_financial_year' => $financialYear
        ];
    }
   
   /**
    * Calculate budget data for units
    */
   private function calculateBudgetData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral',
               'by_unit' => []
           ];
       }
       
       // Get annual budget from ClientUnitAnnualBudget
       $annualBudgetQuery = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds);
       
       if ($financialYear) {
           $annualBudgetQuery->where('financial_year', $financialYear);
       } else {
           // If no financial year specified, get the most recent one for these units
           $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                              ->orderBy('financial_year', 'desc')
                                              ->value('financial_year');
           if ($latestYear) {
               $annualBudgetQuery->where('financial_year', $latestYear);
           }
       }
       
       $annualBudgets = $annualBudgetQuery->get();

       $total = (float)($annualBudgets->sum('annual_budget') ?? 0);
       $currentMonth = $total / 12;

       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => 0,
           'trend' => 'neutral',
           'by_unit' => $this->getBudgetByUnit($unitIds, $financialYear)
       ];
   }
   
   /**
    * Calculate expense data for units
    */
   private function calculateExpenseData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get annual budget IDs for the units
       $annualBudgetIds = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
           ->when($financialYear, function($query) use ($financialYear) {
               return $query->where('financial_year', $financialYear);
           }, function($query) use ($unitIds) {
               // Get the most recent financial year for these units
               $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   return $query->where('financial_year', $latestYear);
               }
               return $query;
           })
           ->pluck('id');
       
       if ($annualBudgetIds->isEmpty()) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get weekly budgets and sum total_weekly_utilisation
       $weeklyBudgets = ClientUnitWeeklyBudget::whereIn('client_unit_annual_budget_id', $annualBudgetIds)->get();
       
       $total = (float)($weeklyBudgets->sum('total_weekly_utilisation') ?? 0);
       $totalAllocation = (float)($weeklyBudgets->sum('total_weekly_allocation') ?? 0);

       $percentage = ($totalAllocation > 0 && is_numeric($totalAllocation) && is_numeric($total))
                     ? ($total / $totalAllocation) * 100
                     : 0;

       $currentMonth = $total / 52;

       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => is_numeric($percentage) ? round((float)$percentage, 1) : 0,
           'trend' => 'neutral',
           'allocation_total' => $totalAllocation
       ];
   }
   
   /**
    * Calculate balance data for units from ClientUnitWeeklyBudget
    */
   private function calculateBalanceData($unitIds, $financialYear = null)
   {
       if (empty($unitIds)) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       // Get annual budget IDs for the units
       $annualBudgetIds = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
           ->when($financialYear, function($query) use ($financialYear) {
               return $query->where('financial_year', $financialYear);
           }, function($query) use ($unitIds) {
               // Get the most recent financial year for these units
               $latestYear = ClientUnitAnnualBudget::whereIn('client_unit_id', $unitIds)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   return $query->where('financial_year', $latestYear);
               }
               return $query;
           })
           ->pluck('id');
       
       if ($annualBudgetIds->isEmpty()) {
           return [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ];
       }
       
       $weeklyBudgets = ClientUnitWeeklyBudget::whereIn('client_unit_annual_budget_id', $annualBudgetIds)->get();
       $total = (float)($weeklyBudgets->sum('balance_fund') ?? 0);
       $currentMonth = $total / 52;

       return [
           'total' => $total,
           'current_month' => $currentMonth,
           'percentage' => 0,
           'trend' => $total >= 0 ? 'positive' : 'negative'
       ];
   }
   
   /**
    * Calculate booking reasons data
    */
   private function calculateBookingReasons($unitIds, $financialYear = null)
   {
       $query = Booking::whereIn('unitId', $unitIds)
                      ->whereIn('unitStatus', [4, 1]);
       
       if ($financialYear) {
           $query->whereYear('date', $financialYear);
       } else {
           $query->whereYear('date', date('Y'));
       }
       
       $bookings = $query->get();
       
       $reasons = [
           'sickness' => ['count' => 0, 'cost' => 0, 'reason_id' => 1],
           'holiday' => ['count' => 0, 'cost' => 0, 'reason_id' => 2],
           'vacant' => ['count' => 0, 'cost' => 0, 'reason_id' => 3],
           'resident_admission' => ['count' => 0, 'cost' => 0, 'reason_id' => 4],
           'one_to_one' => ['count' => 0, 'cost' => 0, 'reason_id' => 5],
           'extra_staff' => ['count' => 0, 'cost' => 0, 'reason_id' => 6],
           'management' => ['count' => 0, 'cost' => 0, 'reason_id' => 7],
           'others' => ['count' => 0, 'cost' => 0, 'reason_id' => null]
       ];
       
       foreach ($bookings as $booking) {
           $reasonKey = $this->getReasonKey($booking->reason);
           if (isset($reasons[$reasonKey])) {
               $reasons[$reasonKey]['count']++;
               $cost = is_numeric($booking->booking_unit_cost) ? (float)$booking->booking_unit_cost : 0;
               $reasons[$reasonKey]['cost'] += $cost;
           }
       }
       
       return $reasons;
   }
   
   /**
    * Get budget breakdown by unit
    */
   private function getBudgetByUnit($unitIds, $financialYear = null)
   {
       $units = ClientUnit::whereIn('clientUnitId', $unitIds)->get();
       $unitBudgets = [];
       
       foreach ($units as $unit) {
           $query = ClientUnitAnnualBudget::where('client_unit_id', $unit->clientUnitId);
           
           if ($financialYear) {
               $query->where('financial_year', $financialYear);
           } else {
               // Get the most recent financial year for this unit
               $latestYear = ClientUnitAnnualBudget::where('client_unit_id', $unit->clientUnitId)
                                                  ->orderBy('financial_year', 'desc')
                                                  ->value('financial_year');
               if ($latestYear) {
                   $query->where('financial_year', $latestYear);
               }
           }
           
           $budget = $query->value('annual_budget');
           $budget = is_numeric($budget) ? (float)$budget : 0;
           
           $unitBudgets[] = [
               'name' => $unit->name,
               'budget' => $budget,
               'unit_id' => $unit->clientUnitId
           ];
       }
       
       return $unitBudgets;
   }
   
   /**
    * Map reason ID to reason key
    */
   private function getReasonKey($reasonId)
   {
       $mapping = [
           1 => 'sickness',
           2 => 'holiday',
           3 => 'vacant',
           4 => 'resident_admission',
           5 => 'one_to_one',
           6 => 'extra_staff',
           7 => 'management',
           null => 'others'
       ];
       
       return $mapping[$reasonId] ?? 'others';
   }
   
   /**
    * Get default dashboard data when there's an error
    */
   private function getDefaultDashboardData()
   {
       return [
           'budget' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral',
               'by_unit' => []
           ],
           'expense' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ],
           'balance' => 0,
           'balance_data' => [
               'total' => 0,
               'current_month' => 0,
               'percentage' => 0,
               'trend' => 'neutral'
           ],
           'expense_percentage' => 0,
           'balance_percentage' => 0,
           'booking_reasons' => [
               'sickness' => ['count' => 0, 'cost' => 0],
               'holiday' => ['count' => 0, 'cost' => 0],
               'vacant' => ['count' => 0, 'cost' => 0],
               'resident_admission' => ['count' => 0, 'cost' => 0],
               'one_to_one' => ['count' => 0, 'cost' => 0],
               'extra_staff' => ['count' => 0, 'cost' => 0],
               'management' => ['count' => 0, 'cost' => 0],
               'others' => ['count' => 0, 'cost' => 0]
           ],
           'units_count' => 0,
           'selected_unit_id' => null,
           'selected_financial_year' => null
       ];
    }

    /**
     * Get header card data for budget view based on unit and financial year selection
     */
    public function getBudgetHeaderCardData($unitId = null, $financialYear = null)
    {
        $user = Auth::user();
        if (!$user) {
            return $this->getDefaultHeaderCardData();
        }

        $clientId = $user->portal_id;
        $unitIds = $this->getUnitIdsForCalculation($user, $clientId, $unitId);

        if (empty($unitIds)) {
            return $this->getDefaultHeaderCardData();
        }

        if (!$financialYear) {
            $financialYear = $this->getCurrentFinancialYear();
        }

        $budgetData = $this->calculateBudgetData($unitIds, $financialYear);
        $expenseData = $this->calculateExpenseData($unitIds, $financialYear);
        $balanceData = $this->calculateBalanceData($unitIds, $financialYear);

        return [
            'budget' => $budgetData,
            'expense' => $expenseData,
            'balance' => $balanceData['total'] ?? 0,
            'expense_percentage' => $expenseData['percentage'] ?? 0,
            'balance_percentage' => 0
        ];
    }

    /**
     * Get unit IDs for calculation based on user access and selection
     */
    private function getUnitIdsForCalculation($user, $clientId, $unitId = null)
    {
        if (!$unitId) {
            return ClientUnit::where('clientId', $clientId)->where('status', 1)->pluck('clientUnitId')->toArray();
        }

        if (!$this->userHasAccessToUnit($user, $unitId)) {
            return [];
        }

        return [$unitId];
    }

    /**
     * Get default header card data
     */
    private function getDefaultHeaderCardData()
    {
        return [
            'budget' => ['total' => 0, 'trend' => 'neutral', 'percentage' => 0],
            'expense' => ['total' => 0, 'trend' => 'neutral', 'percentage' => 0],
            'balance' => 0,
            'expense_percentage' => 0,
            'balance_percentage' => 0
        ];
    }

    /**
     * Get current financial year in the format stored in database (e.g., "2025-2026")
     */
    private function getCurrentFinancialYear()
    {
        $now = Carbon::now();
        $year = $now->year;

        // If current date is before April, we're in the previous financial year
        if ($now->month < 4) {
            $year--;
        }

        // Return in the format stored in database
        return $year . '-' . ($year + 1);
    }
}