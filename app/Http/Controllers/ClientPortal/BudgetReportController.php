<?php

namespace App\Http\Controllers\ClientPortal;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\ClientUnitAnnualBudget;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class BudgetReportController extends Controller
{
    public function index(Request $request)
    {
        $user = \Illuminate\Support\Facades\Auth::user();
        if (!$user) {
            // Redirect to login or show an error
            return redirect()->route('login')->withErrors('You must be logged in to view this page.');
        }
        $clientId = $user->portal_id;
        // Get all units belonging to the client
        $units = \App\Models\ClientUnit::where('clientId', $clientId)->where('status', 1)->get();
        // Eager load annual budgets and weekly budgets for each unit
        $units->load(['annualBudgets' => function($q) {
            $q->select('id', 'client_unit_id', 'financial_year');
        }, 'annualBudgets.weeklyBudgets']);

        // Build filterData for frontend dynamic filters
        $filterData = [];
        foreach ($units as $unit) {
            $years = $unit->annualBudgets->pluck('financial_year')->unique()->values();
            $monthsByYear = [];
            $weeksByYearMonth = [];
            foreach ($unit->annualBudgets as $ab) {
                $weeks = $ab->weeklyBudgets;
                $months = $weeks->pluck('week_end_date')->map(function($date) { return (int)date('n', strtotime($date)); })->unique()->values();
                $monthsByYear[$ab->financial_year] = $months;
                foreach ($months as $m) {
                    $weeksByYearMonth[$ab->financial_year][$m] = $weeks->filter(function($w) use ($m) {
                        return (int)date('n', strtotime($w->week_end_date)) === $m;
                    })->values()->map(function($w) {
                        return ['week_number' => $w->week_number, 'label' => 'Week ' . $w->week_number];
                    })->all();
                }
            }
            $filterData[$unit->clientUnitId] = [
                'years' => $years,
                'monthsByYear' => $monthsByYear,
                'weeksByYearMonth' => $weeksByYearMonth
            ];
        }
        $financialYears = \App\Models\ClientUnitAnnualBudget::select('financial_year')->distinct()->pluck('financial_year');
        
        // Set default values for filters
        $defaultUnit = $units->first() ? $units->first()->clientUnitId : null;
        $currentFinancialYear = $this->getCurrentFinancialYear();
        
        $selectedUnit = $request->unit_id ?? $defaultUnit;
        $selectedYear = $request->financial_year ?? $currentFinancialYear;
        $selectedMonth = $request->month; // Default to null (All)
        $selectedWeek = $request->week; // Default to null (All)

        // DEBUG: Log filter values
        \Log::info('BudgetReport Filters', [
            'unit_id' => $selectedUnit,
            'financial_year' => $selectedYear,
            'month' => $selectedMonth,
            'week' => $selectedWeek,
        ]);

        // Fetch weekly budgets for the selected filters
        $weeklyBudgets = collect();
        // Always define $annualBudget, even if no row is found
        $annualBudget = null;
        $annualBudgetRow = null;
        if ($selectedUnit && $selectedYear) {
            $annualBudgetRow = \App\Models\ClientUnitAnnualBudget::where('client_unit_id', $selectedUnit)
                ->where('financial_year', $selectedYear)
                ->first();
            if ($annualBudgetRow) {
                $annualBudget = $annualBudgetRow;
                $query = \App\Models\ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $annualBudget->id);
                if ($selectedMonth) {
                    $query->whereMonth('week_end_date', $selectedMonth);
                }
                if ($selectedWeek) {
                    $query->where('week_number', $selectedWeek);
                }
                $weeklyBudgets = $query->get();
            } else {
                // No annual budget for this unit/year, so no weekly budgets
                $weeklyBudgets = collect();
            }
        } else {
            $weeklyBudgets = collect();
        }

        // Pie chart data: budget vs utilisation
        $budgetTotal = 0;
        $utilisationTotal = 0;
        if ($weeklyBudgets->count() > 0) {
            $budgetTotal = $weeklyBudgets->sum('total_weekly_allocation');
            $utilisationTotal = $weeklyBudgets->sum('total_weekly_utilisation');
        }
        $utilisation = 0;
        if (isset($weeklyBudgets) && $weeklyBudgets->count() > 0) {
            $utilisation = $weeklyBudgets->sum('total_weekly_utilisation');
        }

        // Debug: Log the query and data for troubleshooting
        // \Log::info('Selected Unit:', [$selectedUnit]);
        // \Log::info('Selected Year:', [$selectedYear]);
        // \Log::info('Weekly Budgets:', $weeklyBudgets->toArray());
        // \Log::info('Budget Total:', [$budgetTotal]);
        // \Log::info('Utilisation Total:', [$utilisationTotal]);
        // If no data, show a warning in the view
        $noDataWarning = null;
        if ($selectedUnit && $selectedYear && $weeklyBudgets->count() === 0) {
            $noDataWarning = 'No weekly budget data found for the selected unit and year.';
        }

        // Calculate header card data
        $dashboardData = $this->calculateHeaderCardData($selectedUnit, $selectedYear, $weeklyBudgets, $annualBudgetRow);

        return view('clientPortal.budgetView', [
            'units' => $units,
            'financialYears' => $financialYears,
            'weeklyBudgets' => $weeklyBudgets,
            'annualBudget' => $annualBudget,
            'utilisation' => $utilisation,
            'selectedUnit' => $selectedUnit,
            'selectedYear' => $selectedYear,
            'selectedMonth' => $selectedMonth,
            'selectedWeek' => $selectedWeek,
            'filterData' => $filterData,
            'budgetTotal' => $budgetTotal,
            'utilisationTotal' => $utilisationTotal,
            'noDataWarning' => $noDataWarning,
            'dashboardData' => $dashboardData,
        ]);
    }

    private function getCurrentFinancialYear()
    {
        $now = Carbon::now();
        $year = $now->year;

        // If current date is before April, we're in the previous financial year
        if ($now->month < 4) {
            $year--;
        }

        return $year;
    }

    /**
     * Calculate header card data based on unit and financial year selection
     */
    private function calculateHeaderCardData($selectedUnit, $selectedYear, $weeklyBudgets, $annualBudgetRow)
    {
        // Initialize default values
        $budgetTotal = 0;
        $expensesTotal = 0;
        $balanceTotal = 0;
        $expensesPercentage = 0;
        $balancePercentage = 0; // Always 0 as requested

        if ($selectedUnit && $selectedYear) {
            // Budget: Get from ClientUnitAnnualBudget based on financial_year
            if ($annualBudgetRow) {
                $budgetTotal = (float)($annualBudgetRow->annual_budget ?? 0);
            }

            // Expenses: Sum of all week's total_weekly_utilisation for the selected year
            $expensesTotal = (float)($weeklyBudgets->sum('total_weekly_utilisation') ?? 0);

            // Balance: Sum of all week's balance_fund for the selected year
            $balanceTotal = (float)($weeklyBudgets->sum('balance_fund') ?? 0);

            // Expenses Percentage: Calculate percentage of expenses vs budget
            if ($budgetTotal > 0) {
                $totalAllocation = (float)($weeklyBudgets->sum('total_weekly_allocation') ?? 0);
                if ($totalAllocation > 0) {
                    $expensesPercentage = round(($expensesTotal / $totalAllocation) * 100, 1);
                }
            }
        }

        return [
            'budget' => [
                'total' => $budgetTotal,
                'trend' => 'neutral',
                'percentage' => 0
            ],
            'expense' => [
                'total' => $expensesTotal,
                'trend' => 'neutral',
                'percentage' => $expensesPercentage
            ],
            'balance' => $balanceTotal,
            'expense_percentage' => $expensesPercentage,
            'balance_percentage' => $balancePercentage
        ];
    }

    // Add more methods as needed for AJAX/chart data
}
