<?php

namespace App\Http\Controllers\Unitportal;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Events\NewBookingCreatedEventFromAllPortal;
use App\Models\Booking;
use App\Models\ClientUnitContact;
use App\Models\ClientUnitSchedule;
use App\Models\Shift;
use App\Models\StaffCategory;
use App\Models\ClientUnitPayment;
use App\Http\Controllers\UnitPortal\UnitHelper;
use App\Models\BookingLog;
use App\Models\Quotation;
use App\Models\TaxWeek;
use App\Models\TaxYear;
use App\Models\ClientUnit;
use App\Models\ClientUnitPortal;
use App\Models\BookingNotification;
use App\Models\ClientUnitLogin;
use Illuminate\Support\Carbon;
use \Swift_Mailer;
use \Swift_SmtpTransport as SmtpTransport;
use App\Mail\SendPlainSMS as SendPlainSMS;
use App\Mail\Shifttimeamend as Shifttimeamend;
use App\Models\BookingWorkflow;
use App\Models\Client;
use App\Models\ClientUnitChat;
use App\Models\Staff;
use App\Models\ClientUnitAnnualBudget;
use App\Models\StaffUnitPayment;
use App\Models\Timesheet;
use Illuminate\Support\Facades\Mail;
use App\Services\UnitHelperService;
use Cache;
use Cookie;
use App\Models\Workflow;
use Illuminate\Support\Facades\Auth;
use App\Models\ClientUnitWeeklyBudget;
use App\Models\WeeklyUtilisedFundLog;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class BookingController extends Controller
{
    public function __construct()
    {
        if (!Auth::check()) {
            return redirect()->route('login');
        }
    }


    // public function list(Request $request)
    // {
    //     $unitHelper = app(UnitHelperService::class);
    //     $events = $unitHelper->getComingEvents();
    //     $loggedInClientId = Auth::user()->portal_id;
    
    //     if (Auth::user()->userable_type == 'App\Models\Client') {
    //         $units = ClientUnit::where('status', 1)->whereIn('clientId', function ($type) use ($loggedInClientId) {
    //             $type->select('clientId')
    //                 ->from('clients')
    //                 ->whereNotIn('clientId', [16, 37, 44, 25])
    //                 ->where('clientId', $loggedInClientId);
    //         })->get();
    //     } else {
    //         $units = ClientUnit::where('status', 1)->where('clientUnitId', $loggedInClientId)->get();
    //     }
    
    //     $unitsdropdown = ClientUnit::where('status', 1)
    //         ->where('clientId', $loggedInClientId)
    //         ->get();
    
    //     $selectedUnitId = $request->input('unitId');
    //     $shifts = Shift::whereNotIn('shiftId', [6])->get();
    //     $categories = StaffCategory::whereIn('categoryId', [1, 2, 3])->get();
    
    //     $query = Booking::with([
    //             'category',
    //             'shift',
    //             'unit',
    //             'workflowApprovers.role',
    //             'workflow',
    //             'bookingWorkflowStatus'
    //         ])
    //         ->orderBy('date', 'ASC')           // ✅ changed to match first controller
    //         ->orderBy('unitStatus', 'DESC')    // ✅ changed to match first controller
    //         ->orderBy('shiftId', 'ASC');       // ✅ changed to match first controller
    
    //     if (Auth::user()->userable_type == 'App\Models\Client') {
    //         $query->whereIn('unitId', function ($type) use ($loggedInClientId) {
    //             $type->select('clientUnitId')
    //                 ->from('client_units')
    //                 ->whereNotIn('clientId', [16, 37, 44, 25])
    //                 ->where('clientId', $loggedInClientId);
    //         });
    //     } else {
    //         $query->where('unitId', $loggedInClientId);
    //     }
    
    //     // ✅ status filters merged from first controller
    //     $currentDateTime = Carbon::now()->format('Y-m-d H:i:s');
    //     if (request()->has('status') && !empty(request('status'))) {
    //         if (request('status') == 7) {
    //             $query->whereIn('unitStatus', [0, 1, 2, 3, 4, 5, 6]);
    //         } elseif (request('status') == 8) {
    //             $query->whereRaw(
    //                 "STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < ?",
    //                 [$currentDateTime]
    //             )
    //             ->whereIn('unitStatus', [1, 4])
    //             ->where('staffStatus', 3);
    //         } elseif (request('status') == 9) {
    //             $query->whereHas('timesheet', function ($q) {
    //                 $q->whereNotNull('image');
    //             });
    //         } elseif (request('status') == 10) {
    //             $query->whereHas('timesheet', function ($q) {
    //                 $q->where('status', 2);
    //             });
    //         } else {
    //             $query->where('unitStatus', request('status'));
    //         }
    //     } else {
    //         $query->whereIn('unitStatus', [4, 1]);
    //     }
    
    //     // ✅ date filters merged from first controller
    //     if (request()->has('date')) {
    //         $dates = explode(" - ", request('date'));
    //         $query->whereBetween('date', [
    //             date('Y-m-d', strtotime($dates[0])),
    //             date('Y-m-d', strtotime($dates[1]))
    //         ]);
    //     } else {
    //         $query->whereBetween('date', [
    //             date('Y-m-d'),
    //             date('Y-m-d', strtotime('+15 days'))
    //         ]);
    //     }
    
    //     if ($selectedUnitId) {
    //         $query->where('unitId', $selectedUnitId);
    //     }
    
    //     // ✅ changed from take(30) to paginate to match first controller
    //     if (request('per_page')) {
    //         $bookings = $query->paginate(request('per_page'));
    //     } else {
    //         $bookings = $query->paginate(15);
    //     }
    
    //     // keep the contact handling from second controller
    //     $contactMailId = '';
    //     foreach ($bookings as $payment) {
    //         $contact = '';
    //         if (!empty($payment->requestedBy)) {
    //             if (is_numeric($payment->requestedBy)) {
    //                 $unitContact = ClientUnitContact::find($payment->requestedBy);
    //                 if ($unitContact) {
    //                     $contact = $unitContact->fullName;
    //                 }
    //             } else {
    //                 $contact = $payment->requestedBy;
    //             }
    //         }
    //         if (isset($unitContact->email) && $unitContact->email == null) {
    //             $unitContact->email = '';
    //         }
    //         if (isset($unitContact->email)) {
    //             $contactMailId = $unitContact->email;
    //         } else {
    //             $contactMailId = '';
    //         }
    //         if ($payment->type == 2) {
    //             $from = "Online";
    //         } else {
    //             if ($payment->modeOfRequest == 1) {
    //                 $from = "Email";
    //             } elseif ($payment->modeOfRequest == 2) {
    //                 $from = "Phone";
    //             } elseif ($payment->modeOfRequest == 3) {
    //                 $from = "SMS";
    //             }
    //         }
    //         $schedule = ClientUnitSchedule::where('clientUnitId', $payment->unitId)
    //             ->where('staffCategoryId', $payment->categoryId)
    //             ->where('shiftId', $payment->shiftId)
    //             ->first();
    //         $html = '';
    //         if (isset($schedule->startTime)) {
    //             $bookDateTime = $payment->date . " " . $schedule->startTime;
    //             $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
    //             $created = Carbon::createFromFormat('Y-m-d H:i:s', $payment->created_at);
    //             $diff_in_minutes = $created->diffInRealHours($to);
    //             $clasRed = $diff_in_minutes < 48 ? "redClr" : "";
    //             $html .= "<div class='font10'>" . $contact . " | ";
    //             $html .= $from . "</div>";
    //             $html .= "<div class='font10'><div class='" . $clasRed . "'>Notice Given - <strong class='hourstostart'>" . round($diff_in_minutes) .
    //                 " Hrs</div></strong></div>";
    //             $html .= "<div class='font10'> " . $payment->reason_text . "</div>";
    //             $html .= "<div class='font10'>" . date('d-m-Y H:i', strtotime($payment->created_at)) . "</div>";
    //             $payment->totalhoursto = round($diff_in_minutes);
    //         } else {
    //             $payment->totalhoursto = 0;
    //         }
    //         $payment->details = $html;
    //         $payment->costPerShift = $this->calculateCostBooking($payment);
    //         $payment->costPerShiftNumberonly = $this->calculateCostBookingNumberonly($payment);
    //         $payment->profile = $this->profileBtn($payment);
    //     }
    
    //     $autharray = [
    //         'usertype' => auth()->user()->userable_type,
    //         'userid' => auth()->user()->userable_id,
    //         'name' => auth()->user()->name
    //     ];
    
    //     $days = $bookings->pluck('entry_date')->unique()->values()->all();
    
    //     return view('unitPortal.bookings', compact(
    //         'shifts',
    //         'categories',
    //         'bookings',
    //         'days',
    //         'events',
    //         'units',
    //         'unitsdropdown',
    //         'autharray'
    //     ));
    // }
        public function list(Request $request)
    {
        $unitHelper = app(UnitHelperService::class);
        $events = $unitHelper->getComingEvents();
        $loggedInClientId = Auth::user()->portal_id;
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $units = ClientUnit::where('status', 1)->whereIn('clientId', function ($type) use ($loggedInClientId) {
                $type->select('clientId')
                    ->from('clients')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            })->get();
        } else {
            $units = ClientUnit::where('status', 1)->where('clientUnitId', $loggedInClientId)->get();
        }

        $unitsdropdown = ClientUnit::where('status', 1)
            ->where('clientId', $loggedInClientId)
            ->get();
        $selectedUnitId = $request->input('unitId');
        $shifts = Shift::whereNotIn('shiftId', [6])->get();
        $categories = StaffCategory::whereIn('categoryId', [1, 2, 3])->get();
        $query = Booking::with(['category', 'shift', 'unit', 'workflowApprovers.role', 'workflow', 'bookingWorkflowStatus'])
            ->latest()
            ->where('unitStatus', '<>', 3)
            ->where('unitStatus', '<>', 5);
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $query->whereIn('unitId', function ($type) use ($loggedInClientId) {
                $type->select('clientUnitId')
                    ->from('client_units')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            });
        } else {
            $query->where('unitId', $loggedInClientId);
        }

        if(request()->has('status') && !empty(request('status'))){
            if(request('status')==7){
              $query->whereIn('unitStatus',[0,1,2,3,4,5,6]);
            }else if(request('status')==8){
  
              $query->whereRaw("STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < ?", [$currentDateTime])
              ->whereIn('unitStatus',[1,4])->where('staffStatus',3);
              
            }else if(request('status')==9){
  
              
              $query->whereHas('timesheet', function ($q) {
                $q->whereNotNull('image');
              });
              
            }else if(request('status')==10){
              $query->whereHas('timesheet', function ($q) {
                $q->where('status',2);
              });
            }else{
              $query->where('unitStatus',request('status'));
            }
  
          }else{
            $query->whereIn('unitStatus',[4,1]);
          }
        // Apply the unit filter if a unit is selected
        if ($selectedUnitId) {
            $query->where('unitId', $selectedUnitId);
        }
        $bookings = $query->take(30)->get();
        $contactMailId  = '';
        foreach ($bookings as $payment) {
            $contact = "";
            if (!empty($payment->requestedBy)) {
                if (is_numeric($payment->requestedBy)) {
                    $unitContact = ClientUnitContact::find($payment->requestedBy);
                    if ($unitContact) {
                        $contact = $unitContact->fullName;
                    }
                } else {
                    $contact = $payment->requestedBy;
                }
            }
            if (isset($unitContact->email) && $unitContact->email == null) {
                $unitContact->email = '';
            }
            if (isset($unitContact->email)) $contactMailId = $unitContact->email;
            else $contactMailId = '';
            if ($payment->type == 2) {
                $from = "Online";
            } else {
                if ($payment->modeOfRequest == 1) {
                    $from = "Email";
                } elseif ($payment->modeOfRequest == 2) {
                    $from = "Phone";
                } elseif ($payment->modeOfRequest == 3) {
                    $from = "SMS";
                }
            }
            $schedule = ClientUnitSchedule::where('clientUnitId', $payment->unitId)->where('staffCategoryId', $payment->categoryId)
                ->where('shiftId', $payment->shiftId)->first();
            $html = "";
            if (isset($schedule->startTime)) {
                $bookDateTime = $payment->date . " " . $schedule->startTime;
                $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
                $created = Carbon::createFromFormat('Y-m-d H:i:s', $payment->created_at);
                $diff_in_minutes = $created->diffInRealHours($to);
                if ($diff_in_minutes < 48) $clasRed = "redClr";
                else $clasRed = "";
                $html = "";
                $html .= "<div class='font10'>" . $contact . " | ";
                $html .= $from . "</div>";
                $html .= "<div class='font10'><div class='" . $clasRed . "'>Notice Given - <strong class='hourstostart'>" . round($diff_in_minutes) .
                    " Hrs</div></strong></div>";
                $html .= "<div class='font10'> " . $payment->reason_text . "</div>";
                $html .= "<div class='font10'>" . date('d-m-Y H:i', strtotime($payment->created_at)) . "</div>";
                $payment->totalhoursto = round($diff_in_minutes);
            }else{
                $payment->totalhoursto=0;
            }
            $payment->details = $html;
            $payment->costPerShift = $this->calculateCostBooking($payment);
            $payment->costPerShiftNumberonly = $this->calculateCostBookingNumberonly($payment);
            $payment->profile = $this->profileBtn($payment);
        }
        $autharray = array('usertype' => auth()->user()->userable_type, 'userid' => auth()->user()->userable_id, 'name' => auth()->user()->name);
        $days = $bookings->pluck('entry_date')->unique()->values()->all();

        return view('unitPortal.bookings', compact('shifts', 'categories', 'bookings', 'days', 'events', 'units', 'unitsdropdown', 'autharray'));
    }
    
    public function listShiftToApprove(Request $request)
    {
        $currentDateTime = Carbon::now()->format('Y-m-d H:i:s'); 
        $unitHelper = app(UnitHelperService::class);
        $events = $unitHelper->getComingEvents();
        $loggedInClientId = Auth::user()->portal_id;
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $units = ClientUnit::where('status', 1)->whereIn('clientId', function ($type) use ($loggedInClientId) {
                $type->select('clientId')
                    ->from('clients')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            })->get();
        } else {
            $units = ClientUnit::where('status', 1)->where('clientUnitId', $loggedInClientId)->get();
        }

        $unitsdropdown = ClientUnit::where('status', 1)
            ->where('clientId', $loggedInClientId)
            ->get();
        $selectedUnitId = $request->input('unitId');
        $dateRange = $request->input('dateRange'); // Get the date range from the request
        $shifts = Shift::whereNotIn('shiftId', [6])->get();
        $categories = StaffCategory::whereIn('categoryId', [1, 2, 3])->get();
        $loggedInPortalId = Auth::user()->portal_id;
        $staff = Staff::with(['category'])
        ->where('status', 1)
        ->whereNull('deleted_at')
        ->whereHas('bookings', function ($q) use ($loggedInPortalId) {
            $q->whereIn('unitId', function ($sub) use ($loggedInPortalId) {
                $sub->select('clientUnitId')
                    ->from('client_units')
                    ->whereNotIn('clientId', [16, 37, 44, 25])
                    ->where('clientId', $loggedInPortalId);
            });
        })
        ->get();
        $query = Booking::with(['category', 'shift', 'unit', 'workflowApprovers.role', 'workflow', 'bookingWorkflowStatus'])
            ->latest()
            //->where('unitStatus', '<>', 3)
            //->where('unitStatus', '<>', 5);
            ->where('unitStatus', 1);
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $query->whereIn('unitId', function ($type) use ($loggedInClientId) {
                $type->select('clientUnitId')
                    ->from('client_units')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            });
        } else {
            $query->where('unitId', $loggedInClientId);
        }

        if(request()->has('status') && !empty(request('status'))){
            if(request('status')==7){
              $query->whereIn('unitStatus',[0,1,2,3,4,5,6]);
            }else if(request('status')==8){
            
                $query->whereRaw(
                      DB::raw("STR_TO_DATE(CONCAT(date, ' ', end_time), '%Y-%m-%d %H:%i:%s') < '{$currentDateTime}'") // ✅ changed to DB::raw
                  )
                  ->whereIn('unitStatus', [1, 4])
                  ->where('staffStatus', 3);
                
              }else if(request('status')==9){
  
              
              $query->whereHas('timesheet', function ($q) {
                $q->whereNotNull('image');
              });
              
            }else if(request('status')==10){
              $query->whereHas('timesheet', function ($q) {
                $q->where('status',2);
              });
            }else{
              $query->where('unitStatus',request('status'));
            }
  
          }else{
            $query->whereIn('unitStatus',[1]);
          }
        // Apply the unit filter if a unit is selected
        if ($selectedUnitId) {
            $query->where('unitId', $selectedUnitId);
        }
        if ($request->filled('bookingId')) {
            $query->where('bookingId', $request->input('bookingId'));
        }
    
        if (request()->filled('dateRange')) {
            $dates = explode(" - ", request('dateRange'));
            $query->whereBetween('date', [
                date('Y-m-d', strtotime($dates[0])),
                date('Y-m-d', strtotime($dates[1]))
            ]);
        }
        
        if ($request->filled('categoryId')) {
            $query->where('categoryId', $request->input('categoryId'));
        }
    
        if ($request->filled('shiftId')) {
            $query->where('shiftId', $request->input('shiftId'));
        }
    
        if ($request->filled('staffId')) {
            $query->where('staffId', $request->input('staffId'));
        }
        $bookings = $query->take(30)->get();
        $contactMailId  = '';
        foreach ($bookings as $payment) {
            $contact = "";
            if (!empty($payment->requestedBy)) {
                if (is_numeric($payment->requestedBy)) {
                    $unitContact = ClientUnitContact::find($payment->requestedBy);
                    if ($unitContact) {
                        $contact = $unitContact->fullName;
                    }
                } else {
                    $contact = $payment->requestedBy;
                }
            }
            if (isset($unitContact->email) && $unitContact->email == null) {
                $unitContact->email = '';
            }
            if (isset($unitContact->email)) $contactMailId = $unitContact->email;
            else $contactMailId = '';
            if ($payment->type == 2) {
                $from = "Online";
            } else {
                if ($payment->modeOfRequest == 1) {
                    $from = "Email";
                } elseif ($payment->modeOfRequest == 2) {
                    $from = "Phone";
                } elseif ($payment->modeOfRequest == 3) {
                    $from = "SMS";
                }
            }
            $schedule = ClientUnitSchedule::where('clientUnitId', $payment->unitId)->where('staffCategoryId', $payment->categoryId)
                ->where('shiftId', $payment->shiftId)->first();
            $html = "";
            if (isset($schedule->startTime)) {
                $bookDateTime = $payment->date . " " . $schedule->startTime;
                $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
                $created = Carbon::createFromFormat('Y-m-d H:i:s', $payment->created_at);
                $diff_in_minutes = $created->diffInRealHours($to);
                if ($diff_in_minutes < 48) $clasRed = "redClr";
                else $clasRed = "";
                $html = "";
                $html .= "<div class='font10'>" . $contact . " | ";
                $html .= $from . "</div>";
                $html .= "<div class='font10'><div class='" . $clasRed . "'>Notice Given - <strong class='hourstostart'>" . round($diff_in_minutes) .
                    " Hrs</div></strong></div>";
                $html .= "<div class='font10'> " . $payment->reason_text . "</div>";
                $html .= "<div class='font10'>" . date('d-m-Y H:i', strtotime($payment->created_at)) . "</div>";
                $payment->totalhoursto = round($diff_in_minutes);
            }else{
                $payment->totalhoursto=0;
            }
            $payment->details = $html;
            $payment->costPerShift = $this->calculateCostBooking($payment);
            $payment->costPerShiftNumberonly = $this->calculateCostBookingNumberonly($payment);
            $payment->profile = $this->profileBtn($payment);
        }
        $autharray = array('usertype' => auth()->user()->userable_type, 'userid' => auth()->user()->userable_id, 'name' => auth()->user()->name);
        $days = $bookings->pluck('entry_date')->unique()->values()->all();

        return view('unitPortal.bookings-shift-approve', compact('shifts', 'categories','staff', 'bookings', 'days', 'events', 'units', 'unitsdropdown', 'autharray'));
    }

       public function listArchivedBooking(Request $request)
    {
        $unitHelper = app(UnitHelperService::class);
        $events = $unitHelper->getComingEvents();
        $loggedInClientId = Auth::user()->portal_id;
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $units = ClientUnit::where('status', 1)->whereIn('clientId', function ($type) use ($loggedInClientId) {
                $type->select('clientId')
                    ->from('clients')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            })->get();
        } else {
            $units = ClientUnit::where('status', 1)->where('clientUnitId', $loggedInClientId)->get();
        }
 
        $staffList = $this->getComplianceStaffList(); // Fetch compliance staff list
        $unitsdropdown = ClientUnit::where('status', 1)
            ->where('clientId', $loggedInClientId)
            ->get();
        $selectedUnitId = $request->input('unitId');
        $selectedShiftId = $request->input('shiftId');
        $selectedCategoryId = $request->input('categoryId');
        $selectedDate = $request->input('date');
        $staffName = $request->input('staffName');
        $bookingId = $request->input('bookingId');
 
        $shifts = Shift::whereNotIn('shiftId', [6])->get();
        $categories = StaffCategory::whereIn('categoryId', [1, 2, 3])->get();
        // Get the date 3 months ago
        $threeMonthsAgo = Carbon::now()->subMonths(3);
 
        // Modify the query to include bookings older than 3 months
        $query = Booking::with(['category', 'shift', 'unit', 'workflowApprovers.role', 'workflow', 'bookingWorkflowStatus','staff'])
            ->latest()
            ->where('unitStatus', '<>', 3)
            ->where('unitStatus', '<>', 5)
            ->where('date', '<', $threeMonthsAgo); // Filter for bookings older than 3 months
        // Apply the "Show All Shifts" filter
        if ($request->has('status') && $request->input('status') == 7) {
            // Show all shifts, including cancelled ones
        } else {
            // Exclude cancelled shifts
            $query->where('unitStatus', '<>', 2); // 2 is the status for cancelled shifts
        }
            // Handle date range filter
        // Handle date range filter
        if ($request->has('dateRange') && !empty($request->input('dateRange'))) {
            $dates = explode(' to ', $request->input('dateRange'));
            if (count($dates) === 2) {
                $query->whereDate('date', '>=', $dates[0])
                    ->whereDate('date', '<=', $dates[1]);
            }
        }
            // Apply the unit filter if a unit is selected
        if ($selectedUnitId) {
            $query->where('unitId', $selectedUnitId);
        }
            // Apply the shift filter if a shift is selected
        if ($selectedShiftId) {
            $query->where('shiftId', $selectedShiftId);
        }
            // Apply the category filter if a category is selected
        if ($selectedCategoryId) {
            $query->where('categoryId', $selectedCategoryId);
        }
            // Filter by staff ID
        if ($request->filled('staffId')) {
            $query->where('staffId', $request->input('staffId'));
        }
 
        if ($request->has('fromDate') && !empty($request->input('fromDate'))) {
            $fromDate = $request->input('fromDate');
            $query->whereDate('date', '>=', $fromDate);
        }
        
        if ($request->has('toDate') && !empty($request->input('toDate'))) {
            $toDate = $request->input('toDate');
            $query->whereDate('date', '<=', $toDate);
        }
 
        if ($bookingId) {
            $query->where('bookingId', $bookingId);
        }
        if (Auth::user()->userable_type == 'App\Models\Client') {
            $query->whereIn('unitId', function ($type) use ($loggedInClientId) {
                $type->select('clientUnitId')
                    ->from('client_units')->whereNotIn('clientId', [16, 37, 44, 25])->where('clientId', $loggedInClientId);
            });
        } else {
            $query->where('unitId', $loggedInClientId);
        }
        // Apply the unit filter if a unit is selected
        if ($selectedUnitId) {
            $query->where('unitId', $selectedUnitId);
        }
        $bookings = $query->paginate(30)->appends($request->query());
        $contactMailId  = '';
        foreach ($bookings as $payment) {
           
            $contact = "";
            if (!empty($payment->requestedBy)) {
                if (is_numeric($payment->requestedBy)) {
                    $unitContact = ClientUnitContact::find($payment->requestedBy);
                    if ($unitContact) {
                        $contact = $unitContact->fullName;
                    }
                } else {
                    $contact = $payment->requestedBy;
                }
            }
            if (isset($unitContact->email) && $unitContact->email == null) {
                $unitContact->email = '';
            }
            if (isset($unitContact->email)) $contactMailId = $unitContact->email;
            else $contactMailId = '';
            if ($payment->type == 2) {
                $from = "Online";
            } else {
                if ($payment->modeOfRequest == 1) {
                    $from = "Email";
                } elseif ($payment->modeOfRequest == 2) {
                    $from = "Phone";
                } elseif ($payment->modeOfRequest == 3) {
                    $from = "SMS";
                }
            }
            $schedule = ClientUnitSchedule::where('clientUnitId', $payment->unitId)->where('staffCategoryId', $payment->categoryId)
                ->where('shiftId', $payment->shiftId)->first();
            $html = "";
            if (isset($schedule->startTime)) {
                $bookDateTime = $payment->date . " " . $schedule->startTime;
                $to = Carbon::createFromFormat('Y-m-d H:i:s', $bookDateTime);
                $created = Carbon::createFromFormat('Y-m-d H:i:s', $payment->created_at);
                $diff_in_minutes = $created->diffInRealHours($to);
                if ($diff_in_minutes < 48) $clasRed = "redClr";
                else $clasRed = "";
                $html = "";
                $html .= "<div class='font10'>" . $contact . " | ";
                $html .= $from . "</div>";
                $html .= "<div class='font10'><div class='" . $clasRed . "'>Notice Given - <strong class='hourstostart'>" . round($diff_in_minutes) .
                    " Hrs</div></strong></div>";
                $html .= "<div class='font10'> " . $payment->reason_text . "</div>";
                $html .= "<div class='font10'>" . date('d-m-Y H:i', strtotime($payment->created_at)) . "</div>";
                $payment->totalhoursto = round($diff_in_minutes);
            }else{
                $payment->totalhoursto=0;
            }
            $payment->details = $html;
            $payment->costPerShift = $this->calculateCostBooking($payment);
            $payment->costPerShiftNumberonly = $this->calculateCostBookingNumberonly($payment);
            $payment->profile = $this->profileBtn($payment);
        }
        $autharray = array('usertype' => auth()->user()->userable_type, 'userid' => auth()->user()->userable_id, 'name' => auth()->user()->name);
 
        return view('unitPortal.archived-booking', compact('staffList','shifts', 'categories', 'bookings', 'events', 'units', 'unitsdropdown', 'autharray'));
    }
    public function getBookingDetails(Request $request)
    {
        $request->validate([
            'bookingId' => 'required|exists:bookings,bookingId', // Ensure the booking ID exists
        ]);

        $booking = Booking::with(['staff', 'unit', 'shift'])->find($request->input('bookingId'));

        if (!$booking) {
            return response()->json(['success' => false, 'message' => 'Booking not found.']);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'staff' => $booking->staff ? $booking->staff->forname . ' ' . $booking->staff->surname : 'Unassigned',
                'unit' => $booking->unit ? $booking->unit->name : 'No Unit',
                'shift' => $booking->shift ? $booking->shift->name : 'Unknown Shift',
                'shiftDate' => $booking->date, 
            ],
        ]);
    }
    public function profileBtn($booking)
    {
        if ($booking->staffId) {
            if ($booking->unitStatus == 2 || $booking->unitStatus == 3) { // if cancelled or unable to cover
                $html = "<a href='javascript:void(0)' class='profile 1'> Cancelled</a>";
            } else {
                if ($booking->unit->clientId == 29) {
                    $html = "<a target='_blank' href='https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/".$booking->staff->profileDocumentFile."' class='profile 2'><i class='fa fa-user' aria-hidden='true'></i>
            ></a>";
                } else {
                    $html = "<a target='_blank' href='https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/".$booking->staff->profileDocumentFile."' class='profile 3'><i class='fa fa-user' aria-hidden='true'></i></a>";
                }
            }
        } else {
            if ($booking->unitStatus == 2 || $booking->unitStatus == 3) {
                $profile = '<i class="fa fa-ban" aria-hidden="true"></i>';
            } else {
                $profile = '<i class="fa fa-user" aria-hidden="true"></i>';
            }
            $html = "<a href='javascript:void(0)' class='profile 4' >" . $profile . "</a>";
        }
        return $html;
    }

    public function calculateCostBooking($booking)
    {   $totalHoursUnit=0;
        $ta=0;
        $unitId = auth()->user()->portal_id;
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $unitId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();

        if(isset($filteredData->taType)){

        if($filteredData->taType==1){

        }else if($filteredData->taType==2){
            if(isset($filteredData->taPerMile)){
             $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
            }else{
             $ta = 0;
            }
        }else if($filteredData->taType==3){
            if(isset($getTaMile->invoiceTaEditable)){
                 $ta = $getTaMile->invoiceTaEditable;
            }else{
                if(isset($getTaMile->invoiceTa)){
                    $ta = $getTaMile->invoiceTa;
                }
            }
        }
      }else{
            $ta = 0;
        }

        //$ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];

        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }

        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData->$columnName)) {
            $payAmountPerHr = $filteredData->$columnName;
        } else {
            $payAmountPerHr = 0;
        }
        //$payAmountPerHr = $filteredData[$columnName];

        if (isset($filteredDataEnic->$columnName)) {
            $clientPaymentsEnicRate = $filteredDataEnic->$columnName;
        } else {
            $clientPaymentsEnicRate = 0;
        }
        $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();

        if(isset($schedule)){
            if(isset($payment->timesheet->unitHours)){
                $totalHoursUnit = $payment->timesheet->unitHours;
            }else{
                $totalHoursUnit = $schedule->totalHoursUnit;
            }
        }
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta;
        return  "<a href='javascript:void(0)' price='£ " . number_format($inTotal, 2) . "' class='view showPrice'><i class='fa fa-search' aria-hidden='true'></i>
      </a>";
    }
    public function calculateCostBookingNumberonly($booking)
    {   
        $taxyear = TaxYear::where('default',1)->first();
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $booking->unitId)->where('tax_year_id', $taxyear->taxYearId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();
        if (isset($filteredData->taPerMile) && isset($filteredData->taNoOfMiles)) {
            $ta = $filteredData->taPerMile * $filteredData->taNoOfMiles;
        } else {
            $ta = 0;
        }
        //$ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];

        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }

        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData[$columnName])) {
            $payAmountPerHr = $filteredData[$columnName];
        } else {
            $payAmountPerHr = 0;
        }
        if (isset($filteredDataEnic[$columnName])) {
            $clientPaymentsEnicRate = $filteredDataEnic[$columnName];
        } else {
            $clientPaymentsEnicRate = 0;
        }

        $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();
        if (isset($schedule->totalHoursUnit)) {
            $totalHoursUnit = $schedule->totalHoursUnit;
        } else {
            $totalHoursUnit = 0;
        }
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate);
        return  number_format($inTotal, 2);
    }

    public function save()
    { 
        $user = auth()->user();
        $wrkflow = Workflow::where('portal_type', $user->portal_type)
            ->where('portal_id', $user->portal_id)->with('approvers.role')->first();
        if (isset($wrkflow->name)) {
            $wrkflowId = $wrkflow->id;
        } else {
            $wrkflowId = null;
        }
        
        // Get all request data arrays
        $dates = request('date', []);
        $shifts = request('shift', []);
        $categories = request('category', []);
        $numbers = request('numbers', []);
        $startTimes = request('startTimes', []);
        $endTimes = request('endTimes', []);
        $reasons = request('reason', []); // Keep as 'reason' to match frontend
        $requestedBy = request('requestedBy', []);
        $impNotes = request('impNotes', []);
        $unitIds = request('unitId', []);
        $urgentValues = request('urgent', []);
        
        // Process each booking entry
        $validEntries = [];
        $maxCount = max(count($dates), count($shifts), count($categories));
        
        for ($i = 0; $i < $maxCount; $i++) {
            // Skip entries with missing required data
            if (empty($dates[$i] ?? '') || empty($shifts[$i] ?? '') || empty($categories[$i] ?? '')) {
                continue;
            }
            
            $validEntries[] = [
                'date' => $dates[$i],
                'shift' => $shifts[$i],
                'category' => $categories[$i],
                'numbers' => $numbers[$i] ?? 1,
                'startTime' => $startTimes[$i] ?? '',
                'endTime' => $endTimes[$i] ?? '',
                'reason' => $reasons[$i] ?? '',
                'requestedBy' => $requestedBy[$i] ?? '',
                'impNotes' => $impNotes[$i] ?? '',
                'unitId' => $unitIds[$i] ?? null,
                'urgent' => $urgentValues[$i] ?? 0
            ];
        }
        
        // Create bookings for each valid entry
        foreach ($validEntries as $entryIndex => $entry) {
            for ($j = 0; $j < $entry['numbers']; $j++) {
                
                if (auth()->user()->userable_type == 'App\Models\Client') {
                    $unitId = $entry['unitId'] ?: 0;
                } else {
                    $unitId = auth()->user()->portal_id;
                }

                // ✅ Determine unit status based on urgent booking
                $isUrgent = $entry['urgent'] == 1;
                // $unitStatus = $isUrgent ? 4 : ($wrkflowId != null ? 1 : 4);

                $confirmedAt = null;
                
                if ($isUrgent == 1) {
                    // If urgent booking is checked, set to confirmed immediately
                    $bookingStatus = 4;
                    $confirmedAt = now();
                } else if ($wrkflowId != null) {
                    // If there's a workflow and not urgent, set to pending approval
                    $bookingStatus = 1;
                }

                $booking = Booking::create([
                    'categoryId' => $entry['category'],
                    'date' => date('Y-m-d', strtotime($entry['date'])),
                    'unitId' => $unitId,
                    'unitStatus' => $bookingStatus,
                    'type'  => 2,
                    'start_time'  => !empty($entry['startTime']) ? date('H:i:s', strtotime($entry['startTime'])) : '00:00:00',
                    'end_time'  => !empty($entry['endTime']) ? date('H:i:s', strtotime($entry['endTime'])) : '00:00:00',
                    'requestedDate' => date('Y-m-d', strtotime(date('Y-m-d'))),
                    'shiftId' => $entry['shift'],
                    'reason' => $entry['reason'],
                    'requestedBy' => $entry['requestedBy'],
                    'importantNotes' => $entry['impNotes'],
                    'confirmedBy' => auth()->user()->id,
                    'confirmedAt' => $confirmedAt,
                    'cancelInformUnitTo' => 0,
                    'modeOfRequest' => 1,
                    'booked_user_id' => auth()->user()->userable_id, //need to change with ClientLogins id
                    'booked_portal' => $user->portal_type,
                    'booked_portal_id' => $user->portal_id

                ]);
                // Only create workflow if booking is not urgent (urgent bookings are auto-confirmed)
                if ($wrkflowId && $isUrgent != 1) {
                    if ($user->portal_type == Client::class) {
                        BookingWorkflow::create([
                            'booking_id' => $booking->bookingId,
                            'workflow_id' => $wrkflowId,
                            'approval_status' => 'pending',
                            'approver_type' => $user->portal_type,
                            'approver_id' => $user->portal_id,
                            'workflow' => $wrkflow->toArray()
                        ]);
                    } else {
                        if ($wrkflow->requires_client_approval) {
                            $client = Client::where('clientId', $user->portal->clientId)->first();
                            if ($client) {
                                $clientWorkflow =  Workflow::where('portal_type', Client::class)
                                    ->where('portal_id', $client->clientId)->with('approvers.role')->first();
                                if ($clientWorkflow) {
                                    BookingWorkflow::create([
                                        'booking_id' => $booking->bookingId,
                                        'workflow_id' => $clientWorkflow->id,
                                        'approval_status' => 'pending',
                                        'approver_type' => Client::class,
                                        'approver_id' => $client->clientId,
                                        'workflow' => $clientWorkflow->toArray()
                                    ]);
                                }
                            }
                        }
                        BookingWorkflow::create([
                            'booking_id' => $booking->bookingId,
                            'workflow_id' => $wrkflowId,
                            'approval_status' => 'pending',
                            'approver_type' => $user->portal_type,
                            'approver_id' => $user->portal_id,
                            'workflow' => $wrkflow->toArray()
                        ]);
                    }
                }
                BookingLog::create([
                    'bookingId' => $booking->bookingId,
                    'content' => 'Booking <span class="logHgt">Created </span> by <strong></strong> from Unit Portal..</strong>',
                    'author' => 1,
                ]);

                if (Cache::has('all_booking_array')) {
                    $all_booking_array = Cache::get('all_booking_array');
                } else {
                    $all_booking_array = [];
                }

                $booking_array = array(
                    'shiftId' => $booking->bookingId,
                    'date' => $booking->date,
                    'shiftname' => $booking->shift->name,
                    'unitname' => $booking->unit->alias,
                    'category' => $booking->category->name,
                    'staff' => $booking->staff->forname . '' . $booking->staff->surname,
                    'bookedby' => $booking->assignedby->name,
                    'time' => date("s"),
                    'image' => 1
                );

                array_push($all_booking_array, array(
                    'bookingId' => $booking->bookingId,
                    'date' => $booking->date,
                    'shiftname' => $booking->shift->name,
                    'unitname' => $booking->unit->alias,
                    'category' => $booking->category->name,
                    'staff' => $booking->staff->forname . '' . $booking->staff->surname,
                    'bookedby' => $booking->assignedby->name,
                    'time' => date("s")
                ));
                foreach ($all_booking_array as $notification) {

                    BookingNotification::updateOrCreate([
                        'bookingId' => $notification['bookingId'],
                        'date' => $notification['date'],
                        'shiftname' => $notification['shiftname'],
                        'unitname' => $notification['unitname'],
                        'category' => $notification['category'],
                        'staff' => $notification['staff'],
                        'bookedby' => $notification['bookedby']
                    ]);
                }



                if (Cache::has('booking_array')) {
                    Cache::forget('booking_array');
                }
                Cache::put('booking_array', $booking_array);
                Cache::put('all_booking_array', $all_booking_array);

                $quotation = Quotation::firstOrCreate([
                    'bookingId' => $booking->bookingId,
                ]);
                $taxWeek = TaxWeek::where('date', $booking->date)->first();
                if ($taxWeek && isset($taxWeek->weekNumber)) {
                    $week = $taxWeek->weekNumber;
                } else {
                    // Fallback: get all tax weeks and try to find the first one
                    $taxWeeks = TaxWeek::all();
                    if ($taxWeeks->isNotEmpty()) {
                        $week = $taxWeeks[0]->weekNumber;
                    } else {
                        // Final fallback: calculate week number from date
                        $week = date('W', strtotime($booking->date));
                    }
                }
                    
                $quotation->update([
                    'year' => date('Y', strtotime($booking->date)),
                    'month' => date('m', strtotime($booking->date)),
                    'week'  => $week,
                ]);
                //  }else {
                //   return ['status' => 400, 'message' => 'Please select category and shift'];
                // }
                // Update weekly utilisation based on booking date
                Log::info('About to call updateWeeklyUtilisationForBooking', [
                    'booking_id' => $booking->bookingId,
                    'booking_date' => $booking->date,
                    'unit_id' => $unitId
                ]);
                $booking->initial_cost = $this->calculateCostBookingNumberonly($booking);
                $booking->save();
                $this->updateWeeklyUtilisationForBooking($booking, $unitId);

                //Utilisation finished
            }
        }
        ClientUnitChat::chatSave($unitId,"Booked the Shift",$booking->bookingId,false);

        //  return $booking;
        return ['status' => 200, 'url' => route('bookings')];
    }

    public function getTimings()
    {
        $schedule = ClientUnitSchedule::where('clientUnitId',auth()->user()->portal_id)
            ->where('staffCategoryId', request('category'))
            ->where('shiftId', request('shift'))
            ->first();
        if (isset($schedule->startTime)) {
            return [
                'start' => date('H:i', strtotime($schedule->startTime)),
                'end' => date('H:i', strtotime($schedule->endTime)),
                'cost'  => $this->calculateCost(request('date'), request('shift'), request('category'))
            ];
        } else {
            return ['start' => '00:00', 'end' => '00:00', 'cost' => $this->calculateCost(request('date'), request('shift'), request('category'))];
        }
    }

    public function saveUnitContact()
    {
        ClientUnitContact::create([
            'clientUnitId'  => auth()->guard('unit')->user()->clientUnitId,
            'fullName'  => request('name'),
            'position'  => request('position'),
            'phone'  => request('phone'),
            'email'  => request('email'),
        ]);

        $contacts = ClientUnitContact::where('clientUnitId', auth()->user()->clientUnitId)->latest()->get();
        return ['status' => 200, 'contacts' => $contacts];
    }

        public function getComplianceStaffList()
    {
        $loggedInPortalId = Auth::user()->portal_id;
 
        // Calculate the date 2 months ago
        $twoMonthsAgo = Carbon::now()->subMonths(2)->startOfDay();
 
        // Fetch compliance staff
        $staff = Staff::with(['category'])
            ->where('status', 1)
            ->whereNull('deleted_at')
            ->whereHas('bookings', function ($q) use ($loggedInPortalId, $twoMonthsAgo) {
                $q->whereIn('unitId', function ($sub) use ($loggedInPortalId) {
                    $sub->select('clientUnitId')
                        ->from('client_units')
                        ->whereNotIn('clientId', [16, 37, 44, 25])
                        ->where('clientId', $loggedInPortalId);
                })
                ->whereDate('date', '<', $twoMonthsAgo); // Filter to dates before 2 months ago
            })
            ->get();
 
        return $staff;
    }
 

    // public function calculateCost($date, $shift, $category)
    // {
    //     $allClientPayments = ClientUnitPayment::where('clientUnitId', auth()->user()->clientUnitId)->where('staffCategoryId', $category)->get();
    //     $day = strtolower(date('D', strtotime($date)));
    //     $shiftId = $shift;
    //     $clientPayment = $allClientPayments->where('rateType', 1);
    //     $filteredData = $clientPayment->first();

    //     // $ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];
    //     if (is_array($filteredData) && isset($filteredData['taPerMile'], $filteredData['taNoOfMiles'])) {
    //         $ta = $filteredData['taPerMile'] * $filteredData['taNoOfMiles'];
    //     } else {
    //         $ta = 0; // or handle it however you need
    //     }

    //     $clientPaymentsEnic = $allClientPayments->where('rateType', 2);
    //     $filteredDataEnic = $clientPaymentsEnic->first();

    //     switch ($day) {
    //         case "mon":
    //             $selectColumn = "Monday";
    //             break;
    //         case "tue":
    //             $selectColumn = "Tuesday";
    //             break;
    //         case "wed":
    //             $selectColumn = "Wednesday";
    //             break;
    //         case "thu":
    //             $selectColumn = "Thursday";
    //             break;
    //         case "fri":
    //             $selectColumn = "Friday";
    //             break;
    //         case "sat":
    //             $selectColumn = "Saturday";
    //             break;
    //         case "sun":
    //             $selectColumn = "Sunday";
    //             break;
    //     }

    //     if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
    //         $columnName = "day" . $selectColumn;
    //     } else if ($shiftId == 5) {
    //         $columnName = "twilight_" . strtolower($selectColumn);
    //     } else {
    //         $columnName = "night" . $selectColumn;
    //     }

    //     // $payAmountPerHr = $filteredData[$columnName];
    //     $payAmountPerHr = (is_array($filteredData) ? ($filteredData[$columnName] ?? 0) : 0);

    //     $clientPaymentsEnicRate = $filteredDataEnic[$columnName];

    //     $schedule = ClientUnitSchedule::where('clientUnitId', auth()->guard('unit')->user()->clientUnitId)
    //         ->where('staffCategoryId', $category)
    //         ->where('shiftId', $shiftId)
    //         ->first();
    //     if (isset($schedule->totalHoursUnit)) {
    //         $totalHoursUnit = $schedule->totalHoursUnit;
    //     } else {
    //         $totalHoursUnit = 0;
    //     }
    //     $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta;
    //     return  "£ " . number_format($inTotal, 2);
    // }
    public function getBookingLog(Request $req){
        $logs = BookingLog::with('admin')->where('bookingId',$req->bookId)->orderBy('bookingLogId','desc')->get();
        $html = view('bookings.partials.logTemplate',compact('logs'));
        return $html;
      }
    public function calculateCost($date, $shiftId, $category)
{
    $clientUnitId = auth()->user()->clientUnitId;
    $day = strtolower(date('D', strtotime($date)));

    // Get all related payments
    $allClientPayments = ClientUnitPayment::where('clientUnitId', $clientUnitId)
        ->where('staffCategoryId', $category)
        ->get();

    // Get rateType 1 (normal rate)
    $clientPayment = $allClientPayments->firstWhere('rateType', 1);

    // Calculate TA
    $taPerMile = $clientPayment->taPerMile ?? 0;
    $taNoOfMiles = $clientPayment->taNoOfMiles ?? 0;
    $ta = $taPerMile * $taNoOfMiles;

    // Get rateType 2 (ENIC rate)
    $clientPaymentEnic = $allClientPayments->firstWhere('rateType', 2);

    // Determine column based on day + shift
    $dayMap = [
        'mon' => 'Monday',
        'tue' => 'Tuesday',
        'wed' => 'Wednesday',
        'thu' => 'Thursday',
        'fri' => 'Friday',
        'sat' => 'Saturday',
        'sun' => 'Sunday',
    ];
    $selectColumn = $dayMap[$day] ?? 'Monday'; // default to Monday if unknown

    if (in_array($shiftId, [1, 2, 3])) {
        $columnName = "day{$selectColumn}";
    } elseif ($shiftId == 5) {
        $columnName = "twilight_" . strtolower($selectColumn);
    } else {
        $columnName = "night{$selectColumn}";
    }

    // Get pay rates
    $payAmountPerHr = $clientPayment->$columnName ?? 0;
    $clientEnicRate = $clientPaymentEnic->$columnName ?? 0;

    // Get total hours from schedule
    $schedule = ClientUnitSchedule::where('clientUnitId', $clientUnitId)
        ->where('staffCategoryId', $category)
        ->where('shiftId', $shiftId)
        ->first();

    $totalHoursUnit = $schedule->totalHoursUnit ?? 0;

    // Final cost calculation
    $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientEnicRate) + $ta;

    return "£ " . number_format($inTotal, 2);
}

/**
     * Update weekly utilisation for a booking based on the booking date
     * 
     * @param Booking $booking The booking object
     * @param int $unitId The unit ID
     * @return void
     */
    private function updateWeeklyUtilisationForBooking(Booking $booking, int $unitId)
    {
        Log::info('updateWeeklyUtilisationForBooking called', [
            'booking_id' => $booking->bookingId,
            'booking_date' => $booking->date,
            'unit_id' => $unitId
        ]);
        
        try {
            // Calculate financial year (April to March)
            $bookingDate = Carbon::parse($booking->date);
            $financialYear = $bookingDate->month >= 4 ? $bookingDate->year : $bookingDate->year - 1;
            
            Log::info('Financial year calculated', [
                'booking_date' => $booking->date,
                'financial_year' => $financialYear
            ]);
            
            // Get the annual budget for this unit and financial year
            $clientUnitAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
                ->where('financial_year', $financialYear)
                ->first();
            
            if (!$clientUnitAnnualBudget) {
                Log::warning('No annual budget found for unit', [
                    'unit_id' => $unitId, 
                    'booking_id' => $booking->bookingId,
                    'financial_year' => $financialYear,
                    'booking_date' => $booking->date
                ]);
                return;
            }
            
            Log::info('Annual budget found', [
                'annual_budget_id' => $clientUnitAnnualBudget->id,
                'financial_year' => $clientUnitAnnualBudget->financial_year
            ]);
            
            // Find the weekly budget where the booking date falls between week_start_date and week_end_date
            // Convert booking date to proper format for comparison
            $bookingDateString = $bookingDate->format('Y-m-d');
            
            $clientUnitWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                ->whereDate('week_start_date', '<=', $bookingDateString)
                ->whereDate('week_end_date', '>=', $bookingDateString)
                ->first();
            
            // Debug logging to track the weekly budget selection
            Log::info('Weekly budget search', [
                'annual_budget_id' => $clientUnitAnnualBudget->id,
                'booking_date' => $bookingDateString,
                'booking_id' => $booking->bookingId,
                'found_weekly_budget_id' => $clientUnitWeeklyBudget ? $clientUnitWeeklyBudget->id : null,
                'found_weekly_budget_week_start' => $clientUnitWeeklyBudget ? $clientUnitWeeklyBudget->week_start_date : null,
                'found_weekly_budget_week_end' => $clientUnitWeeklyBudget ? $clientUnitWeeklyBudget->week_end_date : null,
            ]);
            
            if (!$clientUnitWeeklyBudget) {
                // Let's also check what weekly budgets exist for debugging
                $availableWeeks = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
                    ->orderBy('week_start_date')
                    ->get(['id', 'week_number', 'week_start_date', 'week_end_date'])
                    ->toArray();
                
                Log::warning('No weekly budget found for booking date', [
                    'unit_id' => $unitId,
                    'booking_id' => $booking->bookingId,
                    'booking_date' => $bookingDateString,
                    'financial_year' => $financialYear,
                    'annual_budget_id' => $clientUnitAnnualBudget->id,
                    'available_weekly_budgets' => $availableWeeks
                ]);
                return;
            }
            
            // Calculate the cost of the booking
            $utilised_cost = $this->calculateCostBookingNumberonly($booking);
            
            Log::info('Booking cost calculated', [
                'booking_id' => $booking->bookingId,
                'utilised_cost' => $utilised_cost
            ]);
            
            // Update the weekly utilised fund using the global helper function
            // Pass null as weeklyBudgetId - the helper will find it from the booking
            updateWeeklyUtilisedFund(
                null, // Let the helper function find the weekly budget from the booking
                $utilised_cost,
                'addition',
                'App\Models\Booking',
                $booking->bookingId,
                '[NEW BOOKING] Budget utilization update for booking ID: ' . $booking->bookingId .
                                ' | Unit: ' . ($booking->unit->name ?? 'N/A') .
                                ' | Staff: ' . ($booking->staff->forname ?? 'N/A') . ' ' . ($booking->staff->surname ?? '') .
                                ' | Date: ' . $booking->date .
                                ' | Shift: ' . ($booking->shift->name ?? 'N/A') .
                                ' | Category: ' . ($booking->category->name ?? 'N/A') .
                                ' | Cost: £' . number_format($utilised_cost, 2)
            );
            
            Log::info('Weekly utilised fund updated successfully', [
                'weekly_budget_id' => $clientUnitWeeklyBudget->id,
                'booking_id' => $booking->bookingId,
                'booking_date' => $booking->date,
                'week_start_date' => $clientUnitWeeklyBudget->week_start_date,
                'week_end_date' => $clientUnitWeeklyBudget->week_end_date,
                'utilised_cost' => $utilised_cost
            ]);
            
        } catch (Exception $e) {
            Log::error('Error updating weekly utilised fund for booking', [
                'error' => $e->getMessage(),
                'booking_id' => $booking->bookingId,
                'unit_id' => $unitId,
                'booking_date' => $booking->date,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Debug function to check weekly budget data and log creation
     * This can be called manually to debug issues
     */
    public function debugWeeklyBudgetForBooking($bookingId)
    {
        $booking = Booking::find($bookingId);
        if (!$booking) {
            return ['error' => 'Booking not found'];
        }
        
        $unitId = $booking->unitId;
        
        // Calculate financial year (April to March)
        $bookingYear = date('Y', strtotime($booking->date));
        $bookingMonth = date('n', strtotime($booking->date));
        $financialYear = $bookingMonth >= 4 ? $bookingYear : $bookingYear - 1;
        
        // Get the annual budget
        $clientUnitAnnualBudget = ClientUnitAnnualBudget::where('client_unit_id', $unitId)
            ->where('financial_year', $financialYear)
            ->first();
        
        if (!$clientUnitAnnualBudget) {
            return ['error' => 'No annual budget found', 'unit_id' => $unitId, 'financial_year' => $financialYear];
        }
        
        // Find weekly budgets for this annual budget
        $allWeeklyBudgets = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
            ->orderBy('week_start_date')
            ->get(['id', 'week_number', 'week_start_date', 'week_end_date', 'total_weekly_utilisation']);
        
        // Find the specific weekly budget for this booking date
        $matchingWeeklyBudget = ClientUnitWeeklyBudget::where('client_unit_annual_budget_id', $clientUnitAnnualBudget->id)
            ->where('week_start_date', '<=', $booking->date)
            ->where('week_end_date', '>=', $booking->date)
            ->first();
        
        // Get logs for the matching weekly budget
        $logs = [];
        if ($matchingWeeklyBudget) {
            $logs = WeeklyUtilisedFundLog::where('client_unit_weekly_budget_id', $matchingWeeklyBudget->id)
                ->where('source_type', 'App\Models\Booking')
                ->where('source_id', $booking->bookingId)
                ->get();
        }
        
        return [
            'booking' => [
                'id' => $booking->bookingId,
                'date' => $booking->date,
                'unit_id' => $unitId,
            ],
            'financial_year' => $financialYear,
            'annual_budget' => [
                'id' => $clientUnitAnnualBudget->id,
                'financial_year' => $clientUnitAnnualBudget->financial_year,
            ],
            'all_weekly_budgets' => $allWeeklyBudgets->toArray(),
            'matching_weekly_budget' => $matchingWeeklyBudget ? [
                'id' => $matchingWeeklyBudget->id,
                'week_number' => $matchingWeeklyBudget->week_number,
                'week_start_date' => $matchingWeeklyBudget->week_start_date,
                'week_end_date' => $matchingWeeklyBudget->week_end_date,
                'total_weekly_utilisation' => $matchingWeeklyBudget->total_weekly_utilisation,
            ] : null,
            'logs_for_this_booking' => $logs->toArray(),
        ];
    }
    
    public function finalcostCalculation($booking)
    {   
        
        if( $booking->unitStatus!=4 || //4 is not confirmed from the unit.
            $booking->staffStatus==1 || //1 not confirmed or potential.
            $booking->staffId==NULL || //staff not assigned.
            $booking->timesheet->status!=2){ //Timesheet not approved.
            return 0;
        }
        $taxyear = TaxYear::where('default',1)->first();
        $allClientPayments = ClientUnitPayment::where('clientUnitId', $booking->unitId)->where('tax_year_id', $taxyear->taxYearId)->get();
        $day = strtolower(date('D', strtotime($booking->date)));
        $shiftId = $booking->shift->shiftId;
        $clientPayment = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 1);
        $filteredData = $clientPayment->first();

        if (isset($filteredData->taPerMile) && isset($filteredData->taNoOfMiles)) {
            $ta = $filteredData->taPerMile * $filteredData->taNoOfMiles;
        } else {
            $ta = 0;
        }
        $getTaMile = StaffUnitPayment::where('clientUnitId',$booking->unitId)->where('tax_year_id',$taxyear->taxYearId)
        ->where('staffId',$booking->staffId)->first();
        $timesheet = Timesheet::where('bookingId', $booking->bookingId)->where('status', 2)->first();
        if(isset($timesheet->unit_expense_amount)){
            $additionalExpenseAmount = $timesheet->unit_expense_amount;
        }else{
            $additionalExpenseAmount = 0;
        }
        if($filteredData->taType==1){
              $ta = 0;
            }else if($filteredData->taType==2){
              if(isset($filteredData->taPerMile)){
                $ta = $filteredData->taPerMile*$filteredData->taNoOfMiles;
              }else{
                $ta = 0;
              }
            }else if($filteredData->taType==3){
              if(isset($getTaMile->invoiceTaEditable)){
                $ta = $getTaMile->invoiceTaEditable;
              }else{
                if(isset($getTaMile->invoiceTa)){
                  $ta = $getTaMile->invoiceTa;
                }
              }              
        }
        $clientPaymentsEnic = $allClientPayments->where('staffCategoryId', $booking->category->categoryId)->where('rateType', 2);
        $filteredDataEnic = $clientPaymentsEnic->first();

        switch ($day) {
            case "mon":
                $selectColumn = "Monday";
                break;
            case "tue":
                $selectColumn = "Tuesday";
                break;
            case "wed":
                $selectColumn = "Wednesday";
                break;
            case "thu":
                $selectColumn = "Thursday";
                break;
            case "fri":
                $selectColumn = "Friday";
                break;
            case "sat":
                $selectColumn = "Saturday";
                break;
            case "sun":
                $selectColumn = "Sunday";
                break;
        }
        if ($shiftId == 1 || $shiftId == 2 || $shiftId == 3) {
            $columnName = "day" . $selectColumn;
        } else if ($shiftId == 5) {
            $columnName = "twilight_" . strtolower($selectColumn);
        } else {
            $columnName = "night" . $selectColumn;
        }
        if (isset($filteredData[$columnName])) {
            $payAmountPerHr = $filteredData[$columnName];
        } else {
            $payAmountPerHr = 0;
        }
        if (isset($filteredDataEnic[$columnName])) {
            $clientPaymentsEnicRate = $filteredDataEnic[$columnName];
        } else {
            $clientPaymentsEnicRate = 0;
        }

        // Check if timesheet has unitHours, otherwise get from schedule
        if(isset($timesheet->unitHours)){
            $totalHoursUnit = $timesheet->unitHours;
        }else{
            $schedule = ClientUnitSchedule::where('clientUnitId', $booking->unitId)->where('staffCategoryId', $booking->category->categoryId)->where('shiftId', $booking->shift->shiftId)->first();
            if (isset($schedule->totalHoursUnit)) {
                $totalHoursUnit = $schedule->totalHoursUnit;
            } else {
                $totalHoursUnit = 0;
            }
        }
        
        $inTotal = ($payAmountPerHr * $totalHoursUnit) + ($totalHoursUnit * $clientPaymentsEnicRate) + $ta + $additionalExpenseAmount;
        return  number_format($inTotal, 2);
    }

}