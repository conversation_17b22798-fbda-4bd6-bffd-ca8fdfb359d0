<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\ClientUnit;
use App\Models\ClientUnitLogin;
use App\Models\Rights\Module;
use App\Models\Rights\Role;
use App\Models\Rights\UserRole;
use App\Models\User;
use App\Models\WorkflowApprover;
use DataTables;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $isClient = $user->portal_type == Client::class;
        if (request()->ajax()) {

            $users = \App\Models\User::with('portal')->select('users.*')->with('role');
            if ($isClient) {
                $units = ClientUnit::where('clientId', $user->portal_id)->pluck('clientUnitId');
                $users->where(function ($query) use ($user, $units) {
                    $query->where(function ($query) use ($user) {
                        $query->where('portal_type', Client::class)
                            ->where('portal_id', $user->portal_id);
                    })->orWhere(function ($query) use ($user, $units) {
                        $query->where('portal_type', ClientUnit::class)
                            ->whereIn('portal_id', $units);
                    });
                });
            } else {
                $users->where(function ($query) use ($user) {
                    $query->where('portal_type', ClientUnit::class)
                        ->where('portal_id', $user->portal_id);
                });
            }

            return datatables()::of($users)
                ->addColumn('type', function ($user) {
                    return $user->portal_type == "App\Models\Client" ? 'Client' : 'Unit';
                })
                ->addColumn('unit_client', function ($user) {
                    return $user->portal_type != "App\Models\Client" ? optional($user->portal)->name : '';
                })
                ->addColumn('status', function ($user) {
                    return $user->status == 1 ? 'Active' : 'Inactive';
                })
               ->addColumn('actions', function ($user) {
                    return "<a href='#' data-user='" . htmlspecialchars(json_encode($user), ENT_QUOTES, 'UTF-8') . "' data-url='" . route('users.update', $user->id) . "' class='btn btn-three-sm btn-sm edit-user'>Edit</a>
                            <a href='#' data-url='" . route('users.destroy', $user->id) . "' class='btn btn-three-sm delete-user'>Delete</a>";
                })->rawColumns(['actions'])
                ->make(true);
        } else {
            $roles = User::roles()->get();
            $units = [];
            if ($isClient) {
                $units = ClientUnit::where('clientId', $user->portal_id)->get();
            }
            return view('common.users.index', compact('isClient', 'roles', 'units'));
        }
    }
    public function store()
    {
        $data = request()->validate([
            'name' => 'required',
            'type' => 'nullable|in:unit,client',
            'clientUnit' => [
                'required_if:type,unit',
                'nullable',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && !DB::table('client_units')->where('clientUnitId', $value)->exists()) {
                        $fail('The selected client unit is invalid.');
                    }
                },
            ],
            'email' => [
                'required',
                'email',
                Rule::unique('users', 'email')->whereNull('deleted_at'),
            ],
            'password' => 'required',
            'role' => 'required|exists:roles,role_id'
        ]);

        if (!self::checkRoleAccess($data['role'], true)) {
            return redirect()->back()->with('error', 'Permission denied');
        }
        if (\App\Models\User::where('username', $data['email'])->exists()) {
            return redirect()->back()->with('error', 'Username already exists! Try a different email.');
        }

        try {
            DB::beginTransaction();
            $user = auth()->user();
            if(!isset($data['type'])){
                $data['type'] = $user->portal_type == Client::class ? 'client' : 'unit';
                if($data['type'] == 'unit'){
                    $data['clientUnit'] = $user->portal_id;
                }
            }
            $isClient = $user->portal_type == Client::class;
            if ($isClient) {
                if ($data['type'] == 'unit') {
                    //insert entry to ClientUnitLogin
                    $clientUnitLogin = ClientUnitLogin::create([
                        'clientUnitId' => $data['clientUnit'],
                        'name' => $data['name'],
                        'username' => $data['email'],
                        'type' => $data['role'],
                        'status' => true,
                        'password' => bcrypt($data['password'])
                    ]);
                    $data['portal_type'] = $data['type'] == 'unit' ? ClientUnit::class : Client::class;
                    $data['portal_id'] = $data['type'] == 'unit' ? $data['clientUnit'] : $user->portal_id;
                    $data['userable_type'] = $data['type'] == 'unit' ? ClientUnitLogin::class : Client::class;
                    $data['userable_id'] = $data['type'] == 'unit' ? $clientUnitLogin->clientUnitLoginId : $user->portal_id;
                } else {
                    $data['portal_type'] = Client::class;
                    $data['portal_id'] = $user->portal_id;
                    $data['userable_type'] = Client::class;
                    $data['userable_id'] = $user->portal_id;
                }
            } else {
                $clientUnitLogin = ClientUnitLogin::create([
                    'clientUnitId' => $user->portal_id,
                    'name' => $data['name'],
                    'username' => $data['email'],
                    'type' => $data['role'],
                    'status' => true,
                    'password' => bcrypt($data['password'])
                ]);
                $data['portal_type'] =  ClientUnit::class;
                $data['portal_id'] = $user->portal_id;
                $data['userable_type'] =  ClientUnitLogin::class;
                $data['userable_id'] = $clientUnitLogin->clientUnitLoginId;
            }
            $data['password'] = bcrypt($data['password']);
            $data['username'] = $data['email'];
            $newUser = \App\Models\User::create($data);
            UserRole::create([
                'role_id' => $data['role'],
                'user_id' => $newUser->id
            ]);
            DB::commit();
            return redirect()->back()->with('success', 'User created successfully');
        } catch (Exception $e) {
            DB::rollBack();
            dd($e);

            return redirect()->back()->with('error', 'Something went wrong');
        }
    }


    public function update($id)
{

    $data = request()->validate([
        'name' => 'required',
        'type' => 'nullable|in:unit,client',
            'clientUnit' => [
                'required_if:type,unit',
                'nullable',
                function ($attribute, $value, $fail) {
                    if (!empty($value) && !DB::table('client_units')->where('clientUnitId', $value)->exists()) {
                        $fail('The selected client unit is invalid.');
                    }
                },
            ],
        'email' => ['required','email',Rule::unique('users', 'email')->ignore($id)],
        'password' => 'nullable', // Password is optional for update
        'role' => 'required|exists:roles,role_id'
        ]);


    if (!self::checkRoleAccess($data['role'], true)) {
        return redirect()->back()->with('error', 'Permission denied');
    }

    try {
        DB::beginTransaction();
        $user = auth()->user();
        if(!isset($data['type'])){
            $data['type'] = $user->portal_type == Client::class ? 'client' : 'unit';
            if($data['type'] == 'unit'){
                $data['clientUnit'] = $user->portal_id;
            }
        }

        $user = \App\Models\User::findOrFail($id);
        $authUser = auth()->user();
        $isClient = $authUser->portal_type == \App\Models\Client::class;

        // Update password only if provided
        if (!empty($data['password'])) {
            $data['password'] = bcrypt($data['password']);
        } else {
            unset($data['password']); // Avoid updating with null
        }

        if ($isClient) {
            if ($data['type'] == 'unit') {
                // Update or create ClientUnitLogin
                $clientUnitLogin = ClientUnitLogin::where('clientUnitLoginId', $user->userable_id)->first();
                if ($clientUnitLogin) {
                    $clientUnitLogin->update([
                        'clientUnitId' => $data['clientUnit'],
                        'name' => $data['name'],
                        'username' => $data['email'],
                        'type' => $data['role'],
                        'password' => $data['password'] ?? $clientUnitLogin->password
                    ]);
                } else {
                    $clientUnitLogin = ClientUnitLogin::create([
                        'clientUnitId' => $data['clientUnit'],
                        'name' => $data['name'],
                        'username' => $data['email'],
                        'type' => $data['role'],
                        'status' => true,
                        'password' => $data['password'] ?? bcrypt('defaultPass123') // fallback default
                    ]);
                }

                $data['portal_type'] = ClientUnit::class;
                $data['portal_id'] = $data['clientUnit'];
                $data['userable_type'] = ClientUnitLogin::class;
                $data['userable_id'] = $clientUnitLogin->clientUnitLoginId;
            } else {
                $data['portal_type'] = Client::class;
                $data['portal_id'] = $authUser->portal_id;
                $data['userable_type'] = Client::class;
                $data['userable_id'] = $authUser->portal_id;
            }
        } else {
            // Portal is a client unit
            $clientUnitLogin = ClientUnitLogin::where('clientUnitLoginId', $user->userable_id)->first();
            if ($clientUnitLogin) {
                $clientUnitLogin->update([
                    'clientUnitId' => $authUser->portal_id,
                    'name' => $data['name'],
                    'username' => $data['email'],
                    'type' => $data['role'],
                    'password' => $data['password'] ?? $clientUnitLogin->password
                ]);
            }

            $data['portal_type'] = ClientUnit::class;
            $data['portal_id'] = $authUser->portal_id;
            $data['userable_type'] = ClientUnitLogin::class;
            $data['userable_id'] = $clientUnitLogin->clientUnitLoginId ?? $user->userable_id;
        }

        $data['username'] = $data['email'];

        $user->update($data);

        // Update user role
        UserRole::updateOrCreate(
            ['user_id' => $user->id],
            ['role_id' => $data['role']]
        );

        DB::commit();
        return redirect()->back()->with('success', 'User updated successfully');
    } catch (Exception $e) {
        DB::rollBack();
        dd($e);
        return redirect()->back()->with('error', 'Something went wrong');
    }
}
    public function roles()
    {
        $user = auth()->user();
        $isClient = $user->portal_type == Client::class;
        $type = $isClient ? ['client', 'unit'] : ['unit'];
        if (request()->ajax()) {
            $roles =  Role::with('permissions');
            if ($isClient) {
                $units = ClientUnit::where('clientId', $user->portal_id)->pluck('clientUnitId');

                $roles->where(function ($query) use ($units, $user) {
                    $query->where(function ($query) use ($units) {
                        $query->where('type', 'unit')
                            ->where(function ($query) use ($units) {
                                $query->where('type_id', 0)
                                    ->orWhereIn('type_id', $units);
                            });
                    })->orWhere(function ($query) use ($user) {
                        $query->where('type', 'client')
                            ->where(function ($query) use ($user) {
                                $query->where('type_id', 0)
                                    ->orWhere('type_id', $user->portal_id);
                            });
                    });
                });
            } else {
                $roles->where('type', 'unit')->where(function ($query) use ($user) {
                    $query->where('type_id', 0)->orWhere('type_id', $user->portal_id);
                });
            }
            //Get role sql query
            return Datatables::of($roles)
                ->addColumn('actions', function ($role) {
                    if ($role->type_id != 0) {
                        $permissions = json_encode($role->permissions);
                        $url = route('users.roles.update', $role->role_id);
                        $role_data = json_encode($role);
                        return "<a href='#' data-url='{$url}' data-role='{$role_data}' class='btn btn-three-sm edit-role'>Edit</a>
                            <a href='#' data-url='" . route('users.roles.destroy', $role->role_id) . "' class='btn btn-three-sm delete-role'>Delete</a>";
                    } else {
                        return 'N/A';
                    }
                })->rawColumns(['actions'])
                ->make(true);
        } else {
            $modules =  Module::with('submodules.permissions')->whereIn('type', $type)->get();
            $units = [];
            if($isClient){
                $units = ClientUnit::where('clientId', $user->portal_id)->get();
            }
            return view('common.users.roles', compact('modules','isClient','units'));
        }
    }
    public function storeRole()
    {
        $data = request()->validate([
            'name' => 'required',
            'permissions' => 'required|array|min:1', // At least one permission must be selected
            'permissions.*' => 'exists:permissions,permission_id', // Ensure each permission exists in the database
            'type' => 'nullable|in:unit,client',
            'clientUnit' => 'nullable|exists:client_units,clientUnitId',
            'able_to_approve_within_budget' => 'nullable|boolean',
            'able_to_approve_weekly_budget' => 'nullable|boolean',
            'able_to_approve_special_allowance_budget' => 'nullable|boolean',
        ]);

        // Convert checkbox values (null if unchecked becomes false)
        $data['able_to_approve_within_budget'] = $data['able_to_approve_within_budget'] ?? false;
        $data['able_to_approve_weekly_budget'] = $data['able_to_approve_weekly_budget'] ?? false;
        $data['able_to_approve_special_allowance_budget'] = $data['able_to_approve_special_allowance_budget'] ?? false;

        $user = auth()->user();
        if ($user->portal_type == Client::class) {
            if($data['type'] == 'unit'){
                $data['type_id'] = $data['clientUnit'];
            }else{
                $data['type_id'] = $user->portal_id;
            }
        } else {
            $data['type'] = 'unit';
            $data['type_id'] = $user->portal_id;
        }
        $role = Role::create($data);
        $role->permissions()->sync($data['permissions']);
        return redirect()->back()->with('success', 'Role created successfully');
    }
    public static function checkRoleAccess($id, $defaultRole = false)
    {
        $user = auth()->user();
        $role = Role::find($id);
        if ($role->type_id == 0) {
            if ($user->portal_type != Client::class) {
                if (!$defaultRole) {
                    return false;
                } elseif ($role->type == 'unit') {
                    return true;
                }
            } elseif ($defaultRole) {
                return true;
            }
            return false;
        }
        //Check all exist in same user type
        if ($user->portal_type == Client::class) {
            $clients = ClientUnit::where('clientId', $user->portal_id)->pluck('clientUnitId');
            $role = Role::whereIn('type', ['client', 'unit'])->where(function ($query) use ($clients, $user) {
                $query->whereIn('type_id', $clients);
                $query->orWhere('type_id', $user->portal_id);
            })->where('role_id', $id)->first();
            if (!$role) {
                return false;
            }
        } else {
            $role = Role::where('type', 'unit')->orWhere('type_id', $user->portal_id)->where('role_id', $id)->first();
            if (!$role) {
                return false;
            }
        }
        return true;
    }
    public function updateRole($id)
    {
        $data = request()->validate([
            'name' => 'required',
            'permissions' => 'required|array',
            'type' => 'nullable|in:unit,client',
            'clientUnit' => 'nullable|exists:client_units,clientUnitId',
            'able_to_approve_within_budget' => 'nullable|boolean',
            'able_to_approve_weekly_budget' => 'nullable|boolean',
            'able_to_approve_special_allowance_budget' => 'nullable|boolean',
        ]);

        // Convert checkbox values (null if unchecked becomes false)
        $data['able_to_approve_within_budget'] = $data['able_to_approve_within_budget'] ?? false;
        $data['able_to_approve_weekly_budget'] = $data['able_to_approve_weekly_budget'] ?? false;
        $data['able_to_approve_special_allowance_budget'] = $data['able_to_approve_special_allowance_budget'] ?? false;

        $role = Role::find($id);
        if (!self::checkRoleAccess($id)) {
            return response()->json(['message' => 'Permission denied'], 422);
        }
        if(auth()->user()->portal_type == Client::class){
            $data['type_id'] = $data['type'] == 'unit' ? $data['clientUnit'] : auth()->user()->portal_id;
        }

        // Update role with all fields including budget approval fields
        $role->update([
            'name' => $data['name'],
            'able_to_approve_within_budget' => $data['able_to_approve_within_budget'],
            'able_to_approve_weekly_budget' => $data['able_to_approve_weekly_budget'],
            'able_to_approve_special_allowance_budget' => $data['able_to_approve_special_allowance_budget']
        ]);

        $role->permissions()->sync($data['permissions']);
        return redirect()->back()->with('success', 'Role updated successfully');
    }
    public function destroyRole($id)
    {
        //delete roles and permissions and also user have already the role assigned
        $workflowExist = WorkflowApprover::where('role_id', $id)->exists();
        $userExist =  UserRole::whereHas('User')->where('role_id', $id)->exists();

        if ($userExist || $workflowExist) {
            return response()->json(['message' => 'Role cannot be deleted as it is already assigned to a user/workflow'], 422);
        }
        $role = Role::find($id);
        if (!self::checkRoleAccess($id)) {
            return response()->json(['message' => 'Permission denied'], 422);
        }

        $role->permissions()->sync([]);
        $role->delete();
        //delete Role and its assigned permissions
        return response()->json(['message' => 'Role deleted successfully']);
    }
    public function destroy($id)
    {
        $user = \App\Models\User::findOrFail($id);
        if ($user->portal_type == ClientUnit::class) {
            $clientUnitLogin = ClientUnitLogin::where('clientUnitLoginId', $user->userable_id)->first();
            if ($clientUnitLogin) {
                $clientUnitLogin->delete();
            }
        }
        $user->delete();
        return response()->json(['message' => 'User deleted successfully']);
    }
}
