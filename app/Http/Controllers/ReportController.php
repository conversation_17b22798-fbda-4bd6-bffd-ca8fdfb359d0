<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Booking;
use App\Models\ClientUnit;
use App\Models\Shift;
use App\Models\StaffCategory;
use App\Models\Staff;
use Yajra\DataTables\Facades\DataTables;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

class ReportController extends Controller
{
    /**
     * Display the shift cover report page
     */
    public function shiftCoverReport(Request $request)
    {
        // Get authenticated user
        $user = Auth::user();

        // Get units belonging to the logged-in client only
        $units = ClientUnit::where('status', 1)
            ->where('clientId', $user->portal_id)
            ->select('clientUnitId', 'name', 'alias')
            ->orderBy('name')
            ->get();

        return view('reports.shift_cover_report', compact('units'));
    }

    /**
     * Get shift cover report data for DataTables
     */
    public function getShiftCoverReportData(Request $request)
    {
        // Validate request parameters
        $request->validate([
            'unit_id' => 'nullable|integer|exists:client_units,clientUnitId',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        // Get authenticated user
        $user = Auth::user();

        // Build the query with eager loading
        $query = Booking::with([
            'shift:shiftId,name',
            'category:categoryId,name',
            'staff:staffId,forname,middle_name,surname',
            'unit:clientUnitId,name'
        ])
        ->select([
            'bookingId',
            'date',
            'shiftId',
            'categoryId',
            'staffId',
            'unitId',
            'start_time',
            'end_time',
            'created_at',
            'reason',
            'initial_cost',
            'unit_final_cost'
        ]);

        // Filter by client's units only using whereHas relationship
        $query->whereHas('unit', function ($unitQuery) use ($user) {
            $unitQuery->where('clientId', $user->portal_id)
                     ->where('status', 1);
        });

        // Apply additional filters
        if ($request->filled('unit_id')) {
            $query->where('unitId', $request->unit_id);
        }

        if ($request->filled('start_date')) {
            $query->where('date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('date', '<=', $request->end_date);
        }

        // Only include confirmed bookings
        $query->whereIn('unitStatus', [1, 4]);

        return DataTables::eloquent($query)
            ->addColumn('account_code', function ($booking) {
                return '607000'; // Static value as requested
            })
            ->editColumn('date', function ($booking) {
                return $booking->date ? Carbon::parse($booking->date)->format('n/j/Y') : '';
            })
            ->addColumn('shift_name', function ($booking) {
                return $booking->shift ? $booking->shift->name : '';
            })
            ->addColumn('category_name', function ($booking) {
                return $booking->category ? $booking->category->name : '';
            })
            ->addColumn('staff_name', function ($booking) {
                if (!$booking->staff) return '';

                $parts = array_filter([
                    $booking->staff->forname,
                    $booking->staff->middle_name,
                    $booking->staff->surname
                ]);

                return implode(' ', $parts);
            })
            ->editColumn('start_time', function ($booking) {
                return $booking->start_time ? Carbon::parse($booking->start_time)->format('H:i') : '';
            })
            ->editColumn('end_time', function ($booking) {
                return $booking->end_time ? Carbon::parse($booking->end_time)->format('H:i') : '';
            })
            ->addColumn('hours_worked', function ($booking) {
                if (!$booking->start_time || !$booking->end_time) return '0.00';

                $start = Carbon::parse($booking->start_time);
                $end = Carbon::parse($booking->end_time);

                // Handle overnight shifts
                if ($end->lt($start)) {
                    $end->addDay();
                }

                $hours = $end->diffInMinutes($start) / 60;

                // Ensure hours is never negative
                $hours = max(0, $hours);

                return number_format($hours, 2);
            })
            ->editColumn('created_at', function ($booking) {
                return $booking->created_at ? $booking->created_at->format('n/j/Y') : '';
            })
            ->addColumn('reason_text', function ($booking) {
                return $this->getReasonText($booking->reason);
            })
            ->editColumn('initial_cost', function ($booking) {
                return $booking->initial_cost ? '£' . number_format($booking->initial_cost, 2) : '£0.00';
            })
            ->editColumn('unit_final_cost', function ($booking) {
                return $booking->unit_final_cost ? '£' . number_format($booking->unit_final_cost, 2) : '£0.00';
            })
            ->orderColumn('bookingId', function ($query, $order) {
                return $query->orderBy('bookingId', $order);
            })
            ->orderColumn('date', function ($query, $order) {
                return $query->orderBy('date', $order);
            })
            // Note: Sorting disabled for relationship columns to avoid JOIN issues
            // Users can still search and filter these columns
            ->orderColumn('start_time', function ($query, $order) {
                return $query->orderBy('start_time', $order);
            })
            ->orderColumn('end_time', function ($query, $order) {
                return $query->orderBy('end_time', $order);
            })
            ->orderColumn('created_at', function ($query, $order) {
                return $query->orderBy('created_at', $order);
            })
            ->orderColumn('reason_text', function ($query, $order) {
                return $query->orderBy('reason', $order);
            })
            ->orderColumn('initial_cost', function ($query, $order) {
                return $query->orderBy('initial_cost', $order);
            })
            ->orderColumn('unit_final_cost', function ($query, $order) {
                return $query->orderBy('unit_final_cost', $order);
            })
            ->filterColumn('shift_name', function ($query, $keyword) {
                $query->whereHas('shift', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            })
            ->filterColumn('category_name', function ($query, $keyword) {
                $query->whereHas('category', function ($q) use ($keyword) {
                    $q->where('name', 'like', "%{$keyword}%");
                });
            })
            ->filterColumn('staff_name', function ($query, $keyword) {
                $query->whereHas('staff', function ($q) use ($keyword) {
                    $q->where(function ($subQuery) use ($keyword) {
                        $subQuery->where('forname', 'like', "%{$keyword}%")
                                ->orWhere('middle_name', 'like', "%{$keyword}%")
                                ->orWhere('surname', 'like', "%{$keyword}%")
                                ->orWhere(DB::raw("CONCAT(forname, ' ', IFNULL(middle_name, ''), ' ', surname)"), 'like', "%{$keyword}%");
                    });
                });
            })
            ->make(true);
    }

    /**
     * Export shift cover report data
     */
    public function exportShiftCoverReport(Request $request)
    {
        // Validate request parameters
        $request->validate([
            'unit_id' => 'nullable|integer|exists:client_units,clientUnitId',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'export' => 'required|in:excel,pdf'
        ]);

        // Get authenticated user
        $user = Auth::user();

        // Build the query with eager loading
        $query = Booking::with([
            'shift:shiftId,name',
            'category:categoryId,name',
            'staff:staffId,forname,middle_name,surname',
            'unit:clientUnitId,name'
        ])
        ->select([
            'bookingId',
            'date',
            'shiftId',
            'categoryId',
            'staffId',
            'unitId',
            'start_time',
            'end_time',
            'created_at',
            'reason',
            'initial_cost',
            'unit_final_cost'
        ]);

        // Filter by client's units only using whereHas relationship
        $query->whereHas('unit', function ($unitQuery) use ($user) {
            $unitQuery->where('clientId', $user->portal_id)
                     ->where('status', 1);
        });

        // Apply additional filters
        if ($request->filled('unit_id')) {
            $query->where('unitId', $request->unit_id);
        }

        if ($request->filled('start_date')) {
            $query->where('date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('date', '<=', $request->end_date);
        }

        // Only include confirmed bookings
        $query->whereIn('unitStatus', [1, 4]);

        // Get the data
        $bookings = $query->orderBy('date', 'desc')->get();

        // Transform data for export
        $exportData = $bookings->map(function ($booking) {
            $staffName = '';
            if ($booking->staff) {
                $parts = array_filter([
                    $booking->staff->forname,
                    $booking->staff->middle_name,
                    $booking->staff->surname
                ]);
                $staffName = implode(' ', $parts);
            }

            $hoursWorked = '0.00';
            if ($booking->start_time && $booking->end_time) {
                $start = Carbon::parse($booking->start_time);
                $end = Carbon::parse($booking->end_time);

                if ($end->lt($start)) {
                    $end->addDay();
                }

                $hours = $end->diffInMinutes($start) / 60;

                // Ensure hours is never negative
                $hours = max(0, $hours);

                $hoursWorked = number_format($hours, 2);
            }

            return [
                'Booking ID' => $booking->bookingId,
                'Account Code' => '607000',
                'Date' => $booking->date ? Carbon::parse($booking->date)->format('n/j/Y') : '',
                'Shift' => $booking->shift ? $booking->shift->name : '',
                'Category' => $booking->category ? $booking->category->name : '',
                'Staff' => $staffName,
                'Start Time' => $booking->start_time ? Carbon::parse($booking->start_time)->format('H:i') : '',
                'End Time' => $booking->end_time ? Carbon::parse($booking->end_time)->format('H:i') : '',
                'Hours' => $hoursWorked,
                'Shift Booked On' => $booking->created_at ? $booking->created_at->format('n/j/Y') : '',
                'Reason for Booking' => $this->getReasonText($booking->reason),
                'Initial Amount' => $booking->initial_cost ? '£' . number_format($booking->initial_cost, 2) : '£0.00',
                'Final Amount' => $booking->unit_final_cost ? '£' . number_format($booking->unit_final_cost, 2) : '£0.00',
            ];
        });

        // Generate filename
        $filename = 'shift_cover_report_' . date('Y-m-d_H-i-s');

        if ($request->export === 'excel') {
            return $this->exportToExcel($exportData, $filename);
        } else {
            return $this->exportToPDF($exportData, $filename);
        }
    }

    /**
     * Export to Excel
     */
    private function exportToExcel($data, $filename)
    {
        // Simple CSV export (can be enhanced with PhpSpreadsheet)
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '.csv"',
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');

            // Add headers
            if ($data->isNotEmpty()) {
                fputcsv($file, array_keys($data->first()));
            }

            // Add data
            foreach ($data as $row) {
                fputcsv($file, $row);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export to PDF
     */
    private function exportToPDF($data, $filename)
    {
        // Simple HTML to PDF conversion (can be enhanced with DomPDF or similar)
        $html = '<h1>Shift Cover Report</h1>';
        $html .= '<table border="1" cellpadding="5" cellspacing="0" style="width:100%; border-collapse: collapse;">';

        // Add headers
        if ($data->isNotEmpty()) {
            $html .= '<thead><tr>';
            foreach (array_keys($data->first()) as $header) {
                $html .= '<th>' . htmlspecialchars($header) . '</th>';
            }
            $html .= '</tr></thead>';
        }

        // Add data
        $html .= '<tbody>';
        foreach ($data as $row) {
            $html .= '<tr>';
            foreach ($row as $cell) {
                $html .= '<td>' . htmlspecialchars($cell) . '</td>';
            }
            $html .= '</tr>';
        }
        $html .= '</tbody></table>';

        return response($html)
            ->header('Content-Type', 'text/html')
            ->header('Content-Disposition', 'attachment; filename="' . $filename . '.html"');
    }

    /**
     * Convert reason enum to text
     */
    private function getReasonText($reason)
    {
        $reasons = [
            1 => 'Staff Sickness',
            2 => 'Holiday cover',
            3 => 'Vacant Position',
            4 => 'New Resident admission',
            5 => '1 to 1 care',
            6 => 'Extra staff requirement',
            7 => 'Management request'
        ];

        return $reasons[$reason] ?? 'Unknown';
    }
}
