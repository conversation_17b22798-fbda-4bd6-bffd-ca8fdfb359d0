<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Storage;

class InvoiceUnit extends Model
{
    protected $fillable = [
        'invoice_number', 'amount', 'invoice_date','due_date','unit_id','client_id','file','year','month','status','week','excel','weekend'
    ];

	protected $primaryKey = 'invoice_id';

    public function client(){
      return $this->hasOne(Client::class,'clientId','client_id')->withDefault([
        'name' =>''
      ]);
    }

    public function unit(){
      return $this->hasOne(ClientUnit::class,'clientUnitId','unit_id')->withDefault([
        'name' =>''
      ]);
    }

    public function getHumanDateInvoiceAttribute(){
      return date('d-m-Y',strtotime($this->invoice_date));
    }

    public function getHumanDateDueAttribute(){
      return date('d-m-Y',strtotime($this->invoice_date));
    }

    public function getPdfUrlAttribute(){
      return "https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/invoice_files/".$this->file;


    }

    protected $appends = ['pdf_url','human_date_invoice','human_date_due'];
}
