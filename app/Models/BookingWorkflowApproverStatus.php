<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BookingWorkflowApproverStatus extends Model
{
    use HasFactory;

    public const APPROVED = 'approved';
    public const DECLINED = 'declined';

    protected $table = 'bookings_workflow_approvers_status';

    protected $fillable = [
        'workflow_id',
        'workflow_approvers_id',
        'booking_id',
        'approval_status',
        'notes',
        'user_id',
    ];

    public function workflowApprover()
    {
        return $this->belongsTo(WorkflowApprover::class, 'workflow_approvers_id');
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }


    protected static function booted()
    {
        static::saved(function ($status) {
            $bookingId = $status->booking_id;
            $workflowId = $status->workflow_id;

            // Get all approvers for this workflow
            $bookingWorkflow = BookingWorkflow::where('booking_id', $bookingId)
                ->where('workflow_id', $workflowId)
                ->first();

            $allApprovers = $bookingWorkflow->workflow->get('approvers');
            $allApprovers = array_map(function ($approver) {
                return (object) $approver;
            }, $allApprovers);
            $allApprovers = collect($allApprovers);

            // Get all statuses for this booking/workflow
            $statuses = self::where('booking_id', $bookingId)
                ->where('workflow_id', $workflowId)
                ->get();

            $mandatoryApprovers = $allApprovers->where('is_mandatory', true);
            $approvedStatuses = $statuses->where('approval_status', self::APPROVED);
            $declinedStatuses = $statuses->where('approval_status', self::DECLINED);

            $isComplete = false;
            $isDeclined = false;

            // Check if any mandatory approver has declined
            $isDeclined = $mandatoryApprovers->contains(function ($approver) use ($declinedStatuses) {
                return $declinedStatuses->contains('workflow_approvers_id', $approver->id);
            });

            // ✅ Additional condition: if no mandatory approvers and anyone declined
            if ($mandatoryApprovers->count() === 0 && $declinedStatuses->isNotEmpty()) {
                $isDeclined = true;
            }


            if ($isDeclined) {
                // 🔁 Mark this workflow as declined
                $client_status = BookingWorkflow::where('booking_id', $bookingId)
                    ->where('workflow_id', $workflowId)
                    ->first();

                if ($client_status) {
                    $client_status->approval_status = self::DECLINED;
                    $client_status->save();
                }
            } else {
                // ✅ Check if workflow can be approved
                if ($mandatoryApprovers->count() === $allApprovers->count()) {
                    // Case 1: All are mandatory – all must approve
                    $isComplete = $mandatoryApprovers->every(function ($approver) use ($approvedStatuses) {
                        return $approvedStatuses->contains('workflow_approvers_id', $approver->id);
                    });
                } elseif ($mandatoryApprovers->count() === 1) {
                    // Case 2: One mandatory – only that one must approve
                    $mandatoryApprover = $mandatoryApprovers->first();
                    $isComplete = $approvedStatuses->contains('workflow_approvers_id', $mandatoryApprover->id);
                } elseif ($mandatoryApprovers->count() === 0) {
                    // Case 3: No mandatory – any approver approval completes it
                    $isComplete = $approvedStatuses->isNotEmpty();
                }

                if ($isComplete) {
                    $client_status = BookingWorkflow::where('booking_id', $bookingId)
                        ->where('workflow_id', $workflowId)
                        ->first();

                    if ($client_status) {
                        $client_status->approval_status = self::APPROVED;
                        $client_status->save();
                    }
                }
            }

            // ✅ Evaluate overall booking status
            $allStatus = BookingWorkflow::where('booking_id', $bookingId)->get();

            $totalCount = $allStatus->count();
            $approvedCount = $allStatus->where('approval_status', self::APPROVED)->count();
            $declinedCount = $allStatus->where('approval_status', self::DECLINED)->count();

            $allApproved = false;
            $pending = true;


            if ($totalCount === $approvedCount) {
                $allApproved = true;
                $pending = false;
            } elseif ($declinedCount > 0) {
                $allApproved = false;
                $pending = false;
            }


            if (!$pending) {
                $booking = Booking::find($bookingId);
                if ($booking) {
                    $currentBudgetStatus = $booking->getBudgetStatus();
                    if($currentBudgetStatus != Booking::BUDGET_STATUS_EMERGENCY){
                        $approvers =  $bookingWorkflow->workflow->get('approvers');
                        $approvers = array_map(function ($approver) {
                            return (object) $approver;
                        }, $approvers);
                        $approvers = collect($approvers);

                        // Get the actual role IDs from approvers
                        $roleIds = $approvers->pluck('role_id')->unique();

                        // Get the actual Role models to check permissions
                        $roles = \App\Models\Rights\Role::whereIn('role_id', $roleIds)->get();

                        $allStatus = BookingWorkflowApproverStatus::where('booking_id', $bookingId)
                            ->with('user.role')
                            ->get();
                        //Need to get all approved users roles

                        if(Booking::BUDGET_STATUS_WITHIN_WEEKLY_BUDGET == $currentBudgetStatus){
                            $checkExistingWorkflowHaveRole = $roles->every(function ($role) {
                                return $role->able_to_approve_within_budget;
                            });

                            if(!$checkExistingWorkflowHaveRole){
                                // Check if any users with the required role have already approved
                                $requiredRoles = \App\Models\Rights\Role::where('able_to_approve_within_budget', true)
                                    ->where(function($query) use ($booking) {
                                        $query->where(function($subQuery) use ($booking) {
                                            $subQuery->where('type', 'unit')
                                                     ->where('type_id', $booking->unitId)
                                                     ->orWhere('type_id', 0); // Global unit roles
                                        });
                                        // Also include client roles if booking has a client
                                        if ($booking->unit && $booking->unit->client) {
                                            $query->orWhere(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'client')
                                                         ->where('type_id', $booking->unit->client->clientId)
                                                         ->orWhere('type_id', 0); // Global client roles
                                            });
                                        }
                                    })
                                    ->pluck('role_id');

                                // Check if any user with required role has already approved this booking
                                $hasAlreadyApproved = $allStatus->filter(function ($status) use ($requiredRoles) {
                                    if ($status->approval_status !== self::APPROVED) {
                                        return false;
                                    }

                                    // Check if user exists and has role
                                    if (!$status->user || !$status->user->role) {
                                        return false;
                                    }

                                    return $requiredRoles->contains($status->user->role->role_id);
                                })->isNotEmpty();

                                // If no user with required role has approved, add the additional approver
                                if (!$hasAlreadyApproved) {
                                    // Find a role that can approve within budget (prioritize unit roles, then client roles)
                                    $budgetApprovalRole = \App\Models\Rights\Role::where('able_to_approve_within_budget', true)
                                        ->where(function($query) use ($booking) {
                                            $query->where(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'unit')
                                                         ->where('type_id', $booking->unitId)
                                                         ->orWhere('type_id', 0); // Global unit roles
                                            });
                                            // Also include client roles if booking has a client
                                            if ($booking->unit && $booking->unit->client) {
                                                $query->orWhere(function($subQuery) use ($booking) {
                                                    $subQuery->where('type', 'client')
                                                             ->where('type_id', $booking->unit->client->clientId)
                                                             ->orWhere('type_id', 0); // Global client roles
                                                });
                                            }
                                        })
                                        ->first();

                                    if ($budgetApprovalRole) {
                                        // Get current workflow data from booking_workflow
                                        $workflowData = $bookingWorkflow->workflow;
                                        $currentApprovers = $workflowData->get('approvers', []);

                                        // Find max approval_order
                                        $maxOrder = 0;
                                        foreach ($currentApprovers as $approver) {
                                            if (isset($approver['approval_order']) && $approver['approval_order'] > $maxOrder) {
                                                $maxOrder = $approver['approval_order'];
                                            }
                                        }

                                        // Generate new ID for the approver
                                        $newApproverId = count($currentApprovers) + 1;

                                        // Add the budget approval role to the workflow approvers JSON
                                        $currentApprovers[] = [
                                            'id' => $newApproverId,
                                            'role' => [
                                                'name' => $budgetApprovalRole->name,
                                                'type' => $budgetApprovalRole->type,
                                                'status' => $budgetApprovalRole->status,
                                                'role_id' => $budgetApprovalRole->role_id,
                                                'type_id' => $budgetApprovalRole->type_id,
                                                'created_at' => $budgetApprovalRole->created_at,
                                                'deleted_at' => $budgetApprovalRole->deleted_at,
                                                'updated_at' => $budgetApprovalRole->updated_at,
                                            ],
                                            'role_id' => $budgetApprovalRole->role_id,
                                            'created_at' => now(),
                                            'deleted_at' => null,
                                            'updated_at' => now(),
                                            'workflow_id' => $workflowId,
                                            'is_mandatory' => true,
                                            'approval_order' => $maxOrder + 1,
                                            'additional_conditions' => 'able_to_approve_within_budget',
                                        ];

                                        // Update the workflow data
                                        $workflowData->put('approvers', $currentApprovers);
                                        $bookingWorkflow->workflow = $workflowData;
                                        $bookingWorkflow->save();

                                        // Don't set booking to approved yet - need budget approval first
                                        return;
                                    }
                                }
                            }
                        } elseif(Booking::BUDGET_STATUS_WITHIN_WEEKLY_BUDGET_ALLOWANCE == $currentBudgetStatus){
                            $checkExistingWorkflowHaveRole = $roles->every(function ($role) {
                                return $role->able_to_approve_weekly_budget;
                            });

                            if(!$checkExistingWorkflowHaveRole){
                                // Check if any users with the required role have already approved
                                $requiredRoles = \App\Models\Rights\Role::where('able_to_approve_weekly_budget', true)
                                    ->where(function($query) use ($booking) {
                                        $query->where(function($subQuery) use ($booking) {
                                            $subQuery->where('type', 'unit')
                                                     ->where('type_id', $booking->unitId)
                                                     ->orWhere('type_id', 0); // Global unit roles
                                        });
                                        // Also include client roles if booking has a client
                                        if ($booking->unit && $booking->unit->client) {
                                            $query->orWhere(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'client')
                                                         ->where('type_id', $booking->unit->client->clientId)
                                                         ->orWhere('type_id', 0); // Global client roles
                                            });
                                        }
                                    })
                                    ->pluck('role_id');

                                // Check if any user with required role has already approved this booking
                                $hasAlreadyApproved = $allStatus->filter(function ($status) use ($requiredRoles) {
                                    if ($status->approval_status !== self::APPROVED) {
                                        return false;
                                    }

                                    // Check if user exists and has role
                                    if (!$status->user || !$status->user->role) {
                                        return false;
                                    }

                                    return $requiredRoles->contains($status->user->role->role_id);
                                })->isNotEmpty();

                                // If no user with required role has approved, add the additional approver
                                if (!$hasAlreadyApproved) {
                                    // Find a role that can approve weekly budget (prioritize unit roles, then client roles)
                                    $weeklyBudgetApprovalRole = \App\Models\Rights\Role::where('able_to_approve_weekly_budget', true)
                                        ->where(function($query) use ($booking) {
                                            $query->where(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'unit')
                                                         ->where('type_id', $booking->unitId)
                                                         ->orWhere('type_id', 0); // Global unit roles
                                            });
                                            // Also include client roles if booking has a client
                                            if ($booking->unit && $booking->unit->client) {
                                                $query->orWhere(function($subQuery) use ($booking) {
                                                    $subQuery->where('type', 'client')
                                                             ->where('type_id', $booking->unit->client->clientId)
                                                             ->orWhere('type_id', 0); // Global client roles
                                                });
                                            }
                                        })
                                        ->first();

                                    if ($weeklyBudgetApprovalRole) {
                                        // Get current workflow data from booking_workflow
                                        $workflowData = $bookingWorkflow->workflow;
                                        $currentApprovers = $workflowData->get('approvers', []);

                                        // Find max approval_order
                                        $maxOrder = 0;
                                        foreach ($currentApprovers as $approver) {
                                            if (isset($approver['approval_order']) && $approver['approval_order'] > $maxOrder) {
                                                $maxOrder = $approver['approval_order'];
                                            }
                                        }

                                        // Generate new ID for the approver
                                        $newApproverId = count($currentApprovers) + 1;

                                        // Add the weekly budget approval role to the workflow approvers JSON
                                        $currentApprovers[] = [
                                            'id' => $newApproverId,
                                            'role_id' => $weeklyBudgetApprovalRole->role_id,
                                            'approval_order' => $maxOrder + 1,
                                            'is_mandatory' => true,
                                            'additional_conditions' => 'able_to_approve_weekly_budget',
                                        ];

                                        // Update the workflow data
                                        $workflowData->put('approvers', $currentApprovers);
                                        $bookingWorkflow->workflow = $workflowData;
                                        $bookingWorkflow->save();

                                        // Don't set booking to approved yet - need weekly budget approval first
                                        return;
                                    }
                                }
                            }
                        } elseif(in_array($currentBudgetStatus, [
                            Booking::BUDGET_STATUS_WITHIN_WEEKLY_BUDGET_ALLOWANCE_SPECIAL,
                            Booking::BUDGET_STATUS_NO_SPECIAL_ALLOWANCE,
                            Booking::BUDGET_STATUS_NO_BUDGET
                        ])){
                            $checkExistingWorkflowHaveRole = $roles->every(function ($role) {
                                return $role->able_to_approve_special_allowance_budget;
                            });

                            if(!$checkExistingWorkflowHaveRole){
                                // Check if any users with the required role have already approved
                                $requiredRoles = \App\Models\Rights\Role::where('able_to_approve_special_allowance_budget', true)
                                    ->where(function($query) use ($booking) {
                                        $query->where(function($subQuery) use ($booking) {
                                            $subQuery->where('type', 'unit')
                                                     ->where('type_id', $booking->unitId)
                                                     ->orWhere('type_id', 0); // Global unit roles
                                        });
                                        // Also include client roles if booking has a client
                                        if ($booking->unit && $booking->unit->client) {
                                            $query->orWhere(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'client')
                                                         ->where('type_id', $booking->unit->client->clientId)
                                                         ->orWhere('type_id', 0); // Global client roles
                                            });
                                        }
                                    })
                                    ->pluck('role_id');

                                // Check if any user with required role has already approved this booking
                                $hasAlreadyApproved = $allStatus->filter(function ($status) use ($requiredRoles) {
                                    if ($status->approval_status !== self::APPROVED) {
                                        return false;
                                    }

                                    // Check if user exists and has role
                                    if (!$status->user || !$status->user->role) {
                                        return false;
                                    }

                                    return $requiredRoles->contains($status->user->role->role_id);
                                })->isNotEmpty();

                                // If no user with required role has approved, add the additional approver
                                if (!$hasAlreadyApproved) {
                                    // Find a role that can approve special allowance budget (prioritize unit roles, then client roles)
                                    $specialAllowanceApprovalRole = \App\Models\Rights\Role::where('able_to_approve_special_allowance_budget', true)
                                        ->where(function($query) use ($booking) {
                                            $query->where(function($subQuery) use ($booking) {
                                                $subQuery->where('type', 'unit')
                                                         ->where('type_id', $booking->unitId)
                                                         ->orWhere('type_id', 0); // Global unit roles
                                            });
                                            // Also include client roles if booking has a client
                                            if ($booking->unit && $booking->unit->client) {
                                                $query->orWhere(function($subQuery) use ($booking) {
                                                    $subQuery->where('type', 'client')
                                                             ->where('type_id', $booking->unit->client->clientId)
                                                             ->orWhere('type_id', 0); // Global client roles
                                                });
                                            }
                                        })
                                        ->first();

                                    if ($specialAllowanceApprovalRole) {
                                        // Get current workflow data from booking_workflow
                                        $workflowData = $bookingWorkflow->workflow;
                                        $currentApprovers = $workflowData->get('approvers', []);

                                        // Find max approval_order
                                        $maxOrder = 0;
                                        foreach ($currentApprovers as $approver) {
                                            if (isset($approver['approval_order']) && $approver['approval_order'] > $maxOrder) {
                                                $maxOrder = $approver['approval_order'];
                                            }
                                        }

                                        // Generate new ID for the approver
                                        $newApproverId = count($currentApprovers) + 1;

                                        // Add the special allowance approval role to the workflow approvers JSON
                                        $currentApprovers[] = [
                                            'id' => $newApproverId,
                                            'role_id' => $specialAllowanceApprovalRole->role_id,
                                            'approval_order' => $maxOrder + 1,
                                            'is_mandatory' => true,
                                            'additional_conditions' => 'able_to_approve_special_allowance_budget',
                                        ];

                                        // Update the workflow data
                                        $workflowData->put('approvers', $currentApprovers);
                                        $bookingWorkflow->workflow = $workflowData;
                                        $bookingWorkflow->save();

                                        // Don't set booking to approved yet - need special allowance approval first
                                        return;
                                    }
                                }
                            }
                        }

                        $booking->unitStatus = $allApproved ? 4 : 2; // 4 = Approved, 2 = Declined
                        $booking->save();
                    } else {
                        $booking->unitStatus = 4;
                        $booking->save();
                    }
                }
            }
        });
    }
}
