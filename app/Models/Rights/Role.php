<?php

namespace App\Models\Rights;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;


class Role extends Model
{
    use SoftDeletes;
    protected $primaryKey = 'role_id';

    protected $fillable = [
        'name',
        'status',
        'type',
        'type_id',
        'able_to_approve_within_budget',
        'able_to_approve_weekly_budget',
        'able_to_approve_special_allowance_budget'
    ];

    // Permissions through RolePermission
    public function permissions()
    {
        return $this->belongsToMany(Permission::class, 'role_permissions', 'role_id', 'permission_id');

    }
}
