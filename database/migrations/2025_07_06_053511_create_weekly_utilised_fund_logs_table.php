<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('weekly_utilised_fund_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('client_unit_weekly_budget_id');
            $table->decimal('adjustment_amount', 10, 2);
            $table->enum('adjustment_type', ['addition', 'deduction']);
            $table->string('source_type')->nullable(); // e.g., 'App\Models\Booking'
            $table->unsignedBigInteger('source_id')->nullable(); // e.g., booking ID
            $table->text('description')->nullable();
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('client_unit_weekly_budget_id', 'wufl_weekly_budget_fk')
                  ->references('id')
                  ->on('client_unit_weekly_budgets')
                  ->onDelete('cascade');

            // Index for performance
            //$table->index(['client_unit_weekly_budget_id', 'created_at']);
            //$table->index(['source_type', 'source_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('weekly_utilised_fund_logs');
    }
};
