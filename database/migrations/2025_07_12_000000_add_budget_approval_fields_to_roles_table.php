<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->boolean('able_to_approve_within_budget')->default(false)->after('status');
            $table->boolean('able_to_approve_weekly_budget')->default(false)->after('able_to_approve_within_budget');
            $table->boolean('able_to_approve_special_allowance_budget')->default(false)->after('able_to_approve_weekly_budget');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('roles', function (Blueprint $table) {
            $table->dropColumn([
                'able_to_approve_within_budget',
                'able_to_approve_weekly_budget',
                'able_to_approve_special_allowance_budget'
            ]);
        });
    }
};
