<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('staff_categories', function (Blueprint $table) {
            $table->text('account_code')->nullable()->after('colorCode'); // adjust 'after' if needed
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('staff_categories', function (Blueprint $table) {
                        $table->dropColumn('account_code');
        });
    }
};
