<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            // You must re-declare the column type when changing a comment
            $table->tinyInteger('status')
                ->default(0)
                ->comment('0: Unpaid, 1: Paid, 3: Overdue')
                ->change();
        });
    }

    public function down(): void
    {
        Schema::table('payments', function (Blueprint $table) {
            $table->tinyInteger('status')
                ->default(0)
                ->comment('0: Unpaid, 1: Paid')
                ->change();
        });
    }
};
