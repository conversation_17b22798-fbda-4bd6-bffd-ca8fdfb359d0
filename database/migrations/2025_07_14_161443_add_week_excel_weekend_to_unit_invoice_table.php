<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('invoice_units', function (Blueprint $table) {
            $table->integer('week')->nullable()->after('month');
            $table->string('excel')->nullable()->after('week');
            $table->date('weekend')->nullable()->after('excel');

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('invoice_units', function (Blueprint $table) {
            $table->dropColumn(['week', 'excel', 'weekend']);
        });
    }
};
