<?php

use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\ClientPortal\BudgetController;
use App\Http\Controllers\ClientPortal\DashboardController;
use App\Http\Controllers\ClientPortal\InvoiceUnitController;
use App\Http\Controllers\ClientPortal\UnitlistController;
use App\Http\Controllers\UnitPortal\DashboardController as UnitDashboardController;
use App\Http\Controllers\UnitPortal\BookingController;
use App\Http\Controllers\UnitPortal\BookedShiftController;
use App\Http\Controllers\UnitPortal\ManagementReportController;
use App\Http\Controllers\ClientPortal\TimesheetController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\WorkflowController;
use App\Http\Controllers\ExecuteArtisanCommandController;
use App\Http\Controllers\ClientPortal\BudgetAllocationController;
use App\Http\Controllers\ClientPortal\IncidentLogController;
use App\Http\Controllers\ConfigurationController;
use App\Http\Controllers\AuditController;

Route::get('/workflows', [WorkflowController::class, 'index'])->name('workflows.index');
Route::get('/workflows/create', [WorkflowController::class, 'create'])->name('workflows.create');
Route::post('/workflows', [WorkflowController::class, 'store'])->name('workflows.store');
Route::get('/workflows/{id}/edit', [WorkflowController::class, 'edit'])->name('workflows.edit');
Route::put('/workflows/{id}', [WorkflowController::class, 'update'])->name('workflows.update');
Route::delete('/workflows/{id}', [WorkflowController::class, 'destroy'])->name('workflows.destroy');
Route::post('workflows/approve', [WorkflowController::class, 'approve'])->name('workflows.approve');

Route::post('login', [AuthController::class, 'login'])->name('login');
Route::get('login', [AuthController::class, 'index'])->name('login');
//Auth Group
Route::middleware('auth')->group(function () {
Route::get('/', function () {
    return view('unitPortal.login');
});
//UNIT PORTAL ROUTES
Route::get('unit-dashboard',[UnitDashboardController::class,'dashboard'])->name('unit-dashboard');
// Route::get('budget-view', [BudgetController::class, 'budgetView'])->name('budgetView');
Route::get('budget-view', [\App\Http\Controllers\ClientPortal\BudgetReportController::class, 'index'])->name('budget.report');

Route::get('bookings', [BookingController::class, 'list'])->name('bookings');
Route::post('save', [BookingController::class, 'save'])->name('save');
Route::post('get-booking',[BookedShiftController::class,'getBooking'])->name('get.booking');
Route::get('booked-shifts',[BookedShiftController::class,'list'])->name('booked.shifts');
Route::post('get-timings',[BookingController::class,'getTimings'])->name('get.timings');
Route::post('get-unit-chat',[BookedShiftController::class,'getUnitChat'])->name('get.unit.chat');
Route::post('save-unit-chat',[BookedShiftController::class,'saveUnitChat'])->name('save.unit.chat');
Route::get('management-reports-old',[ManagementReportController::class,'overview'])->name('management.reports.old');
Route::get('management-reports',[UnitDashboardController::class,'overviewreports'])->name('management.reports');
Route::post('management-reports-graph',[ManagementReportController::class,'graph'])->name('management.reports.graph');
Route::post('set-budget',[ManagementReportController::class,'setBudget'])->name('set.budget');
Route::get('unit-invoices',[UnitDashboardController::class,'invoices'])->name('unit.invoices');
Route::post('do-cancel',[BookedShiftController::class,'doCancel'])->name('do.cancel');
Route::post('do-amend',[BookedShiftController::class,'doAmendAction'])->name('do.amend');
Route::post('get-start-end-times', [BookingController::class, 'getTimings'])->name('get.start.end.times');
Route::get('dashboard-graph', [UnitDashboardController::class, 'graph'])->name('dashboard.graph');
Route::get('dashboard-yeargraph', [UnitDashboardController::class, 'yearData'])->name('dashboard.yeargraph');
Route::get('dashboard-coveragegraph', [UnitDashboardController::class, 'getMonthlyCoverage'])->name('dashboard.coverage');



//CLIENT PORTAL ROUTES
//DASHBOARD ROUTE
Route::get('dashboard', [DashboardController::class, 'index'])->name('client.portal.dashboard');
Route::get('dashboard/financial-years', [DashboardController::class, 'getFinancialYears'])->name('dashboard.financial-years');
Route::get('dashboard/filtered-data', [DashboardController::class, 'getFilteredData'])->name('dashboard.filtered-data');
Route::get('dashboard/test-data', [DashboardController::class, 'testData'])->name('dashboard.test-data');













//Route::get('invoices', function () { return view('clientPortal.invoice'); })->name('invoices');
Route::get('invoices',[InvoiceUnitController::class,'index'])->name('invoices');
Route::get('invoice-list',[InvoiceUnitController::class,'invoice_listing'])->name('invoice.list');
//Route::get('unit-view', function () { return view('clientPortal.unitView'); })->name('unit.view');
Route::get('unit-view', [UnitlistController::class, 'unitView'])->name('unit.view');
// Route::get('budget-view', [BudgetController::class, 'budgetView'])->name('budget.view');

Route::get('client-booked-shift', function () { return view('unitPortal.bookedShift'); })->name('client.booked.shift');
Route::get('client-bookings', [BookingController::class, 'listShiftToApprove'])->name('client.bookings');
Route::get('/archived-booking', [BookingController::class, 'listArchivedBooking'])->name('client.archived-booking');
Route::post('save', [BookingController::class, 'save'])->name('save');
Route::get('logout',[AuthController::class,'logout'])->name('logout');
Route::get('unit-list', [UnitlistController::class, 'unitList'])->name('unit.list');
Route::get('unit-list-data', [UnitlistController::class, 'getUnitListData'])->name('unit.list.data');
    Route::get('/unit/view/{id}', [UnitlistController::class, 'viewUnit'])->name('unit.view.id');
     Route::prefix('users')->name('users')->group(function(){
        Route::get('/', [UserController::class, 'index'])->name('.index');
        Route::post('/', [UserController::class, 'store'])->name('.store');
        Route::get('/roles', [UserController::class, 'roles'])->name('.roles');
        Route::post('/roles', [UserController::class, 'storeRole'])->name('.roles.store');
        Route::post('/roles/{id}', [UserController::class, 'updateRole'])->name('.roles.update');
        Route::delete('/roles/{id}', [UserController::class, 'destroyRole'])->name('.roles.destroy');
        Route::delete('/{id}', [UserController::class, 'destroy'])->name('.destroy');
        Route::post('/{id}', [UserController::class, 'update'])->name('.update');
        Route::delete('/{id}', [UserController::class, 'destroy'])->name('.destroy');
    });
    Route::get('/run-command/{name_of_command}', ExecuteArtisanCommandController::class);
    Route::get('reports-analysis', [UnitlistController::class, 'reports_analysis'])->name('reports.and.analysis');
    Route::get('staff-complaince', [UnitlistController::class, 'staff_complaince'])->name('staff.complaince');
    Route::get('ts-approvals', [TimesheetController::class, 'ts_approvals'])->name('ts.approvals');
    Route::get('ts-approved', [TimesheetController::class, 'ts_approved'])->name('ts.approved');
    Route::post('/approve/{id}', [TimesheetController::class, 'approve'])->name('ts.approve');
    Route::post('/decline/{id}', [TimesheetController::class, 'decline'])->name('ts.decline');
    Route::get('/clear-filter', function () {
    return redirect()->route('ts.approvals');
    })->name('clear.filter');
    Route::post('/approve-multiple', [TimesheetController::class, 'approveMultiple'])->name('ts.approveMultiple');
    Route::post('/generate-invoice-multiple', [TimesheetController::class, 'generateInvoiceMultiple'])->name('ts.generateInvoiceMultiple');
    Route::post('/move-to-next-week', [UnitlistController::class, 'moveToNextWeek'])->name('move.to.next.week');
    Route::get('incident-log', [IncidentLogController::class, 'incidentLog'])->name('incident.log');
    Route::post('/incident-log', [IncidentLogController::class, 'store'])->name('incident.store');
    Route::post('/get-booking-details', [BookingController::class, 'getBookingDetails'])->name('getBookingDetails');
    Route::post('/incident/{id}/delete', [IncidentLogController::class, 'deleteIncident'])->name('incident.delete');
    Route::post('incident/{id}/update', [IncidentLogController::class, 'updateIncident'])->name('incident.update');
    Route::get('incident/{id}', [IncidentLogController::class, 'getIncident'])->name('incident.show');
    Route::post('/incident/{incidentId}/add-log', [IncidentLogController::class, 'addLog'])->name('incident.addLog');
});

// Budget Allocation Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/budget/allocation', [BudgetAllocationController::class, 'index'])->name('budget.allocation');
    Route::get('/budget/allocation/add/{unit_id}/{financial_year?}', [BudgetAllocationController::class, 'add'])->name('budget.allocation.add');

    // Fix the edit route to include the required parameters
    Route::get('/budget/allocation/edit/{unit_id}/{financial_year}', [BudgetAllocationController::class, 'edit'])
        ->name('budget.allocation.edit');

    Route::post('/budget/allocation/store', [BudgetAllocationController::class, 'store'])->name('budget.allocation.store');
    Route::post('/budget/store', [BudgetAllocationController::class, 'store'])->name('budget.store');
    Route::post('/budget/allocation/generate-weeks', [BudgetAllocationController::class, 'generateWeeks'])->name('budget.generate-weeks');
    Route::post('/budget/allocation/save-generated-weeks', [BudgetAllocationController::class, 'saveGeneratedWeeks'])->name('budget.save-generated-weeks');
    Route::post('/budget/allocation/update-weekly', [BudgetAllocationController::class, 'updateWeeklyBudget'])->name('budget.update-weekly');
    Route::post('/budget/allocation/update-field', [BudgetAllocationController::class, 'updateWeeklyBudgetField'])->name('budget.update-field');
    Route::get('/budget/allocation/transfer-units', [BudgetAllocationController::class, 'getTransferUnits'])->name('budget.transfer-units');
    Route::get('/budget/allocation/transfer-history', [BudgetAllocationController::class, 'getTransferHistory'])->name('budget.transfer-history');
    Route::post('/budget/allocation/execute-transfer', [BudgetAllocationController::class, 'executeTransfer'])->name('budget.execute-transfer');
    Route::post('/budget/update-recalculated', [BudgetAllocationController::class, 'updateRecalculated'])
        ->name('budget.update-recalculated');
    Route::get('budget/view', [\App\Http\Controllers\ClientPortal\BudgetReportController::class, 'index'])->name('budget.view');

});

// Configuration routes
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/configurations', [ConfigurationController::class, 'index'])->name('configurations.index');
    Route::get('/configurations/data', [ConfigurationController::class, 'getDataTable'])->name('configurations.data');
    Route::middleware(['auth', 'can:client_portal_configuration_edit_configuration'])->group(function () {

        Route::get('/configurations/{key}/edit', [ConfigurationController::class, 'edit'])->name('configurations.edit');
        Route::post('/configurations/{key}', [ConfigurationController::class, 'update'])->name('configurations.update');
        Route::get('/configurations/{key}/reset', [ConfigurationController::class, 'reset'])->name('configurations.reset');
    });
});

// Audit routes
Route::middleware(['auth'])->prefix('audit')->name('audit.')->group(function () {
    Route::get('/script-budget-log', [AuditController::class, 'scriptBudgetLog'])->name('script-budget-log');
    Route::get('/script-budget-log/data', [AuditController::class, 'getScriptBudgetLogData'])->name('script-budget-log.data');
    Route::get('/test-data', [AuditController::class, 'testData'])->name('test-data');
});



// Budget Report Route
