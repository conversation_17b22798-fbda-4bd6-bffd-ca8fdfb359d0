# Dashboard Unit and Financial Year Filters Implementation

## Overview
This implementation adds unit selection and financial year filtering functionality to the Client Portal Dashboard. The solution includes:

1. **Unit Dropdown Filter**: Shows units available to the logged-in client
2. **Financial Year Dropdown Filter**: Dynamically populated based on selected unit
3. **User Access Control**: Respects user permissions (unit-specific vs client-wide access)
4. **AJAX Functionality**: Dynamic loading of financial years without page refresh
5. **Event System**: Custom events for dashboard components to respond to filter changes

## Files Created/Modified

### Backend Files
- `app/Http/Controllers/ClientPortal/DashboardController.php` - Main controller logic
- `routes/web.php` - Added new routes for AJAX endpoints

### Frontend Files
- `public/js/dashboard-filters.js` - JavaScript functionality for filters
- `public/css/dashboard-filters.css` - Styling for filter components
- `resources/views/common/dashboard/dashboard_wrapper.blade.php` - Updated view with filters

## Controller Methods

### `index(Request $request)`
- Main dashboard page
- Gets units for logged-in client
- Handles initial financial year loading if unit is pre-selected
- Returns view with data

### `getUnitsForClient($clientId)`
- Private method to get units based on user permissions
- If user belongs to specific unit: returns only that unit
- If user is client admin: returns all client units

### `getFinancialYearsForUnit($unitId)`
- Private method to get available financial years for a unit
- Queries `ng_client_unit_annual_budgets` table
- Returns unique financial years in descending order

### `getFinancialYears(Request $request)`
- AJAX endpoint to get financial years for selected unit
- Returns JSON response with financial years array

### `getFilteredData(Request $request)`
- AJAX endpoint for future dashboard data filtering
- Ready for implementation of filtered budget/expense data

## JavaScript Class: DashboardFilters

### Key Features
- **Automatic Initialization**: Initializes when DOM loads
- **Event Handling**: Handles dropdown changes
- **AJAX Requests**: Fetches financial years dynamically
- **Loading States**: Shows loading indicators during requests
- **Error Handling**: Graceful error handling with user feedback
- **Custom Events**: Dispatches events for other components to listen

### Methods
- `handleUnitChange()`: Handles unit selection changes
- `handleFinancialYearChange()`: Handles financial year changes
- `loadFinancialYears(unitId)`: Loads financial years via AJAX
- `triggerDataRefresh()`: Triggers dashboard data refresh
- `getCurrentFilters()`: Gets current filter values
- `setFilters(unitId, financialYear)`: Sets filters programmatically

## Database Relationships

### Tables Used
- `ng_client_units`: Unit information (clientUnitId, name, clientId)
- `ng_client_unit_annual_budgets`: Financial year data (client_unit_id, financial_year)

### Key Relationships
- ClientUnit belongs to Client (clientId)
- ClientUnitAnnualBudget belongs to ClientUnit (client_unit_id)
- User authentication links to Client or specific ClientUnit

## User Access Logic

### Client Admin Users
- Can see all units belonging to their client
- Financial years shown for selected unit

### Unit-Specific Users
- Can only see their assigned unit
- Unit dropdown shows only their unit (but still functional)
- Financial years shown for their unit

## Usage Instructions

### For Dashboard Components
Listen for filter changes:
```javascript
document.addEventListener('dashboardFiltersChanged', function(event) {
    const filters = event.detail;
    // filters.unitId and filters.financialYear available
    // Update your dashboard component based on filters
});
```

### For Data Refresh
Listen for data refresh events:
```javascript
document.addEventListener('dashboardDataRefreshed', function(event) {
    const data = event.detail;
    // Handle refreshed dashboard data
});
```

### Programmatic Filter Setting
```javascript
const dashboardFilters = new DashboardFilters();
dashboardFilters.setFilters('123', '2024'); // unitId, financialYear
```

## API Endpoints

### GET /dashboard/financial-years
Parameters: `unit_id`
Returns: `{ financial_years: [2024, 2023, 2022] }`

### GET /dashboard/filtered-data
Parameters: `unit_id`, `financial_year`
Returns: Filtered dashboard data (implementation pending)

## Styling
- Responsive design using Bootstrap classes
- Custom CSS for enhanced appearance
- Loading states and error handling styles
- Consistent with existing portal styling

## Security Considerations
- User permissions respected in controller
- CSRF protection via Laravel middleware
- Input validation on AJAX requests
- No direct database queries from frontend

## Future Enhancements
- Add more filter options (date ranges, categories)
- Implement dashboard data filtering
- Add export functionality based on filters
- Cache frequently accessed data
- Add filter state persistence

## Testing
1. Login as Client Admin - should see all client units
2. Login as Unit User - should see only their unit
3. Select unit - financial years should load dynamically
4. Change selections - events should fire properly
5. Check console for any JavaScript errors

## Deployment Notes
- Clear view cache: `php artisan view:clear`
- Clear route cache: `php artisan route:clear`
- Ensure JavaScript/CSS files are accessible
- Check file permissions on public directory
