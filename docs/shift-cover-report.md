# Shift Cover Report Documentation

## Overview
The Shift Cover Report is a comprehensive Laravel DataTables implementation that provides detailed information about shift bookings with server-side processing, filtering, and export capabilities.

## Features

### ✅ **Technical Implementation**
- **Yajra DataTables**: Server-side processing with AJAX pagination
- **Laravel Eloquent**: Proper relationships, no raw queries
- **MVC Structure**: Separate controller, view, and JavaScript files
- **Responsive Design**: Mobile and tablet friendly
- **Error Handling**: Comprehensive validation and error messages

### ✅ **Report Columns**
1. **Booking ID** - Unique booking identifier
2. **Account Code** - Static value "607000" (configurable)
3. **Date** - Booking date in MM/DD/YYYY format
4. **Shift** - Shift name from relationship
5. **Category** - Staff category from relationship
6. **Staff** - Full staff name (forname + middle_name + surname)
7. **Start Time** - Shift start time
8. **End Time** - Shift end time
9. **Number of Hours** - Calculated hours worked
10. **Shift Booked On** - Booking creation date
11. **Reason for Booking** - Enum converted to readable text
12. **Initial Amount** - Initial cost in currency format
13. **Final Amount** - Final cost in currency format

### ✅ **Filtering & Search**
- **Unit Filter**: Dropdown to filter by specific units
- **Date Range**: Start and end date pickers
- **Column Search**: Search functionality for all text columns
- **Sorting**: All columns are sortable

### ✅ **Summary Features**
- **Real-time Totals**: Initial and Final amount totals
- **Summary Cards**: Visual display of totals
- **Dynamic Updates**: Totals update with filters

## File Structure

```
app/Http/Controllers/ReportController.php          # Main controller
resources/views/reports/shift_cover_report.blade.php  # Blade template
public/js/reports/shift-cover-report.js            # JavaScript functionality
routes/web.php                                     # Route definitions
```

## Routes

```php
// Main report page
GET /reports/shift-cover-report

// DataTables AJAX data endpoint
GET /reports/shift-cover-report/data

// Export functionality
GET /reports/shift-cover-report/export
```

## Usage

### Accessing the Report
Navigate to: `/reports/shift-cover-report`

### Filtering Data
1. **Unit Selection**: Choose specific unit or "All Units"
2. **Date Range**: Set start and end dates
3. **Apply Filters**: Click "Apply Filters" or press Enter
4. **Reset**: Click "Reset" to clear all filters

### Exporting Data
- **Excel Export**: Click the Excel button for CSV download
- **PDF Export**: Click the PDF button for HTML/PDF download

### Search and Sort
- **Global Search**: Use the search box in the top right
- **Column Sorting**: Click column headers to sort
- **Multi-column Search**: Each column supports individual searching

## Database Requirements

### Required Tables
- `bookings` - Main booking data
- `shifts` - Shift information
- `staff_categories` - Staff category data
- `staff` - Staff information
- `client_units` - Unit information

### Required Relationships
```php
// Booking model relationships
public function shift() // belongsTo Shift
public function category() // belongsTo StaffCategory  
public function staff() // belongsTo Staff
public function unit() // belongsTo ClientUnit
```

## Performance Optimization

### Implemented Optimizations
- **Eager Loading**: All relationships loaded efficiently
- **Server-side Processing**: Handles large datasets
- **Indexed Queries**: Optimized database queries
- **Pagination**: Configurable page sizes

### Recommended Indexes
```sql
-- Booking table indexes
CREATE INDEX idx_bookings_unit_status ON bookings(unitStatus);
CREATE INDEX idx_bookings_date ON bookings(date);
CREATE INDEX idx_bookings_unit_id ON bookings(unitId);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);
```

## Customization

### Adding New Columns
1. Update the controller's `getShiftCoverReportData()` method
2. Add column definition in JavaScript
3. Update the Blade template header

### Modifying Filters
1. Add filter inputs to the Blade template
2. Update JavaScript to pass filter parameters
3. Modify controller to handle new filters

### Changing Export Format
1. Update `exportToExcel()` or `exportToPDF()` methods
2. Install additional packages (PhpSpreadsheet, DomPDF) if needed

## Error Handling

### Client-side Errors
- Loading indicators during AJAX requests
- User-friendly error messages
- Form validation feedback

### Server-side Errors
- Request validation
- Database error handling
- Graceful fallbacks

## Browser Compatibility
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## Dependencies

### PHP Packages
- Laravel 8+
- Yajra DataTables
- Carbon (date handling)

### JavaScript Libraries
- jQuery 3.6+
- DataTables 1.13+
- Bootstrap 4/5

### CSS Frameworks
- Bootstrap 4/5
- Font Awesome (icons)

## Troubleshooting

### Common Issues
1. **No data showing**: Check database connections and relationships
2. **Slow loading**: Verify database indexes are in place
3. **Export not working**: Check file permissions and storage paths
4. **Filters not working**: Verify JavaScript console for errors

### Debug Mode
Enable debug mode in the JavaScript file by setting:
```javascript
console.log('Debug mode enabled');
```

## Future Enhancements

### Planned Features
- Advanced filtering options
- Chart visualizations
- Scheduled report generation
- Email report delivery
- Custom column selection

### Performance Improvements
- Redis caching for frequently accessed data
- Database query optimization
- CDN integration for assets

## Support
For technical support or feature requests, contact the development team.
