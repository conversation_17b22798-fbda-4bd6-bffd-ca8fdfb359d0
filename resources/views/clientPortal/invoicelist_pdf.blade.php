  <style>

table {
	border-collapse: collapse;
	border-spacing: 4px; 
	color: #4a4a4d;
	font: 15px/1.4 "Helvetica Neue", Helvetica, Arial, Sans-serif;
	width: 100%;
}
tbody tr:nth-child(even) {
	background: #f0f0f2;
}
/* tfoot tr:last-child td {
	border-bottom: 0;
} */
th,
td {
	border: 1px solid #666;
	padding: 6px 4px;
	vertical-align: middle;
    font-size: 12px;
}
td {
	border-bottom: 1px solid #e5e5e5;
	border-right: 1px solid #e5e5e5;
}
td:first-child {
	border-left: 1px solid #e5e5e5;
}
.book-title {
	color: #395870;
	display: block;
}
.item-stock,
.item-qty {
	text-align: center;
}
.item-price {
	text-align: right;
}
.item-multiple {
	display: block;
}
.task table {
	margin-bottom: 44px;
}
.task a {
	color: #666;
	text-decoration: none;
}
.task thead {
	background-color: #f5f5f5;
	-webkit-background: transparent;
	   -moz-background: transparent;
	        background: transparent;
	color: #333;
}
.task table th, .task table td {
	border-bottom: 0;
	border-right: 0;
}
.task table td {
	border-bottom: 1px solid #ddd;
}
.task table th, .task table td {
	padding-bottom: 22px;
	vertical-align: top;
}
.task tbody tr:nth-child(even) {
	background: transparent;
} 
.task table:last-child {
	margin-bottom: 0;
}
tfoot {
    background: #fff;
}
tbody tr td{
    padding: 4px;
}
  </style>
<table style="border-collapse: collapse;border-spacing: 4px;width: 100%;font-size: 12px;">
    <tr style="background: #f4f4f4;">
        <td style="padding: 20px 15px;font-size: 13px; border: none;">
            <img style="width: 260px;" src="https://smartseek.co.uk/unit-portal/images/header.svg" alt="Logo"></a>
        </td>
        
    </tr>
</table>

 <table>
		       <tr>
                 <td style="height: 5px;border:none"> </td>
               </tr>
            </table>

<table style="border-collapse: collapse;border-spacing: 4px;width: 100%;font-size: 12px;">
    <tr>
        <td style="padding: 10px;font-size: 13px;    border-top: 1px solid #e5e5e5;">
            <b>Client</b><br>
            Amica Care Trust <br>
            Gatchell House, Gatchell Oaks,<br>
            Trull, Somerset, TA3 7EG
        </td>
        <td style="padding: 10px;font-size: 13px;    border-top: 1px solid #e5e5e5;">
            <b>Correspondence Address</b><br>
            Nurses Group, Nexus Innovation Centre,
            George Smith Way,Lufton,Yeovil,BA22 8QR,England
            Phone: 01935 315031
            Email: <EMAIL>
            Web: www.nursesgroup.co.uk
        </td>
    </tr>
</table>

<table>
    <tr>
        <td style="height: 15px;border:none"> </td>
    </tr>
</table>


<table style="border-collapse: collapse;border-spacing: 4px;width: 100%;font-size: 12px;">
		<thead style="background: #212121;color: #d2d2d2;">
			<tr>
				<th style="padding: 10px;">Invoice Date </th>
				<th style="padding: 10px;width:30%;">Work Place</th>
                <th style="padding: 10px;">Invoice Period</th>
                <th style="padding: 10px;">Invoice No</th>
                <th style="padding: 10px;">Due on </th>
                <th style="text-align: right;font-weight:600;padding: 10px;">Grand Total</th>
			</tr>
		</thead>
		<tbody>
			<tr>
				<td style="padding: 10px;">{{ date('d-m-Y') }}</td>
				<td style="padding: 10px;width: 30%;">{{ $unitName }} {{ $unitAddress }}</td>
                <td style="padding: 10px;">{{ $week_range }}</td>
                <td style="padding: 10px;">{{ substr($unitName,0,3) }} 000</td>
                <td style="padding: 10px;">{{ $dueDate }}</td>
                <td style="text-align: right; font-weight:600;padding: 10px;"> £{{ number_format($grandTotal, 2) }}</td>
			</tr>
		</tbody>
	</table>

           <table>
		       <tr>
                 <td style="height: 15px;border:none"> </td>
               </tr>
            </table>

	<table style="    border-collapse: collapse;border-spacing: 4px;width: 100%;font-size: 12px;">
		<thead style="background: #212121;color: #d2d2d2;">
			<tr>
				<th>Shift ID</th>
				<th>Date</th>
                <th>Shift Type</th>
                <th>Staff</th>
                <th>Start Time</th>
                <th>End Time</th>
                <th>Work Hours</th>
                <th>TS NO</th>
                <th>Account</th>
                <th>Hourly Rate</th>
                <th>Addtl Exp </th>
                <th>TA</th>
                <th>Tax</th>
                <th style="text-align: right;">Line Total</th>
			</tr>
		</thead>
		<tbody>
        @php $netAmount = 0; @endphp

        @foreach($data as $row)
            <tr>
                <td>{{ $row['bookingId'] }}</td>
                <td>{{ $row['date'] }}</td>
                <td>{{ $row['shift_category'] }}</td>
                <td>{{ $row['staff_name'] }}</td>
                <td>{{ $row['startTime'] }}</td>
                <td>{{ $row['endTime'] }}</td>
                <td>{{ $row['unitHours'] }}</td>
                <td>{{ $row['number'] }}</td>
                <td>000000</td>
                <td>£{{ number_format($row['hourly_rate'],2) }}</td>
                <td>£{{ number_format($row['unit_expense'],2) }}</td>
                <td>£{{ number_format($row['ta'],2) }}</td>
                <td>£{{ number_format($row['tax'],2) }}</td>
                <td style="text-align: right;">£{{ number_format($row['lineTotal'],2) }}</td>
                @php
                    $netAmount += floatval($row['lineTotal']);
                @endphp
            </tr>
        @endforeach
			
          
		</tbody>
		<tfoot>
		
			<tr>
				<td style="text-align: right;font-size: 15px;    padding: 10px; padding-right: 8px;" colspan="14">Net Amount to Pay</td>
				<td style="font-size: 15px;text-align: right;">£{{ number_format($netAmount, 2) }}</td>    
			</tr>
		</tfoot>
	</table>

    <table>
    <tr>
        <td style="height: 15px;border:none"> </td>
    </tr>
   </table>

    <table>
        <tr>
            <td style="border: none;padding: 0;">
                Full payment due in 14 days upon receipt of the invoice
           </td>
        </tr>
        <tr style="background: none;">
            <td style="border: none;padding: 0;">  
                Please make all payments to: JSS HEALTHCARE LTD, Sort Code: 20-12-83, Account Number: ******** 

           </td>
        </tr>
        <tr style="background: none;">
            <td style="border: none;padding: 0;">  
                Reference: Use invoice number 
                Thank You very much for choosing Nursesgroup.
           </td>
        </tr>
    </table>

    <table>
    <tr>
        <td style="height: 25px;border:none"> </td>
    </tr>
   </table>


     <table>
    <tr>
       <td style="border: none;padding: 0;">
    <img  src="{{ asset('https://smartseek.co.uk/unit-portal/images/header.svg') }}" alt="Logo"></a>        
    </td>
    </tr>
   </table>