@extends('clientPortal.layout')
@section('title', 'Dashboard | Client Portal')

@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper clientvalue" client="{{ auth()->user()->portal_id }}">


    <div class="Container_Fluid_Main">
        <div class="row headTitleBtm">
            <div class="col-sm-9">
                <div class="GpBoxHeading no-bdr pb0">
                    <h4>
                        Incident Log
                    </h4>
                </div>
            </div>
            <div class="col-sm-3 text-right ">
                <button class="btn-primary" id="addNewIncident"><i class="fa fa-plus-circle" aria-hidden="true"></i> Add New</button>
            </div>
        </div>

        <div class="MainBoxRow mb25">

        <form method="GET" action="{{ route('incident.log') }}" class="mb-4">
            <div class="row">
                <!-- Booking ID Filter -->
                <div class="col-md-2">
                    <label for="booking_id">Booking ID</label>
                    <input type="text" name="booking_id" id="booking_id" class="form-control" value="{{ request('booking_id') }}" placeholder="Enter Booking ID">
                </div>

                <!-- Unit Filter -->
                <div class="col-md-2">
                    <label for="unit">Unit</label>
                    {{-- <input type="text" name="unit" id="unit" class="form-control" value="{{ request('unit') }}" placeholder="Enter Unit"> --}}
                    <select name="unit" id="unit" class="form-control">
                        @if($unitsdropdown->isEmpty())
                            <option value="">No data available</option>
                        @else
                            <option value="" {{ request('unit') == '' ? 'selected' : '' }}>Select Unit</option>
                            @foreach($unitsdropdown as $unit)
                                <option value="{{ $unit->name }}" {{ request('unit') == $unit->name ? 'selected' : '' }}>
                                    {{ $unit->name }}
                                </option>
                            @endforeach
                        @endif
                    </select>
                </div>

                <!-- Date Range Filter -->
                <div class="col-md-2">
                    <label for="date_range">Date Range</label>
                    <input type="text" name="date_range" id="date_range" class="form-control" placeholder="Select the date">
                </div>
                <!-- Incident Priority Filter -->
                <div class="col-md-2 mt-3">
                    <label for="priority">Incident Priority</label>
                    <select name="priority" id="incidentStatus" class="form-control">
                        <option value="">Select Priority</option>
                        <option value="Low" {{ request('priority') == 'Low' ? 'selected' : '' }}>Low</option>
                        <option value="Medium" {{ request('priority') == 'Medium' ? 'selected' : '' }}>Medium</option>
                        <option value="High" {{ request('priority') == 'High' ? 'selected' : '' }}>High</option>
                    </select>
                </div>

                <!-- Status Filter -->
                <div class="col-md-2 mt-3">
                    <label for="status">Status</label>
                    <select name="status" id="incidentStatus" class="form-control">
                        <option value="">Select Status</option>
                        <option value="New" {{ request('status') == 'New' ? 'selected' : '' }}>New</option>
                        <option value="In Progress" {{ request('status') == 'In Progress' ? 'selected' : '' }}>In Progress</option>
                        <option value="Informed Manager" {{ request('status') == 'Informed Manager' ? 'selected' : '' }}>Informed Manager</option>
                        <option value="Informed Unit" {{ request('status') == 'Informed Unit' ? 'selected' : '' }}>Informed Unit</option>
                        <option value="Closed" {{ request('status') == 'Closed' ? 'selected' : '' }}>Closed</option>
                    </select>
                </div>
           
                <div class="col-md-2 mt20 mb10">
                    <button type="submit" class="btn btn-filter">
                            <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                    </button>
                    <a href="{{ route('incident.log') }}" class="btn btn-reset" id="resetButton">
                        <img class="reset_img" src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                    </a>
                </div>
                 </div>
        </form>

    </div>



        <div class="card-body">

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead class="thead-dark">
                                <tr>
                                    <th>SlNo.</th> <!-- Add Serial Number Column -->
                                    <th>Booking ID</th>
                                    <th>Unit</th>
                                    <th>Booking Date</th>
                                    <th>Shift</th>
                                    <th>Staff Name</th>
                                    <th>Category</th> <!-- Add Category Column -->
                                    <th>Incident</th>
                                    <th>Status</th>
                                    <th>Incident Priority</th>
                                    <th>Booking Log</th>
                                    <th>Log</th>
                                    <th style="text-align: right;">Action</th>
                                </tr>
                            </thead>

                            <tbody>
                                @foreach ($incidents as $incident)
                                    <tr>
                                        <td>{{ $loop->iteration + ($incidents->currentPage() - 1) * $incidents->perPage() }}</td> <!-- Serial Number -->
                                        <td>{{ $incident->booking_id }}</td>
                                        <td>{{ $incident->unit }}</td>
                                        <td>{{ \Carbon\Carbon::parse($incident->date)->format('d-M-Y') }}</td>
                                        <td>{{ $incident->shift }}</td>
                                        <td>{{ $incident->staff }}</td>
                                        <td>{{ $incident->booking->category->name ?? 'No Category' }}</td> <!-- Display Category -->
                                        <td>{{ $incident->incident }}</td>
                                        <td>{{ $incident->status }}</td>
                                        <td>{{ $incident->priority }}</td>
                                        <td>
                                            <button type="button" class="btn btn-primary-sm view-booking-log" data-booking-id="{{ $incident->booking_id }}" data-incident="true">
                                                <i class="fa fa-clock-o" aria-hidden="true"></i>
                                            </button>
                                        </td>
                                        <td>
                                            <button class="btn-three-sm view-log" data-id="{{ $incident->id }}">
                                                <i class="fa fa-sticky-note-o" aria-hidden="true"></i>
                                            </button>
                                        </td>
                                        <td style="text-align: right;">
                                            <button class="btn-three-sm update-incident" data-id="{{ $incident->id }}">Update</button>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>

                        <!-- Pagination Links -->
                        <div class="d-flex justify-content-center">
                            {{ $incidents->links() }}
                        </div>
                    </div>

            </div>
        </div>
    </div>
</div>
@include('unitPortal.bookingUnitLogBook')
<!-- Log Modal -->
<div class="modal fade" id="logModal" tabindex="-1" role="dialog" aria-labelledby="logModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="logModalLabel">Incident Log</h5>
            </div>
            <div class="modal-body">
                <!-- Manual Log Entry -->
                <textarea class="form-control mb-3" id="manualLogEntry" rows="3" placeholder="Enter your log entry here..."></textarea>
                <!-- Existing Log Display -->
                <textarea class="form-control mb-3" id="incidentLog" rows="10" readonly></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="saveLogButton">Save Log</button>
            </div>
        </div>
    </div>
</div>
<!-- Add New Incident Modal -->
<div class="modal fade" id="incidentModal" tabindex="-1" role="dialog" aria-labelledby="incidentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <script>

        </script>
        <div class="modal-content">
            <div class="modal-header">
                <h4 class="modal-title" id="incidentModalLabel">Add New Incident</h4>
            </div>
            <form id="incidentForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="bookingId">Search Booking ID</label>
                        <input type="text" class="form-control" id="bookingId" name="bookingId" placeholder="Enter Booking ID">
                    </div>
                    <div class="form-group">
                        <label for="unit">Unit</label>
                        <input type="text" class="form-control" id="incidentUnit" name="unit" readonly>
                        <input type="hidden" id="unitId" name="unitId"> <!-- Hidden Unit ID -->
                    </div>
                    <div class="form-group">
                        <label for="date">Date</label>
                        <input style="background: #e9e9e9 !important;box-shadow: none;" type="text" class="form-control" id="shiftDate" name="shiftDate" readonly>
                    </div>
                    <div class="form-group">
                        <label for="shift">Shift</label>
                        <input style="background: #e9e9e9 !important;box-shadow: none;" type="text" class="form-control" id="shift" name="shift" readonly>
                    </div>
                    <div class="form-group">
                        <label for="staffName">Staff Name</label>
                        <input style="background: #e9e9e9 !important;box-shadow: none;" type="text" class="form-control" id="staffName" name="staffName" readonly>
                        <input type="hidden" id="staffId" name="staffId"> <!-- Hidden Staff ID -->
                    </div>
                    <div class="form-group">
                        <label for="incidentDetails">Details of Incident</label>
                        <textarea class="form-control" id="incidentDetails" name="incidentDetails" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" name="status">
                            <option value="New">New</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Informed Manager">Informed Manager</option>
                            <option value="Informed Unit">Informed Unit</option>
                            <option value="Closed">Closed</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="priority">Incident Priority</label>
                        <select class="form-control" id="priority" name="priority">
                            <option value="Low">Low</option>
                            <option value="Medium">Medium</option>
                            <option value="High">High</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>
<!-- Update Incident Modal -->
<div class="modal fade" id="updateIncidentModal" tabindex="-1" role="dialog" aria-labelledby="updateIncidentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateIncidentModalLabel">Update Incident</h5>
            </div>
            <form id="updateIncidentForm">
                <div class="modal-body">
                    <div class="form-group">
                        <label for="updateBookingId">Booking ID</label>
                        <input type="text" class="form-control" id="updateBookingId" name="bookingId" readonly>
                        <input type="hidden" id="updateIncidentId" name="incident_id">
                    </div>
                    <div class="form-group">
                        <label for="updateUnit">Unit</label>
                        <input type="text" class="form-control" id="updateUnit" name="unit" readonly>
                    </div>
                    <div class="form-group">
                        <label for="updateShiftDate">Date</label>
                        <input type="text" class="form-control" id="updateShiftDate" name="shiftDate" readonly>
                    </div>
                    <div class="form-group">
                        <label for="updateShift">Shift</label>
                        <input type="text" class="form-control" id="updateShift" name="shift" readonly>
                    </div>
                    <div class="form-group">
                        <label for="updateStaffName">Staff Name</label>
                        <input type="text" class="form-control" id="updateStaffName" name="staffName" readonly>
                    </div>
                    <div class="form-group">
                        <label for="updateIncidentDetails">Details of Incident</label>
                        <textarea class="form-control" id="updateIncidentDetails" name="incidentDetails" rows="3" readonly></textarea>
                    </div>
                    <div class="form-group">
                        <label for="updateStatus">Status</label>
                        <select class="form-control" id="updateStatus" name="status">
                            <option value="New">New</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Informed Manager">Informed Manager</option>
                            <option value="Informed Unit">Informed Unit</option>
                            <option value="Closed">Closed</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="updatePriority">Incident Priority</label>
                        <input type="text" class="form-control" id="updatePriority" name="priority" readonly>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

<script>
    localStorage.setItem('nsg-client-portal-login-user-id', "{{ auth()->user()->portal_id }}");
    localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->portal->name }}");
</script>
<script>
    $('#date_range').daterangepicker({
        autoApply: true, // Automatically apply the selected range
        autoUpdateInput: false, // Prevent the input field from being automatically populated
        locale: {
            format: 'YYYY-MM-DD', // Format for the selected dates
            separator: ' to ', // Separator between start and end dates
        },
        opens: 'center', // Position of the calendar
        alwaysShowCalendars: true, // Always show the calendar
    });

    // Update the input field only when a date range is selected
    $('#date_range').on('apply.daterangepicker', function(ev, picker) {
        $(this).val(picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format('YYYY-MM-DD'));
    });

    // Clear the input field only when the Reset button is clicked
    $('#resetButton').click(function(ev) {
        $('#date_range').val(''); // Reset the input field
    });
    $(document).ready(function() {
        // Open the modal when Add New button is clicked
        $('#addNewIncident').click(function() {
            $('#incidentModal').modal('show');
        });

        // Autofill fields when Booking ID is entered
        $('#bookingId').on('input', function() {
            const bookingId = $(this).val();
            if (bookingId) {
                const token = $('meta[name="csrf-token"]').attr('content'); // CSRF token for security

                $.ajax({
                    type: 'POST',
                    url: '/get-booking-details', // Adjust the route as necessary
                    data: {
                        _token: token,
                        bookingId: bookingId,
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#staffName').val(response.data.staff ? response.data.staff : 'nostaff');
                            $('#incidentUnit').val(response.data.unit);
                            $('#shift').val(response.data.shift);
                            $('#shiftDate').val(response.data.shiftDate); // Populate the shift date
                             // Populate hidden fields
                            $('#unitId').val(response.data.unitId); // Set Unit ID
                            $('#staffId').val(response.data.staffId); // Set Staff ID
                        } else {
                            alert(response.message);
                            $('#staffName, #incidentUnit, #shift, #shiftDate').val('');
                        }
                    },
                    error: function(xhr) {
                        console.error(xhr.responseText);
                        alert('An error occurred while fetching booking details.');
                        $('#staffName, #incidentUnit, #shift, #shiftDate').val('');
                    },
                });
            } else {
                $('#staffName, #unit, #shift, #shiftDate').val('');
            }
        });

        // Handle form submission
        $('#incidentForm').submit(function(e) {
            e.preventDefault(); // Prevent the default form submission

            // Collect form data
            const formData = {
                _token: '{{ csrf_token() }}', // CSRF token for security
                bookingId: $('#bookingId').val(),
                unit: $('#incidentUnit').val(),
                unitId: $('#unitId').val(), // Include Unit ID
                shiftDate: $('#shiftDate').val(), // Ensure shiftDate is included
                shift: $('#shift').val(),
                staffName: $('#staffName').val(),
                staffId: $('#staffId').val(), // Include Staff ID
                incidentDetails: $('#incidentDetails').val(),
                status: $('#status').val(),
                priority: $('#priority').val()
            };
            // Send AJAX request to store the incident
            $.ajax({
                url: '{{ route("incident.store") }}', // Route to handle form submission
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Reset the form and close the modal
                        $('#incidentForm')[0].reset();
                        $('#incidentModal').modal('hide');

                        alert('Incident added successfully!');
                        location.reload();
                    } else {
                        alert('Failed to add the incident.');
                    }
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        // Validation error
                        const errors = xhr.responseJSON.errors;
                        let messages = '';
                        for (const field in errors) {
                            messages += errors[field].join('<br>') + '<br>';
                        }
                        bootbox.alert(messages);  // or alert(messages)
                    } else {
                        alert('An error occurred while adding the incident.');
                    }
                }
            });
        });
        // Handle delete button click
        $('.delete-incident').click(function () {
            const incidentId = $(this).data('id'); // Get the incident ID from the button's data attribute
            const token = '{{ csrf_token() }}'; // CSRF token for security

            if (confirm('Are you sure you want to delete this incident?')) {
                $.ajax({
                    type: 'POST',
                    url: `/incident/${incidentId}/delete`, // Adjust the route as necessary
                    data: {
                        _token: token,
                        id: incidentId
                    },
                    success: function(response) {
                        if (response.success) {
                            // Remove the row from the table
                            $(`#incident-row-${incidentId}`).remove();
                            // alert('Incident deleted successfully!');
                            location.reload();
                        } else {
                            alert('Failed to delete the incident.');
                        }
                    },
                    error: function(xhr) {
                        alert('An error occurred while deleting the incident.');
                    }
                });
            }
        });
        $('.update-incident').click(function () {
            const incidentId = $(this).data('id'); // Get the incident ID
            const token = '{{ csrf_token() }}'; // CSRF token for security

            $.ajax({
                type: 'GET',
                url: `/incident/${incidentId}`, // Adjust the route to fetch incident details
                success: function (response) {
                    if (response.success) {
                        // Populate the modal fields with incident data
                        $('#updateBookingId').val(response.data.booking_id);
                        $('#updateIncidentId').val(response.data.id); // store actual incident ID
                        $('#updateUnit').val(response.data.unit);
                        $('#updateShiftDate').val(response.data.date);
                        $('#updateShift').val(response.data.shift);
                        $('#updateStaffName').val(response.data.staff);
                        $('#updateIncidentDetails').val(response.data.incident);
                        $('#updateStatus').val(response.data.status);
                        $('#updatePriority').val(response.data.priority);

                        // Show the modal
                        $('#updateIncidentModal').modal('show');
                    } else {
                        alert('Failed to fetch incident details.');
                    }
                },
                error: function (xhr) {
                    alert('An error occurred while fetching incident details.');
                }
            });
        });
                // Handle Update Form Submission
        $('#updateIncidentForm').submit(function (e) 
        {
            e.preventDefault(); // Prevent the default form submission

            const incidentId = $('#updateIncidentId').val(); // ✅ this is now correct
            const formData = {
                _token: '{{ csrf_token() }}', // CSRF token for security
                status: $('#updateStatus').val(), // Only update the status
            };

            $.ajax({
                type: 'POST',
                url: `/incident/${incidentId}/update`, // Adjust the route for updating the incident
                data: formData,
                success: function (response) {
                    if (response.success) {
                        // Close the modal and refresh the page
                        $('#updateIncidentModal').modal('hide');
                        alert('Incident updated successfully!');
                        location.reload();
                    } else {
                        alert('Failed to update the incident.');
                    }
                },
                error: function (xhr) {
                    alert('An error occurred while updating the incident.');
                }
            });
        });
            // Handle "Save Log" button click
        $('#saveLogButton').click(function () {
            const incidentId = $('#logModal').data('incident-id'); // Get the incident ID (set when opening the modal)
            const newLogEntry = $('#manualLogEntry').val(); // Get the manual log entry
            const token = '{{ csrf_token() }}'; // CSRF token for security

            if (!newLogEntry.trim()) {
                alert('Please enter a log entry.');
                return;
            }

            // Send the new log entry to the server
            $.ajax({
                type: 'POST',
                url: `/incident/${incidentId}/add-log`, // Adjust the route as necessary
                data: {
                    _token: token,
                    log: newLogEntry
                },
                success: function (response) {
                    if (response.success) {
                        // Append the new log entry to the existing log
                        const currentLog = $('#incidentLog').val();
                        const timestamp = new Date().toLocaleString();
                        const updatedLog = `${currentLog}\n[${timestamp}] ${newLogEntry}`;
                        $('#incidentLog').val(updatedLog);

                        // Clear the manual log entry textarea
                        $('#manualLogEntry').val('');

                        alert('Log entry added successfully!');
                    } else {
                        alert('Failed to add the log entry.');
                    }
                },
                error: function (xhr) {
                    alert('An error occurred while adding the log entry.');
                }
            });
        });
                // Handle "View Log" button click
        $('.view-log').click(function () {
            const incidentId = $(this).data('id'); // Get the incident ID

            $.ajax({
                type: 'GET',
                url: `/incident/${incidentId}`, // Adjust the route to fetch incident details
                success: function (response) {
                    if (response.success) {
                        // Populate the modal with the log data
                        $('#incidentLog').val(response.data.log || '');
                        $('#logModal').data('incident-id', incidentId); // Store the incident ID in the modal
                        // Show the modal
                        $('#logModal').modal('show');
                    } else {
                        alert('Failed to fetch incident log.');
                    }
                },
                error: function (xhr) {
                    alert('An error occurred while fetching the incident log.');
                }
            });
        });
        $(document).ready(function() {
            $('#unit').select2({
                placeholder: "Select Unit",
                allowClear: true
            });
        });
        $('.view-booking-log').click(function (){
            const bookingId = $(this).data('booking-id');
            const isIncident = $(this).data('incident'); // Check if called from Incident Log Blade
            const token = '{{ csrf_token() }}';

            // Fetch booking log details via AJAX
            $.ajax({
                type: 'POST',
                url: '{{ route("get.unit.chat") }}',
                data: {
                    _token: token,
                    booking_id: bookingId
                },
                success: function (response) {
                    if (response.status) {
                        // Clear existing log entries
                        $('.chat_contents').empty();

                        // Populate the modal with log data
                        response.data.forEach(function (log) {
                            $('.chat_contents').append(`
                                <li>
                                    <p><strong>${log.user.role.name}:</strong> ${log.content}</p>
                                    <small>${new Date(log.created_at).toLocaleString()}</small>
                                </li>
                            `);
                        });

                        // Hide textarea and log button if called from Incident Log Blade
                        if (isIncident) {
                            $('.log-textarea, .log-button').hide();
                        } else {
                            $('.log-textarea, .log-button').show();
                        }

                        // Show the modal
                        $('#BookingUnitLogBook').modal('show');
                    } else {
                        alert('No logs found for this booking.');
                    }
                },
                error: function () {
                    alert('An error occurred while fetching the booking log.');
                }
            });
        });
        $('.view-booking-log').click(function () {
            const isIncident = $(this).data('incident'); // Check if called from Incident Log Blade

            // Show or hide the log options based on the flag
            if (isIncident) {
                $('.log-options').hide(); // Hide the Log Now option
                $('.TopHeadTxt').text('Booking Log'); // <-- SET the heading here
            } else {
                $('.log-options').show(); // Show the Log Now option
                $('.TopHeadTxt').text('Unit Log'); // <-- OR a different heading here
            }

            // Populate the modal with booking details
            const bookingId = $(this).data('booking-id');
            $('#booking_id').val(bookingId);

            // Show the modal
            $('#BookingUnitLogBook').modal('show');
        });
    });
</script>
<script src="{{ asset('unit-portal/js/highcharts.js') }}"></script>
<script src="{{ asset('unit-portal/js/variable-pie.js') }}"></script>
<script src="{{ asset('client-portal/js/bootbox.min.js') }}"></script>
<script src="{{ asset('client-portal/js/dashboard.js') }}"></script>
<script src="{{ asset('client-portal/js/login.js') }}"></script>
@endsection