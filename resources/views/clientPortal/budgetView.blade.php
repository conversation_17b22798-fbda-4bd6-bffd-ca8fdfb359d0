@extends('clientPortal.layout')
@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">


    <div class="Container_Fluid_Main">
        <div class="row headTitleBtm">
            <div class="col-sm-12">
                 <div class="GpBoxHeading no-bdr pb0"> 
                    <h4>
                        Budget
                    </h4>
                </div>
            </div>
        </div><!-- /.container-fluid -->
   

    <!-- Main content -->


            <div class="MainBoxRow mb25">
                <form method="GET" action="" id="budgetReportFilters" class="mb-4">
            <div class="row">
                <div class="col-md-3">
                    <label>Unit</label>
                    <select name="unit_id" class="form-control" id="unitSelect">
                        @foreach($units as $unit)
                            <option value="{{ $unit->clientUnitId }}" {{ ($selectedUnit == $unit->clientUnitId) ? 'selected' : '' }}>{{ $unit->alias }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-3">
                    <label>Financial Year</label>
                    <select name="financial_year" class="form-control" id="yearSelect">
                        @foreach($financialYears as $year)
                            <option value="{{ $year }}" {{ ($selectedYear == $year) ? 'selected' : '' }}>{{ $year }}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <label>Month</label>
                    <select name="month" class="form-control" id="monthSelect">
                        <option value="" {{ ($selectedMonth == '') ? 'selected' : '' }}>All</option>
                        @for($m=1;$m<=12;$m++)
                            <option value="{{ $m }}" {{ ($selectedMonth == $m) ? 'selected' : '' }}>{{ date('F', mktime(0,0,0,$m,1)) }}</option>
                        @endfor
                    </select>
                </div>
                <div class="col-md-2">
                    <label>Week</label>
                    <select name="week" class="form-control" id="weekSelect">
                        <option value="" {{ ($selectedWeek == '') ? 'selected' : '' }}>All</option>
                        @if($weeklyBudgets)
                            @foreach($weeklyBudgets as $wb)
                                <option value="{{ $wb->week_number }}" {{ ($selectedWeek == $wb->week_number) ? 'selected' : '' }}>Week {{ $wb->week_number }}</option>
                            @endforeach
                        @endif
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end mt-2">
                    <button  type="submit" class="mt25 btn-primary">Filter</button>
                </div>
            </div>
        </form>
                </div>

        <!-- Header Cards -->
        @include('common.dashboard.component.card.header_cards')


            <div class="row mb15">
            <div class="col-md-6">
             <div class="GpBox">  <div id="budgetPieChart" style="height: 350px;"></div> </div> 
            </div>
            <div class="col-md-6">
              <div class="GpBox">   <div id="budgetLineChart" style="height: 350px;"></div></div> 
            </div>
        </div>
       <div class="row mb15">
            <div class="col-md-12">
                 <div class="GpBox"> 

                        <div id="weeklyMultiLineChart" style="height: 400px;  width: 100%;; box-sizing: border-box;"></div>
              
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-12">
                <div class="table-responsive">
                    <table class="table table-striped table-bordered budgetTble" id="weeklyBudgetsTable">
                        <thead>
                            <tr>
                                <th>Wk #</th>
                                <th>Week Ending</th>
                                <th style='text-align: end;'>Weekly Budget</th>
                                <th style='text-align: end;'>Weekly Allowance</th>
                                <th style='text-align: end;'>Special Allowance</th>
                                <th style='text-align: end;'>Internal Transfers</th>
                                <th style='text-align: end;'>Total Allowance for the week</th>
                                <th style='text-align: end;'>Total Weekly Utilisation</th>
                                <th style='text-align: end;'>% of Utilisation</th>
                                <th style='text-align: end;'>Weekly Balance</th>
                                <th style='text-align: end;'>Cumulative Balance</th>
                                <th style='text-align: end;'>Balance Fund</th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($weeklyBudgets as $week)
                                <tr>
                                    <td>Week {{ $week->week_number }}</td>
                                    <td>{{ \Carbon\Carbon::parse($week->week_end_date)->format('d M Y') }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->weekly_budget, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->weekly_allowance, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->special_allowance, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->internal_transfers, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->total_weekly_allocation, 2) }}</td>
                                    <td style='text-align: end;'>
                                        <span class="utilisation-cell">
                                            £{{ number_format($week->total_weekly_utilisation, 2) }}
                                            <i class="fa fa-info-circle utilisation-info-btn" 
                                               data-weekly-budget-id="{{ $week->id }}" 
                                               data-logs-fetch="{{config('app.api_base_url')}}/client-portal/get-weekly-utilisation-logs"
                                               title="View utilisation details"></i>
                                        </span>
                                    </td>
                                    <td style='text-align: end;'>{{ number_format($week->percent_utilisation, 2) }}%</td>
                                    <td style='text-align: end;'>£{{ number_format($week->weekly_unutilised_fund, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->consolidated_unutilised_fund, 2) }}</td>
                                    <td style='text-align: end;'>£{{ number_format($week->balance_fund, 2) }}</td>
                                    <td>{{ $week->notes }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                        <tfoot>
                            <tr id="weeklyBudgetsTotalsRow" class="table-success font-weight-bold">
                                <th colspan="2" style="text-align:right">Total:</th>
                                <th style="text-align:end" id="total-weekly-budget">£0.00</th>
                                <th style="text-align:end" id="total-weekly-allowance">£0.00</th>
                                <th style="text-align:end" id="total-special-allowance">£0.00</th>
                                <th style="text-align:end" id="total-internal-transfers">£0.00</th>
                                <th style="text-align:end" id="total-allocation">£0.00</th>
                                <th style="text-align:end" id="total-utilisation">£0.00</th>
                                <th style="text-align:end" id="total-percent-utilisation">0.00%</th>
                                <th style="text-align:end" id="total-weekly-balance">£0.00</th>
                                <th style="text-align:end" id="total-cumulative-balance">£0.00</th>
                                <th style="text-align:end" id="total-balance-fund">£0.00</th>
                                <th></th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Weekly Utilisation Logs Modal -->
    <div class="modal fade" id="utilisationLogsModal" tabindex="-1" role="dialog" aria-labelledby="utilisationLogsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="utilisationLogsModalLabel">Weekly Utilisation Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="modalWeekDetails" class="mb-3">
                        <!-- Week details will be populated here -->
                    </div>
                    <div id="utilisationLogsContainer">
                        <!-- Utilisation logs table will be populated here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

</div>
@endsection
@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
<script src="{{ asset('js/budget-charts.js') }}"></script>
<script>
    // Pass data for charts
    document.addEventListener('DOMContentLoaded', function() {
        var pie = document.getElementById('budgetPieChart');
        if (pie) {
            pie.setAttribute('data-budget', {{ $budgetTotal }});
            pie.setAttribute('data-utilisation', {{ $utilisationTotal }});
        }
        var line = document.getElementById('budgetLineChart');
        if (line) {
            var weeks = @json($weeklyBudgets->pluck('week_number')->toArray());
            var utilisations = @json($weeklyBudgets->pluck('total_weekly_utilisation')->toArray());
            line.setAttribute('data-weeks', JSON.stringify(weeks));
            line.setAttribute('data-utilisations', JSON.stringify(utilisations));
        }
    });
</script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    function calculateWeeklyBudgetTotals() {
        const table = document.getElementById('weeklyBudgetsTable');
        if (!table) return;
        const rows = table.querySelectorAll('tbody tr');
        let totals = {
            'weekly-budget': 0,
            'weekly-allowance': 0,
            'special-allowance': 0,
            'internal-transfers': 0,
            'total-allocation': 0,
            'total-utilisation': 0,
            'percent-utilisation': 0,
            'weekly-balance': 0,
            'cumulative-balance': 0,
            'balance-fund': 0
        };
        rows.forEach(row => {
            function getCellValue(idx) {
                let cell = row.cells[idx];
                if (!cell) return 0;
                let text = cell.textContent.replace(/[^\d.-]/g, '');
                return parseFloat(text) || 0;
            }
            totals['weekly-budget'] += getCellValue(2);
            totals['weekly-allowance'] += getCellValue(3);
            totals['special-allowance'] += getCellValue(4);
            totals['internal-transfers'] += getCellValue(5);
            totals['total-allocation'] += getCellValue(6);
            totals['total-utilisation'] += getCellValue(7);
            totals['weekly-balance'] += getCellValue(9);
            totals['cumulative-balance'] += getCellValue(10);
            totals['balance-fund'] += getCellValue(11);
        });
        
        // Calculate the total percentage utilisation correctly
        totals['percent-utilisation'] = totals['total-allocation'] > 0 ? (totals['total-utilisation'] / totals['total-allocation']) * 100 : 0;
        document.getElementById('total-weekly-budget').textContent = '£' + totals['weekly-budget'].toFixed(2);
        document.getElementById('total-weekly-allowance').textContent = '£' + totals['weekly-allowance'].toFixed(2);
        document.getElementById('total-special-allowance').textContent = '£' + totals['special-allowance'].toFixed(2);
        document.getElementById('total-internal-transfers').textContent = '£' + totals['internal-transfers'].toFixed(2);
        document.getElementById('total-allocation').textContent = '£' + totals['total-allocation'].toFixed(2);
        document.getElementById('total-utilisation').textContent = '£' + totals['total-utilisation'].toFixed(2);
        document.getElementById('total-percent-utilisation').textContent = totals['percent-utilisation'].toFixed(2) + '%';
        document.getElementById('total-weekly-balance').textContent = '£' + totals['weekly-balance'].toFixed(2);
        document.getElementById('total-cumulative-balance').textContent = '£' + totals['cumulative-balance'].toFixed(2);
        document.getElementById('total-balance-fund').textContent = '£' + totals['balance-fund'].toFixed(2);
    }
    calculateWeeklyBudgetTotals();
});
</script>
<script>
localStorage.setItem('nsg-client-portal-login-user-id', "{{ auth()->user()->portal_id }}");
localStorage.setItem('nsg-client-portal-login-user-name', "{{ auth()->user()->name }}");
</script>
<script src="{{asset('unit-portal/js/variable-pie.js')}}"></script>
<script src="{{asset('client-portal/js/budgetView.js')}}"></script>
<script src="{{asset('client-portal/js/login.js')}}"></script>
<script>
document.addEventListener('DOMContentLoaded', function () {
    const unitSelect = document.getElementById('unitSelect');
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');
    const weekSelect = document.getElementById('weekSelect');
    // Use precomputed filterData from controller
    const filterData = @json($filterData);

    function resetSelect(select, keepFirst=true) {
        while (select.options.length > (keepFirst ? 1 : 0)) select.remove(keepFirst ? 1 : 0);
    }
    function showAlert(msg) {
        alert(msg);
    }
    unitSelect.addEventListener('change', function() {
        resetSelect(yearSelect);
        resetSelect(monthSelect);
        resetSelect(weekSelect);
        if (!unitSelect.value) return;
        const data = filterData[unitSelect.value];
        if (!data || !data.years.length) {
            showAlert('No financial budget allocation found for this unit.');
            return;
        }
        data.years.forEach(y => {
            let opt = document.createElement('option');
            opt.value = y; opt.text = y; yearSelect.appendChild(opt);
        });
    });
    yearSelect.addEventListener('change', function() {
        resetSelect(monthSelect);
        resetSelect(weekSelect);
        if (!unitSelect.value || !yearSelect.value) return;
        const data = filterData[unitSelect.value];
        const months = (data.monthsByYear && data.monthsByYear[yearSelect.value]) || [];
        months.forEach(m => {
            let opt = document.createElement('option');
            opt.value = m; opt.text = new Date(2000, m-1, 1).toLocaleString('default', { month: 'long' });
            monthSelect.appendChild(opt);
        });
    });
    monthSelect.addEventListener('change', function() {
        resetSelect(weekSelect);
        if (!unitSelect.value || !yearSelect.value || !monthSelect.value) return;
        const data = filterData[unitSelect.value];
        const weeks = (data.weeksByYearMonth && data.weeksByYearMonth[yearSelect.value] && data.weeksByYearMonth[yearSelect.value][monthSelect.value]) || [];
        weeks.forEach(w => {
            let opt = document.createElement('option');
            opt.value = w.week_number; opt.text = w.label; weekSelect.appendChild(opt);
        });
    });
});
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Check if we have default values and no current filters to auto-submit
    const unitSelect = document.getElementById('unitSelect');
    const yearSelect = document.getElementById('yearSelect');
    const monthSelect = document.getElementById('monthSelect');
    const weekSelect = document.getElementById('weekSelect');
    
    // Check if we have default values selected but no data is currently shown
    const hasDefaultUnit = unitSelect.value !== '' && unitSelect.value !== 'All';
    const hasDefaultYear = yearSelect.value !== '' && yearSelect.value !== 'All';
    const hasDefaultMonth = monthSelect.value === '' || monthSelect.value === 'All';
    const hasDefaultWeek = weekSelect.value === '' || weekSelect.value === 'All';
    
    // Auto-submit form if we have defaults but no URL parameters (first load)
    const urlParams = new URLSearchParams(window.location.search);
    const hasUrlParams = urlParams.has('unit_id') || urlParams.has('financial_year') || urlParams.has('month') || urlParams.has('week');
    
    if (hasDefaultUnit && hasDefaultYear && hasDefaultMonth && hasDefaultWeek && !hasUrlParams) {
        // Auto-submit the form to show data with defaults
        document.getElementById('budgetReportFilters').submit();
    }
});
</script>
<script>
// Handle utilisation info button click for drill-down
$(document).on('click', '.utilisation-info-btn', function() {
    const weeklyBudgetId = $(this).data('weekly-budget-id');
    const logsFetch = $(this).data('logs-fetch');
    
    // Show loading state
    $('#utilisationLogsModal').modal('show');
    $('#modalWeekDetails').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> Loading...</div>');
    $('#utilisationLogsContainer').html('');
    
    // Make AJAX request to get utilisation logs
    $.ajax({
        url: logsFetch,
        method: 'POST',
        data: {
            weekly_budget_id: weeklyBudgetId,
            _token: $('meta[name="csrf-token"]').attr('content')
        },
        success: function(response) {
            if (response.status === 'success') {
                // Populate modal with week details
                const weekDetails = `
                    <div class="card">
                        <div class="card-body">
                            <h6 class="card-title">Week: ${response.weekly_budget.week_period}</h6>
                            <p class="card-text">
                                <strong>Total Weekly Utilisation:</strong> £${parseFloat(response.weekly_budget.total_weekly_utilisation).toFixed(2)}<br>
                                <strong>Week Number:</strong> ${response.weekly_budget.week_number}
                            </p>
                        </div>
                    </div>`;
                
                $('#modalWeekDetails').html(weekDetails);
                
                // Populate logs table
                if (response.logs && response.logs.length > 0) {
                    let logsTable = `
                        <table class="table table-striped logs-table">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Type</th>
                                    <th>Amount</th>
                                    <th>Source</th>
                                    <th>Description</th>
                                </tr>
                            </thead>
                            <tbody>`;
                    
                    response.logs.forEach(log => {
                        const adjustmentClass = log.adjustment_type === 'Addition' ? 'adjustment-addition' : 'adjustment-deduction';
                        const adjustmentSymbol = log.adjustment_type === 'Addition' ? '+' : '-';
                        
                        logsTable += `
                            <tr>
                                <td>${log.created_at_human}</td>
                                <td class="${adjustmentClass}">${log.adjustment_type}</td>
                                <td class="${adjustmentClass}">${adjustmentSymbol}£${parseFloat(log.adjustment_amount).toFixed(2)}</td>
                                <td>${log.source_type} #${log.source_id || 'N/A'}</td>
                                <td title="${log.description || ''}">${log.description || 'N/A'}</td>
                            </tr>`;
                    });
                    
                    logsTable += '</tbody></table>';
                    $('#utilisationLogsContainer').html(logsTable);
                } else {
                    $('#utilisationLogsContainer').html('<div class="no-logs-message">No utilisation details found for this week.</div>');
                }
            } else {
                $('#modalWeekDetails').html('<div class="alert alert-danger">Error loading utilisation details. Please try again.</div>');
                $('#utilisationLogsContainer').html('');
            }
        },
        error: function(xhr, status, error) {
            console.error('Utilisation Logs AJAX Error:', error);
            $('#modalWeekDetails').html('<div class="alert alert-danger">Error loading utilisation details. Please try again.</div>');
            $('#utilisationLogsContainer').html('');
        }
    });
});
</script>
@endsection
@push('css')
<style>
/* Highlight filter section visually */
#budgetReportFilters {
    background: linear-gradient(90deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(21,101,192,0.10);
    padding: 24px 28px 14px 28px;
    margin-bottom: 36px;
    border: 1.5px solid #90caf9;
    display: block;
}
#budgetReportFilters label {
    font-weight: 700;
    color: #1976d2;
    margin-bottom: 6px;
    letter-spacing: 0.5px;
}
#budgetReportFilters .form-control {
    border-radius: 8px;
    border: 1.5px solid #64b5f6;
    box-shadow: 0 1px 2px rgba(21,101,192,0.04);
    font-size: 16px;
    color: #1976d2;
    background: #fff;
    transition: border 0.2s;
}
#budgetReportFilters .form-control:focus {
    border-color: #1976d2;
    outline: none;
}
#budgetReportFilters .btn-primary {
    background: linear-gradient(90deg, #1976d2 0%, #42a5f5 100%);
    border: none;
    border-radius: 8px;
    font-weight: 700;
    font-size: 17px;
    padding: 10px 0;
    transition: background 0.2s;
    box-shadow: 0 2px 8px rgba(21,101,192,0.08);
}
#budgetReportFilters .btn-primary:hover {
    background: linear-gradient(90deg, #1565c0 0%, #1976d2 100%);
}
#budgetReportFilters .row > div {
    margin-bottom: 16px;
}
#budgetReportFilters select {
    cursor: pointer;
}

/* Utilisation Info Button Styles */
.utilisation-info-btn {
    color: #007bff;
    cursor: pointer;
    margin-left: 5px;
    font-size: 14px;
    transition: color 0.2s ease;
}

.utilisation-info-btn:hover {
    color: #0056b3;
}

.utilisation-cell {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 5px;
}

/* Modal Styles */
.logs-table {
    width: 100%;
    margin-top: 15px;
}

.logs-table th,
.logs-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.logs-table th {
    background-color: #f8f9fa;
    font-weight: bold;
}

.adjustment-addition {
    color: #28a745;
    font-weight: bold;
}

.adjustment-deduction {
    color: #dc3545;
    font-weight: bold;
}

.no-logs-message {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 20px;
}

#modalWeekDetails .card {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

#modalWeekDetails .card-body {
    padding: 1rem;
}

#modalWeekDetails .card-title {
    color: #495057;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

#modalWeekDetails .card-text {
    color: #6c757d;
    margin-bottom: 0;
}
</style>
@endpush