<!-- ================================================================
     INTERNAL TRANSFER MODAL SECTION
     ================================================================ -->
<style>
    #transferModal {
        z-index: 1050 !important;
    }
    #transferModal .modal-backdrop {
        z-index: 1040 !important;
    }
</style>
<div class="modal" id="transferModal" tabindex="-1" role="dialog" aria-labelledby="transferModalLabel" aria-hidden="true">
    <div class="modal-dialog" style="width: 80%; max-width: 900px; margin: 30px auto;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header header_txt">
                <h4 class="modal-title"><b>Internal Transfer - Week <span id="transferWeek"></span></b>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <p>Current Unit: <strong>{{ $unit->name }}</strong></p>
                    <p>Available Balance: £<span id="currentBalance"></span></p>
                </div>

                <!-- Transfer Type Selection -->
                <div class="form-group mb-4">
                    <label class="form-label"><strong>Transfer Type:</strong></label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="transferType" id="transferFrom" value="from"
                                       data-input-type="transfer-type" data-transfer-direction="from" checked>
                                <label class="form-check-label" for="transferFrom">
                                    <i class="fa fa-arrow-left text-danger"></i> Transfer From This Unit
                                    <small class="d-block text-muted">Send money to another unit</small>
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="radio" name="transferType" id="transferTo" value="to"
                                       data-input-type="transfer-type" data-transfer-direction="to">
                                <label class="form-check-label" for="transferTo">
                                    <i class="fa fa-arrow-right text-success"></i> Transfer To This Unit
                                    <small class="d-block text-muted">Receive money from another unit</small>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <h6 id="transferTableTitle">Send money to other units:</h6>
                <div class="table-responsive">
                    <table class="table table-bordered" id="transferUnitsTable">
                        <thead>
                            <tr>
                                <th>Unit Name</th>
                                <th>Current Balance</th>
                                <th>Transfer Amount</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody id="transferUnitsBody">
                            <!-- Will be populated via AJAX -->
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <!-- Transfer Session Info -->
                <div class="transfer-session-info mr-auto">
                    <small class="text-muted">
                        <span id="transferCount">0</span> transfer(s) completed this session
                    </small>
                </div>
                
                <!-- Modal Action Buttons -->
                <button type="button" class="btn btn-secondary" id="closeTransferModal" data-action="close-transfer-modal">Close</button>
                <button type="button" class="btn btn-success" id="doneTransferModal" data-action="done-transfer-modal">
                    <i class="fa fa-check"></i> Done & Refresh
                </button>
            </div>
        </div>
    </div>
</div>

<!-- ================================================================
     TRANSFER HISTORY MODAL SECTION
     ================================================================ -->
<div class="modal" id="transferHistoryModal" tabindex="-1" role="dialog" aria-labelledby="transferHistoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" style="max-width: 1000px; margin: 30px auto;">
        <div class="modal-content">
            <div class="modal-header header_txt">
                <h4 class="modal-title"><b>Transfer History - Week <span id="historyWeek"></span></b>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <p>Unit: <strong id="historyUnitName"></strong></p>
                    <p>Net Transfer Amount: £<span id="netTransferAmount"></span></p>
                </div>

                <div class="row">
                    <!-- Incoming Transfers -->
                    <div class="col-md-6">
                        <h6 class="text-success"><i class="fa fa-arrow-down"></i> Incoming Transfers</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-success">
                                    <tr>
                                        <th>From Unit</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody id="incomingTransfersBody">
                                    <!-- Will be populated via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div class="text-right">
                            <strong>Total Incoming: £<span id="totalIncoming">0.00</span></strong>
                        </div>
                    </div>

                    <!-- Outgoing Transfers -->
                    <div class="col-md-6">
                        <h6 class="text-danger"><i class="fa fa-arrow-up"></i> Outgoing Transfers</h6>
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-danger">
                                    <tr>
                                        <th>To Unit</th>
                                        <th>Amount</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody id="outgoingTransfersBody">
                                    <!-- Will be populated via AJAX -->
                                </tbody>
                            </table>
                        </div>
                        <div class="text-right">
                            <strong>Total Outgoing: £<span id="totalOutgoing">0.00</span></strong>
                        </div>
                    </div>
                </div>

                <!-- Transfer Summary -->
                <div class="mt-3 p-3 bg-light border rounded">
                    <h6>Transfer Summary</h6>
                    <div class="row">
                        <div class="col-md-4">
                            <small class="text-muted">Total Incoming:</small><br>
                            <span class="text-success">+£<span id="summaryIncoming">0.00</span></span>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">Total Outgoing:</small><br>
                            <span class="text-danger">-£<span id="summaryOutgoing">0.00</span></span>
                        </div>
                        <div class="col-md-4">
                            <small class="text-muted">Net Amount:</small><br>
                            <strong id="summaryNet">£0.00</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
