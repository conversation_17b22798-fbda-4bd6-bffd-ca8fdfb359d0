@extends('clientPortal.layout')
@section('title', 'Dashboard | Client Portal')

@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper clientvalue" client="{{ auth()->user()->portal_id }}">
    <!-- Content Header (Page header) -->

    <div class="Container_Fluid_Main">
        <div class="row headTitleBtm">
            <div class="col-sm-12">
                <div class="GpBoxHeading no-bdr pb0">
                    <h4>
                        Staff Compliance
                    </h4>
                </div>
            </div>
        </div><!-- /.container-fluid -->
    </div>

    <!-- Main content -->
    <div class="Container_Fluid_Main">
        <input type="hidden" id="client_id" class="datepicker">
        <div class="MainBoxRow mb15">
            <div class="row ">
                <div class="col-sm-5 mt7">
                    <b style="font-size: 16px;"> Staff Who Have Worked in This Home </b>
                </div>
                <div class="col-sm-7 text-right">
                    <form method="GET" action="{{ route('staff.complaince') }}" class="mb-3">
                        <div style="float: right;">
                            <input style="float: left; width: 300px;" type="text" name="search" class="form-control" placeholder="Search by name or code" value="{{ request('search') }}">
                            <button style="float: left; margin-left: 15px; margin-right: 5px;" type="submit" class="btn btn-primary">Search</button>
                            <a style="float: left;" href="{{ route('staff.complaince') }}" class="btn btn-reset">
                                <img class="reset_img" src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                            </a>
                        </div>
                    </form>

                </div>
            </div>
        </div>

        <div class="card">

            <div class="card-body">


                @if($staff->count())
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-dark">
                            <tr>
                                <th>Staff Code</th>
                                <th>Staff Name</th>
                                <th>Category</th>
                                <th>Total Hours in Amica</th>
                                <th>DBS</th>
                                <th>RTW</th>
                                <th style="text-align: right">NMC & Profile</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($staff as $index => $s)
                            <tr>
                                <td>{{ $s->code ?? 'N/A' }}</td>
                                <td>{{ $s->forname }} {{ $s->surname }}</td>
                                <td>{{ $s->category ?? 'N/A' }}</td>
                                <td>{{ $s->total_hours }}</td>
                                <td>
                                    @if($s->validCertificate)
                                    <a href="{{ asset('https://nursesgroupadmin.co.uk/storage/app/applicant/applicant_dbs/' . $s->validCertificate) }}" target="_blank" class="btn btn-three-sm"><i class="fa fa-file-text" aria-hidden="true"></i></a>
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>
                                <td>
                                    @if($s->rtwfile)
                                    <a href="{{ asset('https://nursesgroupadmin.co.uk/storage/app/staff/staff_rtw/visa/' . $s->rtwfile) }}" target="_blank" class="btn btn-three-sm"><i class="fa fa-file-text" aria-hidden="true"></i></a>
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif
                                </td>

                                <td style="text-align: right">
                                    @if($s->nmcfile)
                                    <a href="{{ asset('https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/' . $s->nmcfile) }}" target="_blank" class="btn btn-three-sm"><i class="fa fa-file-text" aria-hidden="true"></i></a>
                                    @else
                                    <span class="text-muted"></span>
                                    @endif
                                    @if($s->profileDocumentFile)
                                    <a href="{{ asset('https://nursesgroupadmin.co.uk/storage/app/staff/staff_profile/' . $s->profileDocumentFile) }}" target="_blank" class="btn btn-three-sm">
                                        <i class="fa fa-user" aria-hidden="true"></i>
                                    </a>
                                    @else
                                    <span class="text-muted">N/A</span>
                                    @endif

                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <!-- Pagination Links -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <!-- #region -->
                        <div class="row">
                            <div class="col-md-6">
                                Showing {{ $staff->firstItem() }} - {{ $staff->lastItem() }} of {{ $staff->total() }} staff
                            </div>
                            <div class="col-md-6 text-right">
                                {{ $staff->links() }}
                            </div>
                        </div>
                    </div>
                </div>
                @else
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="thead-dark">
                            <tr>
                                <th>#</th>
                                <th>Full Name</th>
                                <th>Category</th>
                                <th>DBS</th>
                                <th>RTW</th>
                                <th>NMC</th>
                                <th>Profile</th>
                            </tr>
                        </thead>
                        <tbody>


                        </tbody>
                    </table>

                    <!-- Pagination Links -->
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                Showing {{ $staff->firstItem() }} - {{ $staff->lastItem() }} of {{ $staff->total() }} staff
                            </div>
                            <div class="col-md-6 text-right">
                                {{ $staff->links() }}
                            </div>
                        </div>

                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@endsection