@extends('common.layout')

@section('content')

<div class="content-wrapper">
    <div class="content-header">

<div class="Container_Fluid_Main mt25">
      <div class="row">
          <div class="col-md-3">
               <div class="GpBoxHeading no-bdr pb0">
                  <h4>
                     Roles List
                  </h4>
              </div>
          </div>
          <div class="col-md-9 text-right">
              <div class="">
                        @if(session('success'))
                        <div class="alert alert-success mb-0" role="alert" style="text-align: left;">
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        @endif

                        @if(session('error'))
                        <div class="alert alert-danger mb-0" role="alert" style="text-align: left;">
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        @endif
                    </div>
                    <div class="">
                        <button type="button" class="btn btn-primary add-role" data-toggle="modal"
                            data-target="#roleModal">
                            Add Role
                        </button>
                    </div>
          </div>
      </div>
  </div>

    </div>

    <section class="ClientCtBG content">

        <!-- Yajra DataTable -->
        <table class="table table-striped table-bordered table-hover RolList RolListTable" id="roles-table">
            <thead>
                <tr>
                    <th style="text-align:left">ID</th>
                    <th>Name</th>
                    <th>Actions</th>
                </tr>
            </thead>
        </table>
    </section>
</div>
@include('components.role-add-edit')
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    $(".add-role").click(function() {
        $("#roleForm").attr('action', '{{ route("users.roles.store") }}');
        $('#roleName').val('');

        // Reset budget approval checkboxes
        $('#ableToApproveWithinBudget').prop('checked', false);
        $('#ableToApproveWeeklyBudget').prop('checked', false);
        $('#ableToApproveSpecialAllowanceBudget').prop('checked', false);

        // Reset visual states
        $('.budget-approval-item').removeClass('checked');

        $('.permission-checkbox').prop('checked', false);
        $(".submodule-checkbox").each(function() {
            var submoduleId = $(this).data('submodule-id');
            updateSubmoduleCheckboxState(submoduleId);
        });
        $(".module-checkbox").each(function() {
            var moduleId = $(this).data('module-id');
            updateModuleCheckboxState(moduleId);
        });
    });
    $('#roles-table').DataTable({
        processing: true,
        serverSide: true,
        ajax: '{{ route("users.roles") }}',
        columns: [{
                data: 'role_id',
                name: 'role_id'
            },
            {
                data: 'name',
                name: 'name'
            },
            {
                data: 'actions',
                name: 'actions',
                orderable: false,
                searchable: false
            }
        ]
    });
    // Handle module checkbox -> check all submodules and permissions
    $('.module-checkbox').on('change', function() {
        var moduleId = $(this).data('module-id');
        var checked = $(this).is(':checked');
        $('.submodule-checkbox[data-module-id="' + moduleId + '"]').prop('checked', checked)
            .trigger('change');
    });

    // Handle submodule checkbox -> check all its permissions
    $('.submodule-checkbox').on('change', function() {
        var submoduleId = $(this).data('submodule-id');
        var checked = $(this).is(':checked');
        $('.permission-checkbox[data-submodule-id="' + submoduleId + '"]').prop('checked',
            checked);

        // Update module checkbox state
        var moduleId = $(this).data('module-id');
        updateModuleCheckboxState(moduleId);
    });

    // Handle individual permission checkbox -> update submodule state
    $('.permission-checkbox').on('change', function() {
        var submoduleId = $(this).data('submodule-id');
        updateSubmoduleCheckboxState(submoduleId);

        var moduleId = $('.submodule-checkbox[data-submodule-id="' + submoduleId + '"]').data(
            'module-id');
        updateModuleCheckboxState(moduleId);
    });

    function updateSubmoduleCheckboxState(submoduleId) {
        var all = $('.permission-checkbox[data-submodule-id="' + submoduleId + '"]');
        var checked = all.filter(':checked').length === all.length;
        $('.submodule-checkbox[data-submodule-id="' + submoduleId + '"]').prop('checked', checked);
    }

    function updateModuleCheckboxState(moduleId) {
        var all = $('.submodule-checkbox[data-module-id="' + moduleId + '"]');
        var checked = all.filter(':checked').length === all.length;
        $('.module-checkbox[data-module-id="' + moduleId + '"]').prop('checked', checked);
    }
    document.getElementById('roleForm').addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Validate role name
        const roleName = document.getElementById('roleName').value.trim();
        if (!roleName) {
            isValid = false;
            errorMessages.push('Role name is required.');
        }

        // Validate permissions
        const permissions = document.querySelectorAll('input[name="permissions[]"]:checked');
        if (permissions.length === 0) {
            isValid = false;
            errorMessages.push('At least one permission must be selected.');
        }

        // Validate type and client unit
        const userType = document.getElementById('userType');
        if (userType && userType.value === 'unit') {
            const clientUnit = document.getElementById('userUnitClient').value;
            if (!clientUnit) {
                isValid = false;
                errorMessages.push('Client unit is required for unit type.');
            }
        }

        // Show error messages if validation fails
        if (!isValid) {
            e.preventDefault();
            alert(errorMessages.join('\n'));
        }
    });
    $("body").on("click", ".edit-role", function() {
        var role_data = $(this).data('role');
        if (typeof role_data == 'string') {
            role_data = JSON.parse(role_data);
        }
        var roleId = role_data.role_id;
        var name = role_data.name;
        var permissions = role_data.permissions || [];
        $(".add-role").trigger('click');
        $("#roleForm").attr('action', $(this).data('url'));


        $('#roleName').val(name);
        $('#userType').val(role_data.type);
        $('#userUnitClient').val(role_data.type_id);

        // Set budget approval checkboxes
        $('#ableToApproveWithinBudget').prop('checked', role_data.able_to_approve_within_budget == 1);
        $('#ableToApproveWeeklyBudget').prop('checked', role_data.able_to_approve_weekly_budget == 1);
        $('#ableToApproveSpecialAllowanceBudget').prop('checked', role_data.able_to_approve_special_allowance_budget == 1);

        // Update visual states for budget approval items
        $('.budget-checkbox').each(function() {
            var parentItem = $(this).closest('.budget-approval-item');
            if ($(this).is(':checked')) {
                parentItem.addClass('checked');
            } else {
                parentItem.removeClass('checked');
            }
        });

        // Ensure dependencies are properly maintained when editing
        if (role_data.able_to_approve_special_allowance_budget == 1) {
            $('#ableToApproveWithinBudget').prop('checked', true);
            $('#ableToApproveWeeklyBudget').prop('checked', true);
            $('#ableToApproveWithinBudget').closest('.budget-approval-item').addClass('checked');
            $('#ableToApproveWeeklyBudget').closest('.budget-approval-item').addClass('checked');
        } else if (role_data.able_to_approve_weekly_budget == 1) {
            $('#ableToApproveWithinBudget').prop('checked', true);
            $('#ableToApproveWithinBudget').closest('.budget-approval-item').addClass('checked');
        }

        if (role_data.type == 'unit') {
            $("#unitClientDiv").show();
        } else {
            $("#unitClientDiv").hide();
        }
        $('.permission-checkbox').prop('checked', false);
        permissions.forEach(function(permission) {
            $('.permission-checkbox[value="' + permission.permission_id + '"]').prop('checked',
                true);
        });
        $(".submodule-checkbox").each(function() {
            var submoduleId = $(this).data('submodule-id');
            updateSubmoduleCheckboxState(submoduleId);
        });
        $(".module-checkbox").each(function() {
            var moduleId = $(this).data('module-id');
            updateModuleCheckboxState(moduleId);
        });
        $(".submodule-checkbox").each(function() {
            var submoduleId = $(this).data('submodule-id');
            updateSubmoduleCheckboxState(submoduleId);
        });
        $(".module-checkbox").each(function() {
            var moduleId = $(this).data('module-id');
            updateModuleCheckboxState(moduleId);
        });
    });
    $("body").on("click", ".delete-role", function() {
        var url = $(this).data('url');
        Swal.fire({
            title: 'Are you sure?',
            text: "You won't be able to revert this!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Yes, delete it!'
        }).then((result) => {
            if (result.value) {
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: {
                        "_token": "{{ csrf_token() }}",
                    },
                    success: function(response) {
                        Swal.fire(
                            'Deleted!',
                            'Role has been deleted.',
                            'success'
                        );
                        $('#roles-table').DataTable().ajax.reload();
                    },
                    error: function(response) {
                        Swal.fire(
                            'Error!',
                            response.responseJSON.message,
                            'error'
                        );
                    }
                });
            }
        });
    });
    $("#userType").change(function() {
        var userType = $(this).val();
        if (userType == 'unit') {
            $("#unitClientDiv").show();
        } else {
            $("#unitClientDiv").hide();
        }
    });

    // Update visual state when individual checkboxes change
    $(document).on('change', '.budget-checkbox', function() {
        var parentItem = $(this).closest('.budget-approval-item');
        if ($(this).is(':checked')) {
            parentItem.addClass('checked');
        } else {
            parentItem.removeClass('checked');
        }

        // Handle hierarchical dependencies
        handleBudgetApprovalDependencies($(this));
    });

    // Function to handle budget approval dependencies
    function handleBudgetApprovalDependencies(changedCheckbox) {
        var checkboxId = changedCheckbox.attr('id');
        var isChecked = changedCheckbox.is(':checked');

        if (isChecked) {
            // If Weekly Budget is checked, automatically check Within Budget
            if (checkboxId === 'ableToApproveWeeklyBudget') {
                $('#ableToApproveWithinBudget').prop('checked', true);
                $('#ableToApproveWithinBudget').closest('.budget-approval-item').addClass('checked');
            }

            // If Special Allowance Budget is checked, automatically check both previous options
            if (checkboxId === 'ableToApproveSpecialAllowanceBudget') {
                $('#ableToApproveWithinBudget').prop('checked', true);
                $('#ableToApproveWeeklyBudget').prop('checked', true);
                $('#ableToApproveWithinBudget').closest('.budget-approval-item').addClass('checked');
                $('#ableToApproveWeeklyBudget').closest('.budget-approval-item').addClass('checked');
            }
        } else {
            // If Within Budget is unchecked, uncheck dependent options
            if (checkboxId === 'ableToApproveWithinBudget') {
                $('#ableToApproveWeeklyBudget').prop('checked', false);
                $('#ableToApproveSpecialAllowanceBudget').prop('checked', false);
                $('#ableToApproveWeeklyBudget').closest('.budget-approval-item').removeClass('checked');
                $('#ableToApproveSpecialAllowanceBudget').closest('.budget-approval-item').removeClass('checked');
            }

            // If Weekly Budget is unchecked, uncheck Special Allowance Budget
            if (checkboxId === 'ableToApproveWeeklyBudget') {
                $('#ableToApproveSpecialAllowanceBudget').prop('checked', false);
                $('#ableToApproveSpecialAllowanceBudget').closest('.budget-approval-item').removeClass('checked');
            }
        }
    }

});
</script>
@endsection

<style>
table.dataTable th.dt-type-numeric,
table.dataTable th.dt-type-date,
table.dataTable td.dt-type-numeric,
table.dataTable td.dt-type-date {
    text-align: left;
}

.modal-header .close {
    margin-top: -28px !important;
    font-size: 28px !important;
    margin-right: 19px;
    opacity: 9;
    position: relative !important;
    z-index: 10000 !important;
}

.modal-header {
    background-color: #f8f9fa;
}

.modal-title {
    margin: 0;
    line-height: 1.42857143;
    color: #494949;
    font-weight: 700;
    position: relative;
    top: 2px;
    font-size: 24px;
}

.submodules .submodule label {
    color: #494949;
    margin-top: 8px;

}

.submodules .submodule .permissions label {
    color: #000;
    margin-top: 0;
    font-weight: 600;
    font-size: 12px;
}


label.PerHead {
    text-transform: uppercase;
    font-size: 16px;
    margin-bottom: 10px;
}

#permissionsTree .module {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 7px;
    background: #f7f7f7;
}

.module label strong {
    color: #f36b22;
    font-weight: 700 !important;
    font-size: 16px;
}

.modal {
    padding-top: 0px !important;
}

.modal-body {
    padding: 20px 20px 2px !important;
}

.form-check {
    margin-bottom: 10px;
}

.form-check-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-check-input {
    margin-right: 8px;
}

label.PerHead {
    color: #f36b22 !important;
    border-bottom: 2px solid #f36b22;
    padding-bottom: 5px;
    display: inline-block;
    margin-bottom: 15px !important;
}

/* Budget Approval Section Styling */
.budget-approval-section {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.budget-hierarchy-note {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 6px;
    padding: 10px 15px;
    margin-bottom: 15px;
    text-align: center;
}

.budget-hierarchy-note i {
    color: #1976d2;
    margin-right: 5px;
}

.budget-approval-container {
    margin-bottom: 0;
    position: relative;
}

.budget-approval-container::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #f36b22, #ff9800);
    opacity: 0.3;
}

.budget-approval-item {
    background: white;
    border: 1px solid #e0e6ed;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 10px;
    transition: all 0.3s ease;
    position: relative;
    margin-left: 20px;
}

.budget-approval-item::before {
    content: '';
    position: absolute;
    left: -30px;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 2px;
    background: #f36b22;
    opacity: 0.5;
}

.budget-approval-item[data-level="1"]::before {
    background: #28a745;
}

.budget-approval-item[data-level="2"]::before {
    background: #ffc107;
}

.budget-approval-item[data-level="3"]::before {
    background: #dc3545;
}

.level-indicator {
    display: inline-block;
    background: #f36b22;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: 8px;
    font-weight: 600;
    text-transform: uppercase;
}

.budget-approval-item[data-level="1"] .level-indicator {
    background: #28a745;
}

.budget-approval-item[data-level="2"] .level-indicator {
    background: #ffc107;
    color: #212529;
}

.budget-approval-item[data-level="3"] .level-indicator {
    background: #dc3545;
}

.budget-approval-item:hover {
    border-color: #f36b22;
    box-shadow: 0 2px 8px rgba(243, 107, 34, 0.15);
    transform: translateY(-1px);
}

.budget-approval-item .custom-control {
    min-height: auto;
}

.budget-approval-item .custom-control-label {
    display: flex;
    flex-direction: column;
    padding-left: 10px;
    cursor: pointer;
}

.checkbox-title {
    font-weight: 600;
    color: #2c3e50;
    font-size: 14px;
    margin-bottom: 4px;
}

.checkbox-description {
    font-size: 12px;
    color: #6c757d;
    line-height: 1.4;
}

.custom-control-input:checked ~ .custom-control-label .checkbox-title {
    color: #f36b22;
}

.custom-control-input:checked ~ .custom-control-label .checkbox-description {
    color: #5a6169;
}

/* Custom checkbox styling */
.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #f36b22;
    border-color: #f36b22;
}

.custom-control-input:focus ~ .custom-control-label::before {
    box-shadow: 0 0 0 0.2rem rgba(243, 107, 34, 0.25);
}

.budget-approval-item.checked {
    border-color: #f36b22;
    background: linear-gradient(135deg, #fff5f0 0%, #ffffff 100%);
}

/* Icon styling for section header */
.PerHead i {
    margin-right: 8px;
    color: #f36b22;
}
</style>
