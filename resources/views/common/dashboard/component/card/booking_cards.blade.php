@section('booking_cards')
 <h6 class="Exptitle">Reason for Booking</h6>

<div class="booking_card_grid">

  <div class="card">
     <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/sikness.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Sickness</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['sickness']['cost']) && is_numeric($dashboardData['booking_reasons']['sickness']['cost']) ? number_format($dashboardData['booking_reasons']['sickness']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['sickness']['count']) ? $dashboardData['booking_reasons']['sickness']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card highlight">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/holiday.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Holiday</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['holiday']['cost']) && is_numeric($dashboardData['booking_reasons']['holiday']['cost']) ? number_format($dashboardData['booking_reasons']['holiday']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['holiday']['count']) ? $dashboardData['booking_reasons']['holiday']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/vacent.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Vacant</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['vacant']['cost']) && is_numeric($dashboardData['booking_reasons']['vacant']['cost']) ? number_format($dashboardData['booking_reasons']['vacant']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['vacant']['count']) ? $dashboardData['booking_reasons']['vacant']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
     <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/resident.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Resident Admission</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['resident_admission']['cost']) && is_numeric($dashboardData['booking_reasons']['resident_admission']['cost']) ? number_format($dashboardData['booking_reasons']['resident_admission']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['resident_admission']['count']) ? $dashboardData['booking_reasons']['resident_admission']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/one-two.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">One to One</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['one_to_one']['cost']) && is_numeric($dashboardData['booking_reasons']['one_to_one']['cost']) ? number_format($dashboardData['booking_reasons']['one_to_one']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['one_to_one']['count']) ? $dashboardData['booking_reasons']['one_to_one']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/extra-staff.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Extra Staff</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['extra_staff']['cost']) && is_numeric($dashboardData['booking_reasons']['extra_staff']['cost']) ? number_format($dashboardData['booking_reasons']['extra_staff']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['extra_staff']['count']) ? $dashboardData['booking_reasons']['extra_staff']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/management.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Management</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['management']['cost']) && is_numeric($dashboardData['booking_reasons']['management']['cost']) ? number_format($dashboardData['booking_reasons']['management']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['management']['count']) ? $dashboardData['booking_reasons']['management']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>

  <div class="card">
    <div class="icon-box">
        <img  src="{{ asset('unit-portal/images/others.svg') }}" alt="Reason for Booking">
    </div>
    <div class="content_reason">
      <div class="title_reason">Others</div>
      <div class="metrics_reason">
        <div>Cost : <strong>£{{ isset($dashboardData['booking_reasons']['others']['cost']) && is_numeric($dashboardData['booking_reasons']['others']['cost']) ? number_format($dashboardData['booking_reasons']['others']['cost'], 2) : '0.00' }}</strong></div>
        <div>Count : <strong>{{ isset($dashboardData['booking_reasons']['others']['count']) ? $dashboardData['booking_reasons']['others']['count'] : '0' }}</strong></div>
      </div>
    </div>
  </div>
</div>

@endsection