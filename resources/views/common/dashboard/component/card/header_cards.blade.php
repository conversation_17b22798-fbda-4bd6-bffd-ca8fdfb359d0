{{-- Debug: Show raw dashboard data structure --}}
@if(config('app.debug'))
    <!-- DEBUG: Dashboard Data Structure -->
    <!-- 
    @if(isset($dashboardData))
        Dashboard Data Exists: YES
        Budget Total: {{ $dashboardData['budget']['total'] ?? 'NOT SET' }}
        Budget Structure: {{ json_encode($dashboardData['budget'] ?? 'NO BUDGET KEY') }}
    @else
        Dashboard Data Exists: NO
    @endif
    -->
@endif

<div class="row header_cards">
    <div class="col-md-3">
        <div class="card">
            <div class="title">Budget</div>
            <div class="value">
                @if(isset($dashboardData) && isset($dashboardData['budget']) && isset($dashboardData['budget']['total']) && is_numeric($dashboardData['budget']['total']))
                    £{{ number_format($dashboardData['budget']['total'], 2) }}
                @else
                    £0.00
                @endif
            </div>
            <div class="change {{ (isset($dashboardData['budget']['trend']) && $dashboardData['budget']['trend'] == 'positive') ? 'positive' : 'negative' }}">
                {{ (isset($dashboardData['budget']['trend']) && $dashboardData['budget']['trend'] == 'positive') ? '↑' : '↓' }} 
                {{ isset($dashboardData['budget']['percentage']) && is_numeric($dashboardData['budget']['percentage']) ? abs($dashboardData['budget']['percentage']) : '0' }}% from last month
            </div>
            <div class="icon">
                <img class="header_cards_img" src="{{ asset('unit-portal/images/budget.svg') }}" alt="Budget">
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="title">Expenses</div>
            <div class="value">
                @if(isset($dashboardData) && isset($dashboardData['expense']) && isset($dashboardData['expense']['total']) && is_numeric($dashboardData['expense']['total']))
                    £{{ number_format($dashboardData['expense']['total'], 2) }}
                @else
                    £0.00
                @endif
            </div>
            <div class="change {{ (isset($dashboardData['expense']['trend']) && $dashboardData['expense']['trend'] == 'positive') ? 'positive' : 'negative' }}">
                {{ (isset($dashboardData['expense']['trend']) && $dashboardData['expense']['trend'] == 'positive') ? '↑' : '↓' }} 
                {{ isset($dashboardData['expense']['percentage']) && is_numeric($dashboardData['expense']['percentage']) ? abs($dashboardData['expense']['percentage']) : '0' }}% from last month
            </div>
            <div class="icon">
                <img class="header_cards_img" src="{{ asset('unit-portal/images/expense.svg') }}" alt="Expenses">
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="title">Balance</div>
            <div class="value">
                @if(isset($dashboardData) && isset($dashboardData['balance']) && is_numeric($dashboardData['balance']))
                    £{{ number_format($dashboardData['balance'], 2) }}
                @else
                    £0.00
                @endif
            </div>
            <div class="change {{ (isset($dashboardData['balance']) && is_numeric($dashboardData['balance']) && $dashboardData['balance'] >= 0) ? 'positive' : 'negative' }}">
                {{ (isset($dashboardData['balance']) && is_numeric($dashboardData['balance']) && $dashboardData['balance'] >= 0) ? '↑' : '↓' }} 
                Balance Fund
            </div>
            <div class="icon">
                <img class="header_cards_img" src="{{ asset('unit-portal/images/balance.svg') }}" alt="Balance">
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card">
            <div class="title">Expenses Percentage</div>
            <div class="value">
                @if(isset($dashboardData) && isset($dashboardData['expense_percentage']) && is_numeric($dashboardData['expense_percentage']))
                    {{ number_format($dashboardData['expense_percentage'], 2) }}%
                @else
                    0.00%
                @endif
            </div>
            <div class="change {{ (isset($dashboardData['expense_percentage']) && $dashboardData['expense_percentage'] <= 100) ? 'positive' : 'negative' }}">
                {{ (isset($dashboardData['expense_percentage']) && $dashboardData['expense_percentage'] <= 100) ? '↓' : '↑' }} 
                Utilisation Rate
            </div>
            <div class="icon">
                <img class="header_cards_img" src="{{ asset('unit-portal/images/expense.svg') }}" alt="Expenses Percentage">
            </div>
        </div>
    </div>
</div>