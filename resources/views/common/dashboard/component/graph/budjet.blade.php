@section('budjet')

        <div class="budgetViewBg">
            <h6 class="budgetViewBgTitle">Budget</h6>
            <div id="budjet_chart"></div>
            <div class="budgetTable">
                <table cellpadding="0" cellspacing="0" border="0" class="table table-striped table-bordered" id="revenue_chart">
                    <thead>
                        <tr>
                            <th>Units</th>
                            <th style="text-align: end;">Amount</th>
                            <th style="text-align: end;">Percentage</th>

                        </tr>
                    </thead>
                    <tfoot>
                        <tr>
                            <th>TOTAL</th>
                            <th class="greenFontBold"></th>
                            <th class="greenFontBold" style="text-align: end;">{{ isset($dashboardData['budget']['total']) && is_numeric($dashboardData['budget']['total']) ? number_format($dashboardData['budget']['total'], 2) : '0.00' }}</th>
                        </tr>
                    </tfoot>
                    <tbody>
                        @if(isset($dashboardData['budget']['by_unit']) && is_array($dashboardData['budget']['by_unit']) && count($dashboardData['budget']['by_unit']) > 0)
                            @foreach($dashboardData['budget']['by_unit'] as $unitBudget)
                                @php
                                    $unitBudgetAmount = isset($unitBudget['budget']) && is_numeric($unitBudget['budget']) ? (float)$unitBudget['budget'] : 0;
                                    $totalBudget = isset($dashboardData['budget']['total']) && is_numeric($dashboardData['budget']['total']) ? (float)$dashboardData['budget']['total'] : 0;
                                    $percentage = ($totalBudget > 0 && $unitBudgetAmount > 0) ? ($unitBudgetAmount / $totalBudget) * 100 : 0;
                                @endphp
                                <tr class="odd gradeX">
                                    <td>{{ isset($unitBudget['name']) ? $unitBudget['name'] : 'Unknown Unit' }}</td>
                                    <td class="greenFont" style="text-align: end;">{{ is_numeric($unitBudgetAmount) ? number_format($unitBudgetAmount, 2) : '0.00' }}</td>
                                    <td class="center greenFont" style="text-align: end;">{{ is_numeric($percentage) ? number_format($percentage, 2) : '0.00' }} %</td>
                                </tr>
                            @endforeach
                        @else
                            <tr class="odd gradeX">
                                <td colspan="3" style="text-align: center;">No budget data available</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
            </div>
        </div>

<!-- Echart donut chart Section -->
<script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
<script>
    var dom = document.getElementById('budjet_chart');
    var myChart = echarts.init(dom, null, {
        renderer: 'canvas',
        useDirtyRect: false
    });
    var app = {};
    var option;
    option = {
        tooltip: {
            trigger: 'item'
        },
        //   legend: {
        //     top: '5%',
        //     left: 'center'
        //   },
        series: [{
            name: 'Access From',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: 40,
                    fontWeight: 'bold'
                }
            },
            labelLine: {
                show: false
            },
            data: [{
                    value: 17534.00,
                    name: 'Signature House'
                },
                {
                    value: 13466.00,
                    name: 'Orchards'
                },
                {
                    value: 19454.00,
                    name: 'Exmouth House'
                },
                {
                    value: 8966.00,
                    name: 'Ernstell House Care Home'
                }
            ]
        }]
    };
    if (option && typeof option === 'object') {
        myChart.setOption(option);
    }
    window.addEventListener('resize', myChart.resize);
</script>

@endsection