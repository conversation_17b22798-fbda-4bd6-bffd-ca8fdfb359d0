@extends('common.layout')
@section('title', 'Dashboard | Client Portal')
<link rel="stylesheet" href="{{asset('unit-portal/css/style.css')}}">
<link rel="stylesheet" href="{{asset('css/dashboard-filters.css')}}">
@section('content')
<!-- Content Wrapper. Contains page content -->
<div class="Container_Fluid_Main">

    <div class="row headTitleBtm">
        <div class="col-sm-12">
            <div class="GpBoxHeading no-bdr pb0">
                <h4>
                    Dashboard
                </h4>
            </div>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filter Options</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="unit-filter" class="form-label">Select Unit</label>
                                <select id="unit-filter" name="unit_id" class="form-control">
                                    <option value="">Select Unit</option>
                                    @foreach($units as $unit)
                                        <option value="{{ $unit->clientUnitId }}" 
                                                {{ $selectedUnitId == $unit->clientUnitId ? 'selected' : '' }}>
                                            {{ $unit->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="financial-year-filter" class="form-label">Select Financial Year</label>
                                <select id="financial-year-filter" name="financial_year" class="form-control" 
                                        {{ !$selectedUnitId ? 'disabled' : '' }}>
                                    <option value="">Select Financial Year</option>
                                    @if($selectedUnitId && count($financialYears) > 0)
                                        @foreach($financialYears as $year)
                                            <option value="{{ $year }}" 
                                                    {{ $selectedFinancialYear == $year ? 'selected' : '' }}>
                                                @php
                                                    $yearValue = is_numeric($year) ? (int)$year : date('Y');
                                                    $nextYear = ($yearValue + 1) % 100;
                                                @endphp
                                                {{ $yearValue }}-{{ str_pad($nextYear, 2, '0', STR_PAD_LEFT) }}
                                            </option>
                                        @endforeach
                                    @endif
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cards Section -->
    <div>
        @include('common.dashboard.component.card.header_cards')
    </div>

     <!-- Graph Section -->
    <div class="row">
        <div class="col-md-6">
            @yield('budjet')
        </div>
        <div class="col-md-6">
            @yield('expense')
        </div>
    </div>
    
     <div>
        @yield('expense_overview')
    </div>

    <!-- Reason for Booking -->
    <div>
        @yield('booking_cards')
    </div>

     <div>
        @yield('budjet_expense')
    </div>







    @hasSection('graph1')
    <div class="row mt-3">
        <div class="col-12">
            @yield('graph1')
        </div>
    </div>
    @endif

</div>
</section>
</div>

<!-- Custom Styles for Dashboard -->
@yield('dashboard-styles')

<!-- Dashboard Filters JavaScript -->
<script src="{{ asset('js/dashboard-filters.js') }}"></script>

<!-- Custom Scripts for Dashboard -->
@yield('dashboard-scripts')
@endsection