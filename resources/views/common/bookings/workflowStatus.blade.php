<div class="modal fade" id="ProgressModal{{ $key }}" tabindex="-1" role="dialog" aria-labelledby="ProgressModal"
    ari                                        // Also check if user's role is for the correct unit or client
                                        if ($isCurrentUserApprover) {
                                            $isCurrentUserApprover = (
                                                ($userRole->type === 'unit' && ($userRole->type_id == $booking->unitId || $userRole->type_id == 0)) ||
                                                ($userRole->type === 'client' && $booking->unit && $booking->unit->client && ($userRole->type_id == $booking->unit->client->clientId || $userRole->type_id == 0))
                                            );
                                        }den="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" style="position: relative; z-index: 1000;" data-dismiss="modal"
                    aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="myModalLabel">Approve The Shift</h4>
            </div>
            <div class="modal-body">
                <div class="flexRow">
                    <div class="UntItems">
                        <div class="row Wkpad">
                            <div class="col-md-3">
                                <img src="https://www.nursesgroupadmin.co.uk/storage/app/client/1646842964_7yetR4Yh40GloL1yoZi2CkLT16mvz17TwQoVKbYV.jpg"
                                    alt="UntBg" class="UntBg">
                            </div>
                            <div class="col-md-9">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="UntNmBgTit"> Unit Name : {{ $booking->unit->name }}</div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="UntNmBg"> <span>Booking ID :</span> {{ $booking->bookingId }}</div>
                                        <div class="UntNmBg"> <span>Booking Date :</span>
                                            {{ \Carbon\Carbon::parse($booking->created_at)->format('D, F j') }}
                                        </div>
                                        <div class="UntNmBg"> <span>Booked Shift :</span> {{ $booking->shift->name }}
                                        </div>
                                        <div class="UntNmBg"> <span> Category :</span> {{ $booking->category->name }}
                                        </div>
                                        <div class="UntNmBg"> <span> Reason :</span> {{ $booking->reason_text }}</div>
                                    </div>
                                    <div class="col-md-6">

                                        <div class="UntNmBg"> <span> Booked By :</span> {{ $booking->bookedby->name }}
                                        </div>
                                        <div class=" UntNmBg"> <span> Time to start :</span>
                                            {{  $booking->totalhoursto }}
                                        </div>
                                        <div class="UntNmBg"> <span>Cost :</span> {{ $booking->costPerShiftNumberonly }}
                                        </div>
                                        <div class="UntNmBg"> <span>Time :</span>
                                            {{ $booking->startTime }}-{{ $booking->endTime }}
                                        </div>
                                        <div class="UntNmBg"> &nbsp;
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>

                        <div class="ScrollRow">
                            <div class="pt45 pb20">

                                @php
                                $total_workflow = $booking->bookingWorkflows->count();
                                $showTOClient = true;
                                if($total_workflow > 1) {
                                if(auth()->user()->portal_type == \App\Models\Client::class) {
                                $clientCompleted =
                                \APP\Models\BookingWorkflow::where('booking_id',$booking->bookingId)
                                ->where('approver_type','!=',\App\Models\Client::class)->where('approval_status','pending')->first();
                                if($clientCompleted) {$showTOClient = false;}
                                }
                                }
                                $bookingWorkflows = $booking->bookingWorkflows->sortByDesc('approver_type');
                                @endphp
                                @foreach($bookingWorkflows as $bookingWorkflow)
                                @php
                                $approvers = [];
                                $workflow = $bookingWorkflow->workflow;
                                if($bookingWorkflow->workflow){
                                $approvers =
                                collect($bookingWorkflow->workflow->get('approvers'))->sortBy('approval_order');
                                } @endphp
                                @foreach ($approvers as $index => $approver)
                                @php
                                $approver = collect($approver);

                                // Skip deleted roles
                                if (isset($approver['role']['deleted_at']) && !is_null($approver['role']['deleted_at'])) {
                                    continue;
                                }

                                $statusRecord =collect($booking->bookingWorkflowStatus)->firstWhere(
                                'workflow_approvers_id',
                                $approver['id'],
                                );
                                $alreadyApproved = !is_null($statusRecord);
                                $approvalStatus = $statusRecord->approval_status ?? 'Pending';
                                $statusDate =
                                $statusRecord && $statusRecord->updated_at
                                ? \Carbon\Carbon::parse($statusRecord->updated_at)->format('D, F j')
                                : null;

                                $canApprove = false;
                                $isCurrentUserApprover = false;

                                // Check if current user can approve based on role or additional conditions
                                if (isset($approver['additional_conditions'])) {
                                    // For additional conditions, check if user has the required budget approval permissions
                                    $userRole = auth()->user()->role;
                                    if ($userRole) {
                                        switch($approver['additional_conditions']) {
                                            case 'able_to_approve_within_budget':
                                                $isCurrentUserApprover = $userRole->able_to_approve_within_budget == 1;
                                                break;
                                            case 'able_to_approve_weekly_budget':
                                                $isCurrentUserApprover = $userRole->able_to_approve_weekly_budget == 1;
                                                break;
                                            case 'able_to_approve_special_allowance_budget':
                                                $isCurrentUserApprover = $userRole->able_to_approve_special_allowance_budget == 1;
                                                break;
                                        }

                                        // Also check if user's role is for the correct unit or client
                                        if ($isCurrentUserApprover) {
                                            $isCurrentUserApprover = (
                                                ($userRole->type === 'unit' && ($userRole->type_id == $booking->unitId || $userRole->type_id == 0)) ||
                                                ($userRole->type === 'client' && $booking->unit && $booking->unit->client && ($userRole->type_id == $booking->unit->client->clientId || $userRole->type_id == 0))
                                            );
                                        }
                                    }
                                } else {
                                    // For standard roles, check both exact role match and budget approval permissions
                                    $userRole = auth()->user()->role;

                                    // First check exact role match
                                    $isCurrentUserApprover = $userRole->role_id == $approver['role_id'];

                                    // If not exact match, check if this approver role requires budget approval permissions
                                    // and if current user has those permissions
                                    if (!$isCurrentUserApprover && $userRole) {
                                        $approverRole = \App\Models\Rights\Role::where('role_id', $approver['role_id'])->first();

                                        if ($approverRole) {
                                            // Check if current user has the same budget approval permissions as the approver role
                                            if ($approverRole->able_to_approve_within_budget && $userRole->able_to_approve_within_budget) {
                                                $isCurrentUserApprover = (
                                                    ($userRole->type === 'unit' && ($userRole->type_id == $booking->unitId || $userRole->type_id == 0)) ||
                                                    ($userRole->type === 'client' && $booking->unit && $booking->unit->client && ($userRole->type_id == $booking->unit->client->clientId || $userRole->type_id == 0))
                                                );
                                            } elseif ($approverRole->able_to_approve_weekly_budget && $userRole->able_to_approve_weekly_budget) {
                                                $isCurrentUserApprover = (
                                                    ($userRole->type === 'unit' && ($userRole->type_id == $booking->unitId || $userRole->type_id == 0)) ||
                                                    ($userRole->type === 'client' && $booking->unit && $booking->unit->client && ($userRole->type_id == $booking->unit->client->clientId || $userRole->type_id == 0))
                                                );
                                            } elseif ($approverRole->able_to_approve_special_allowance_budget && $userRole->able_to_approve_special_allowance_budget) {
                                                $isCurrentUserApprover = (
                                                    ($userRole->type === 'unit' && ($userRole->type_id == $booking->unitId || $userRole->type_id == 0)) ||
                                                    ($userRole->type === 'client' && $booking->unit && $booking->unit->client && ($userRole->type_id == $booking->unit->client->clientId || $userRole->type_id == 0))
                                                );
                                            }
                                        }
                                    }
                                }

                                if ($isCurrentUserApprover && !$alreadyApproved) {
                                if ($workflow['type'] === 'sequential') {
                                if ($index === 0) {
                                $canApprove = true;
                                } else {
                                $previousApprover = $approvers[$index - 1];
                                $previousStatus = collect(
                                $booking->bookingWorkflowStatus,
                                )->firstWhere('workflow_approvers_id', $previousApprover['id']);

                                if (
                                $previousStatus &&
                                $previousStatus->approval_status === 'approved'
                                ) {
                                $canApprove = true;
                                }
                                }
                                } else {
                                $canApprove = true;
                                }
                                }

                                // Get role name - handle both original structure and dynamically added roles
                                $roleName = 'Unknown Role';
                                $unitId = $booking->unitId;
                                $clientId = $booking->unit && $booking->unit->client ? $booking->unit->client->clientId : null;

                                if (isset($approver['additional_conditions'])) {
                                    // Handle dynamically added budget approval roles - show all roles with required permissions
                                    $roleNames = [];
                                    $conditionLabel = '';

                                    switch($approver['additional_conditions']) {
                                        case 'able_to_approve_within_budget':
                                            $conditionLabel = 'Budget Approval Required: ';
                                            $budgetRoles = \App\Models\Rights\Role::where('able_to_approve_within_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $roleNames = $budgetRoles->pluck('name')->toArray();
                                            break;
                                        case 'able_to_approve_weekly_budget':
                                            $conditionLabel = 'Weekly Budget Approval Required: ';
                                            $weeklyBudgetRoles = \App\Models\Rights\Role::where('able_to_approve_weekly_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $roleNames = $weeklyBudgetRoles->pluck('name')->toArray();
                                            break;
                                        case 'able_to_approve_special_allowance_budget':
                                            $conditionLabel = 'Special Allowance Approval Required: ';
                                            $specialAllowanceRoles = \App\Models\Rights\Role::where('able_to_approve_special_allowance_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $roleNames = $specialAllowanceRoles->pluck('name')->toArray();
                                            break;
                                        default:
                                            $conditionLabel = 'Budget Approval Required: ';
                                            $roleNames = ['Budget Approver'];
                                    }

                                    $roleName = $conditionLabel . (!empty($roleNames) ? implode(', ', $roleNames) : 'Budget Approver');
                                } elseif (isset($approver['role']['name'])) {
                                    $roleName = $approver['role']['name'];

                                    // Check if this role has budget approval permissions and show all roles with same permissions
                                    $currentRole = \App\Models\Rights\Role::where('role_id', $approver['role_id'])->first();

                                    if ($currentRole) {
                                        $additionalRoleNames = [];
                                        $conditionLabel = '';

                                        // Check if this role has any budget approval permissions
                                        if ($currentRole->able_to_approve_within_budget) {
                                            $conditionLabel = 'Budget Approval Required: ';
                                            $budgetRoles = \App\Models\Rights\Role::where('able_to_approve_within_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $additionalRoleNames = $budgetRoles->pluck('name')->toArray();
                                        } elseif ($currentRole->able_to_approve_weekly_budget) {
                                            $conditionLabel = 'Weekly Budget Approval Required: ';
                                            $weeklyBudgetRoles = \App\Models\Rights\Role::where('able_to_approve_weekly_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $additionalRoleNames = $weeklyBudgetRoles->pluck('name')->toArray();
                                        } elseif ($currentRole->able_to_approve_special_allowance_budget) {
                                            $conditionLabel = 'Special Allowance Approval Required: ';
                                            $specialAllowanceRoles = \App\Models\Rights\Role::where('able_to_approve_special_allowance_budget', true)
                                                ->where(function($query) use ($unitId, $clientId) {
                                                    $query->where(function($subQuery) use ($unitId) {
                                                        $subQuery->where('type', 'unit')
                                                                 ->where('type_id', $unitId)
                                                                 ->orWhere('type_id', 0); // Global unit roles
                                                    });
                                                    if ($clientId) {
                                                        $query->orWhere(function($subQuery) use ($clientId) {
                                                            $subQuery->where('type', 'client')
                                                                     ->where('type_id', $clientId)
                                                                     ->orWhere('type_id', 0); // Global client roles
                                                        });
                                                    }
                                                })
                                                ->whereNull('deleted_at')
                                                ->get();
                                            $additionalRoleNames = $specialAllowanceRoles->pluck('name')->toArray();
                                        }

                                        // If we found additional roles with same permissions, show them all
                                        if (!empty($additionalRoleNames) && count($additionalRoleNames) > 1) {
                                            $roleName = $conditionLabel . implode(', ', $additionalRoleNames);
                                        }
                                    }
                                }
                                @endphp

                                <div
                                    class="order-tracking {{ $approvalStatus=='approved' ? 'completed' : ($approvalStatus=='declined' ? 'declined' : '') }}">
                                    <span class="is-complete">
                                        @if ($approvalStatus == 'approved')
                                        <i class="fa fa-check" aria-hidden="true"></i>
                                        @elseif ($approvalStatus == 'declined')
                                        <i class="fa fa-times" aria-hidden="true"></i>
                                        @else
                                        <i class="fa fa-pause" aria-hidden="true"></i>
                                        @endif
                                    </span>

                                    <div class="ConfStaff">{{ $roleName }}
                                    </div>

                                    <div
                                        class="{{ $approvalStatus=='approved' ? 'CompleteStatus' : ($approvalStatus=='declined' ? 'CancelStatus' : 'PendingStatus') }} ">
                                        {{ ucfirst($approvalStatus) }}

                                        <span>
                                            @if ($statusRecord && $statusRecord->notes)
                                            <div class="Wktooltip"><i class="fa fa-comments-o" aria-hidden="true"></i>
                                                <span class="tooltiptext">{{$statusRecord->notes}}</span>
                                            </div>
                                            @endif
                                        </span>
                                    </div>
                                    <div class="DateStat">{{ $statusDate }}</div>

                                    @if ($canApprove && $showTOClient)
                                    <div>
                                        <button type="button" class="btwkFlow btn btn-primary workflow-approve-btn"
                                            data-id="{{ $booking->bookingId }}" data-value=1>Approve</button>
                                        <button type="button" class="btwkFlow btn btn-three workflow-approve-btn"
                                            data-id="{{ $booking->bookingId }}" data-value=0>Decline</button>
                                    </div>
                                    @endif



                                </div>
                                @endforeach
                                @endforeach

                                <!-- <div class="order-tracking completed">
                                <span class="is-complete"><i class="fa fa-check" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Staff</div>
                                <div class="CompleteStatus">Booking Placed</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                            </div>
                            <div class="order-tracking completed">
                                <span class="is-complete"><i class="fa fa-check" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Manager</div>
                                <div class="CompleteStatus">Approved Booking</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                            </div>
                            <div class="order-tracking declined">
                                <span class="is-complete"><i class="fa fa-times" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Admin</div>
                                <div class="CancelStatus">Cancelled Booking</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                                <div>
                                    <button type="button" class="btwkFlow btn btn-success workflow-approve-btn"><i
                                            class="fa fa-check" aria-hidden="true"></i></button>
                                    <button type="button" class="btwkFlow btn btn-danger workflow-approve-btn"><i
                                            class="fa fa-times" aria-hidden="true"></i></button>
                                </div>
                            </div>
                            <div class="order-tracking declined">
                                <span class="is-complete"><i class="fa fa-times" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Admin</div>
                                <div class="CancelStatus">Cancelled Booking</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                            </div>
                            <div class="order-tracking ">
                                <span class="is-complete"><i class="fa fa-pause" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Client Admin</div>
                                <div class="PendingStatus">Awaiting Approval</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                            </div>
                            <div class="order-tracking">
                                <span class="is-complete"><i class="fa fa-pause" aria-hidden="true"></i></span>
                                <div class="ConfStaff">Client Admin</div>
                                <div class="PendingStatus">Awaiting Approval</div>
                                <div class="DateStat">Mon, Jan 2025</div>
                            </div> -->

                            </div>
                        </div>
                    </div>

                </div>
                <!-- <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
            </div> -->
            </div>
        </div>
    </div>
</div>
