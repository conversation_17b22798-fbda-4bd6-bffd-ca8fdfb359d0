{{-- Dashboard Main View --}}
{{-- This file extends the dashboard_wrapper and includes components to show yielding in action --}}

@extends('common.dashboard.dashboard_wrapper')

{{-- Include components that will populate the @yield sections --}}
@include('common.dashboard.component.card.booking_cards')
@include('common.dashboard.component.graph.budjet')
@include('common.dashboard.component.graph.expense')
@include('common.dashboard.component.graph.expense_overview')
@include('common.dashboard.component.graph.budjet_expense')


@include('common.dashboard.component.filter.client_filter')
@include('common.dashboard.component.filter.unit_filter')

{{-- You can also add content directly using @section --}}
@section('data-tables')
    <!-- <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Sample Data Table</h3>
            </div>
            <div class="card-body">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>1</td>
                            <td><PERSON></td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2024-01-15</td>
                        </tr>
                        <tr>
                            <td>2</td>
                            <td>Jane Smith</td>
                            <td><span class="badge badge-warning">Pending</span></td>
                            <td>2024-01-14</td>
                        </tr>
                        <tr>
                            <td>3</td>
                            <td>Bob Johnson</td>
                            <td><span class="badge badge-success">Active</span></td>
                            <td>2024-01-13</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div> -->
@endsection
