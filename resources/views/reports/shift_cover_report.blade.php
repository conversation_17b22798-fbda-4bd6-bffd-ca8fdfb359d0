@extends('common.layout')

@section('title', 'Shift Cover Report')

@section('styles')
<link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.5.0/css/responsive.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/buttons/2.4.2/css/buttons.bootstrap4.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tempusdominus-bootstrap-4/5.39.0/css/tempusdominus-bootstrap-4.min.css">
<style>
    .report-filters {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
    }

    .filter-row {
        margin-bottom: 15px;
    }

    .filter-row:last-child {
        margin-bottom: 0;
    }

    .table-responsive {
        border: 1px solid #dee2e6;
        border-radius: 8px;
    }

    .dataTables_wrapper .dataTables_length,
    .dataTables_wrapper .dataTables_filter,
    .dataTables_wrapper .dataTables_info,
    .dataTables_wrapper .dataTables_paginate {
        margin-bottom: 10px;
    }

    .summary-cards {
        margin-top: 20px;
    }

    .summary-card {
        background: #fff;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .summary-card h5 {
        color: #495057;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: 600;
        text-transform: uppercase;
    }

    .summary-card .amount {
        font-size: 24px;
        font-weight: bold;
        color: #28a745;
    }

    .btn-export {
        margin-left: 10px;
    }

    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: none;
        justify-content: center;
        align-items: center;
        z-index: 1000;
    }

    .spinner-border {
        width: 3rem;
        height: 3rem;
    }

    @media (max-width: 768px) {
        .report-filters {
            padding: 15px;
        }

        .filter-row .col-md-3,
        .filter-row .col-md-4 {
            margin-bottom: 10px;
        }

        .btn-group {
            width: 100%;
        }

        .btn-group .btn {
            flex: 1;
        }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Shift Cover Report</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        {{-- <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Dashboard</a></li> --}}
                        <li class="breadcrumb-item">Reports</li>
                        <li class="breadcrumb-item active">Shift Cover Report</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters Section -->
    <div class="row">
        <div class="col-12">
            <div class="report-filters">
                <h5 class="mb-3">
                    <i class="fas fa-filter me-2"></i>Filters
                </h5>

                <form id="filter-form">
                    <div class="filter-row row">
                        <div class="col-md-3">
                            <label for="unit_id" class="form-label">Unit</label>
                            <select class="form-select" id="unit_id" name="unit_id">
                                <option value="">All Units</option>
                                @foreach($units as $unit)
                                    <option value="{{ $unit->clientUnitId }}">
                                        {{ $unit->name }} {{ $unit->alias ? '(' . $unit->alias . ')' : '' }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <div class="col-md-3">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date">
                        </div>

                        <div class="col-md-3">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date">
                        </div>

                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid gap-2 d-md-flex">
                                <button type="button" class="btn btn-primary" id="apply-filters">
                                    <i class="fas fa-search me-1"></i>Apply Filters
                                </button>
                                <button type="button" class="btn btn-secondary" id="reset-filters">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Data Table Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>Shift Cover Data
                    </h5>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-success btn-sm" id="export-excel">
                            <i class="fas fa-file-excel me-1"></i>Excel
                        </button>
                        <button type="button" class="btn btn-danger btn-sm" id="export-pdf">
                            <i class="fas fa-file-pdf me-1"></i>PDF
                        </button>
                    </div>
                </div>

                <div class="card-body position-relative">
                    <!-- Loading Overlay -->
                    <div class="loading-overlay" id="loading-overlay">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table id="shift-cover-table" class="table table-striped table-bordered nowrap" style="width:100%">
                            <thead class="table-dark">
                                <tr>
                                    <th>Booking ID</th>
                                    <th>Account Code</th>
                                    <th>Date</th>
                                    <th>Shift</th>
                                    <th>Category</th>
                                    <th>Staff</th>
                                    <th>Start Time</th>
                                    <th>End Time</th>
                                    <th>Hours</th>
                                    <th>Shift Booked On</th>
                                    <th>Reason for Booking</th>
                                    <th>Initial Amount</th>
                                    <th>Final Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Data will be loaded via AJAX -->
                            </tbody>
                            <tfoot>
                                <tr class="table-info">
                                    <th colspan="11" class="text-end">Totals:</th>
                                    <th id="initial-total">£0.00</th>
                                    <th id="final-total">£0.00</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row summary-cards">
        <div class="col-md-6">
            <div class="summary-card">
                <h5>Total Initial Amount</h5>
                <div class="amount" id="summary-initial">£0.00</div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="summary-card">
                <h5>Total Final Amount</h5>
                <div class="amount" id="summary-final">£0.00</div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<!-- DataTables JS -->
<script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.2/js/buttons.html5.min.js"></script>

<!-- Custom JavaScript -->
<script src="{{ asset('js/reports/shift-cover-report.js') }}?v={{ time() }}"></script>
@endsection
