<!-- Role Add/Edit Modal -->
<div class="modal fade" id="roleModal" tabindex="-1" role="dialog" aria-labelledby="roleModalLabel"
    data-backdrop="static" data-keyboard="false">
    <!-- Updated -->
    <div class="modal-dialog modal-lg" role="document">
        <!-- Large modal -->
        <form id="roleForm" method="POST" action="{{route('users.roles.store')}}">
            <!-- Updated -->
            @csrf
            <div class="modal-content">
                <div class="modal-header">
                    <h4 class="modal-title" id="roleModalLabel">Add/Edit Role</h4>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <!-- Close button -->
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="roleName">Role Name</label>
                        <input type="text" name="name" id="roleName" class="form-control" required>
                    </div>

                    <!-- Budget Approval Configuration -->
                    <div class="form-group budget-approval-section">
                        <label class="PerHead">
                            <i class="fas fa-dollar-sign"></i> Budget Approval Configuration
                        </label>
                        <div class="budget-hierarchy-note">
                            <small class="text-muted">
                                <i class="fas fa-info-circle"></i>
                                Higher level approvals automatically include lower level permissions
                            </small>
                        </div>
                        <div class="budget-approval-container">
                            <div class="budget-approval-item" data-level="1">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="able_to_approve_within_budget" id="ableToApproveWithinBudget"
                                           class="custom-control-input budget-checkbox" value="1">
                                    <label class="custom-control-label" for="ableToApproveWithinBudget">
                                        <span class="checkbox-title">
                                            <span class="level-indicator">Level 1</span>
                                            Within Budget Approval
                                        </span>
                                        <span class="checkbox-description">Can approve requests that are within the allocated budget</span>
                                    </label>
                                </div>
                            </div>

                            <div class="budget-approval-item" data-level="2">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="able_to_approve_weekly_budget" id="ableToApproveWeeklyBudget"
                                           class="custom-control-input budget-checkbox" value="1">
                                    <label class="custom-control-label" for="ableToApproveWeeklyBudget">
                                        <span class="checkbox-title">
                                            <span class="level-indicator">Level 2</span>
                                            Weekly Budget Approval
                                        </span>
                                        <span class="checkbox-description">Can approve weekly budget allocations and adjustments (includes Level 1)</span>
                                    </label>
                                </div>
                            </div>

                            <div class="budget-approval-item" data-level="3">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" name="able_to_approve_special_allowance_budget" id="ableToApproveSpecialAllowanceBudget"
                                           class="custom-control-input budget-checkbox" value="1">
                                    <label class="custom-control-label" for="ableToApproveSpecialAllowanceBudget">
                                        <span class="checkbox-title">
                                            <span class="level-indicator">Level 3</span>
                                            Special Allowance Budget Approval
                                        </span>
                                        <span class="checkbox-description">Can approve special allowance and overtime budget requests (includes Level 1 & 2)</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($isClient)
                    <div class="form-group">
                        <label for="userType">Type</label>
                        <select name="type" id="userType" class="form-control @error('type') is-invalid @enderror"
                            required>
                            <option value="client" {{ old('type') == 'client' ? 'selected' : '' }}>Client</option>
                            <option value="unit" {{ old('type') == 'unit' ? 'selected' : '' }}>Unit</option>
                        </select>
                        @error('type')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="form-group" style="display:none;" id="unitClientDiv">
                        <label for="userUnitClient">Units</label>
                        <select name="clientUnit" id="userUnitClient"
                            class="form-control @error('unit_client') is-invalid @enderror">
                            <option value="" selected>Select Unit</option>
                            @foreach($units as $unit)
                            <option value="{{ $unit->clientUnitId }}"
                                {{ old('clientUnit') == $unit->clientUnitId ? 'selected' : '' }}>{{ $unit->name }}
                            </option>
                            @endforeach
                        </select>
                        @error('clientUnit')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    @endif


                    <div class="form-group">
                        <label>Permissions</label>
                        <div id="permissionsTree">
                            @foreach($modules as $module)
                            <div class="module">
                                <label>
                                    <input type="checkbox" class="module-checkbox"
                                        data-module-id="{{ $module->module_id }}">
                                    <strong>{{ $module->name }}</strong>
                                </label>
                                <div class="submodules" style="padding-left: 20px;">
                                    @foreach($module->submodules as $submodule)
                                    <div class="submodule">
                                        <label>
                                            <input type="checkbox" class="submodule-checkbox"
                                                data-submodule-id="{{ $submodule->sub_module_id }}"
                                                data-module-id="{{ $module->module_id }}">
                                            {{ $submodule->name }}
                                        </label>
                                        <div class="permissions" style="padding-left: 20px;">
                                            @foreach($submodule->permissions as $permission)
                                            <div>
                                                <label>
                                                    <input type="checkbox" name="permissions[]"
                                                        class="permission-checkbox"
                                                        value="{{ $permission->permission_id }}"
                                                        data-submodule-id="{{ $submodule->sub_module_id }}">
                                                    {{ $permission->name }}
                                                </label>
                                            </div>
                                            @endforeach
                                        </div>
                                    </div>
                                    @endforeach
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    <!-- Close button -->
                    <button type="submit" class="btn btn-primary">Save Role</button>
                </div>
            </div>
        </form>
    </div>
</div>
