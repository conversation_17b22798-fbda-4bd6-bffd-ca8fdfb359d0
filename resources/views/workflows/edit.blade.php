@extends('common.layout')

@section('content')

<div class="wkflow">

    <div class="Container_Fluid_Main">
        <div class="content-header">
            <div class="row headTitleBtm">
                <div class="col-md-12">
                    <div class="GpBoxHeading no-bdr pb0">
                        <h4>
                            Edit Workflow
                        </h4>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <div class="Container_Fluid_Main">
        <form action="{{ route('workflows.update', $workflow->id) }}" method="POST">
            @csrf
            @method('PUT') <!-- This ensures the form uses the PUT method for updates -->


            <div class="FlowBg MainBoxRow">
                <div class="row">
                    <div class="col-md-5">
                        <label>Name*</label>
                        <input type="text" name="name" class="form-control" value="{{ old('name', $workflow->name) }}" required>
                    </div>
                    <div class="col-md-4">
                        <label>Type*</label>
                        <select name="type" class="form-control" required>
                            <option value="sequential" {{ $workflow->type == 'sequential' ? 'selected' : '' }}>Sequential</option>
                            <option value="parallel" {{ $workflow->type == 'parallel' ? 'selected' : '' }}>Parallel</option>
                        </select>
                    </div>
                    <div class="col-md-3 ManFiled">
                        <label>Is Client Approval Required</label>
                        <input type="hidden" name="client_approval" value="0">
                        <input type="checkbox" name="client_approval" value="1" {{ $workflow->requires_client_approval ? 'checked' : '' }}>
                    </div>
                </div>
            </div>



            <h4>Approval Steps</h4>
            <div id="approval-steps-container">
                @foreach($workflow->approvers as $index => $step)
                <div class="approval-step">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="WfBg">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label>Order:</label>
                                        <input style="border: 1px solid #e5e5e5 !important; background:#fff" type="number" name="approval_steps[{{ $index }}][order]" class="form-control" value="{{ old('approval_steps.' . $index . '.order', $step->approval_order) }}" readonly required>
                                    </div>
                                    <div class="col-md-6">
                                        <label>Approver Role:</label>
                                        <select style="border: 1px solid #e5e5e5 !important;" name="approval_steps[{{ $index }}][role]" class="form-control" required>
                                            <option value="">Select Role</option>
                                            @foreach($roles as $role)
                                            <option value="{{ $role->role_id }}" {{ $role->role_id == $step->role_id ? 'selected' : '' }}>{{ $role->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-2 ManFiled">
                                        <label>Is Mandatory:</label>
                                        <input type="hidden" name="approval_steps[{{ $index }}][mandatory]" value="0">
                                        <input type="checkbox" value="1" name="approval_steps[{{ $index }}][mandatory]" class="form-check-input" {{ old('approval_steps.' . $index . '.mandatory', $step->is_mandatory) ? 'checked' : '' }}>
                                        {{-- <input type="checkbox" name="approval_steps[${stepIndex}][mandatory]" class="form-check-input" value="1"> --}}
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="wk_right_btn remove-step"><i class="fa fa-times" aria-hidden="true"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endforeach
            </div>

            <div class="SaveRow">
            <div class="row">
                <div class="col-md-12 text-right">
                    <div class="createBtn "> 
                        <button type="submit" class="btn-primary">
                        <i class="fa fa-check" aria-hidden="true"></i> Save</button>
                    </div>
                    <div class="AddBtnNew">
                        <button type="button" class="btn btn-three" id="add-step">
                            <i class="fa fa-plus-circle" aria-hidden="true"></i> Add Step
                        </button>
                    </div>
                </div>
            </div>
        </div>

        </form>
    </div>
</div>

@endsection

@section('scripts')
<script src="{{ asset('js/jquery.min.js') }}"></script>
<script>
    // Pass PHP variables to JavaScript
    window.workflowData = {
        approversCount: {{ count($workflow->approvers) }},
        roles: [
            @foreach($roles as $role)
                {
                    id: '{{ $role->role_id }}',
                    name: '{{ $role->name }}'
                },
            @endforeach
        ]
    };
</script>
<script>
    $(document).ready(function() {

        // Function to reindex all steps
        function reindexApprovalSteps() {
            $('#approval-steps-container .approval-step').each(function(index) {
                // Update names of all inputs and selects
                $(this).find('input, select').each(function() {
                    const name = $(this).attr('name');
                    if (name) {
                        const newName = name.replace(/approval_steps\[\d+]/, `approval_steps[${index}]`);
                        $(this).attr('name', newName);
                    }
                });

                // Update order number (1-based)
                $(this).find('input[name$="[order]"]').val(index + 1);
            });
        }

        // Function to disable duplicate role selections
        function updateRoleOptions() {
            const selectedRoles = [];

            $('select[name^="approval_steps"]').each(function() {
                const val = $(this).val();
                if (val) {
                    selectedRoles.push(val);
                }
            });

            $('select[name^="approval_steps"]').each(function() {
                const currentVal = $(this).val();
                $(this).find('option').each(function() {
                    const optionVal = $(this).attr('value');
                    if (optionVal === "") return; // skip placeholder
                    if (optionVal !== currentVal && selectedRoles.includes(optionVal)) {
                        $(this).attr('disabled', true);
                    } else {
                        $(this).attr('disabled', false);
                    }
                });
            });
        }

        // Initialize step index from existing approvers
        let stepIndex = window.workflowData.approversCount;

        // Add new step
        $('#add-step').click(function() {
            // Prevent adding if any existing step has no role selected
            let allRolesSelected = true;
            $('select[name^="approval_steps"]').each(function() {
                if (!$(this).val()) {
                    allRolesSelected = false;
                }
            });

            if (!allRolesSelected) {
                alert('Please select a role for all existing steps before adding a new one.');
                return;
            }

            // Build roles options from the data object
            let rolesOptions = '';
            window.workflowData.roles.forEach(function(role) {
                rolesOptions += `<option value="${role.id}">${role.name}</option>`;
            });

            const stepHtml = `
                <div class="approval-step">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="WfBg">
                                <div class="row">
                                    <div class="col-md-3">
                                        <label>Order:</label>
                                        <input style="border: 1px solid #e5e5e5 !important; background:#fff" type="number" name="approval_steps[${stepIndex}][order]" class="form-control" value="${stepIndex + 1}" readonly required>
                                    </div>
                                    <div class="col-md-6">
                                        <label>Approver Role:</label>
                                        <select style="border: 1px solid #e5e5e5 !important;" name="approval_steps[${stepIndex}][role]" class="form-control" required>
                                            <option value="">Select Role</option>
                                            ${rolesOptions}
                                        </select>
                                    </div>
                                    <div class="col-md-2 ManFiled">
                                        <label>Is Mandatory:</label>
                                        <input type="hidden" name="approval_steps[${stepIndex}][mandatory]" value="0">
                                        <input type="checkbox" name="approval_steps[${stepIndex}][mandatory]" class="form-check-input" value="1">
                                    </div>
                                    <div class="col-md-1">
                                        <button type="button" class="wk_right_btn remove-step"><i class="fa fa-times" aria-hidden="true"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#approval-steps-container').append(stepHtml);
            stepIndex++;
            reindexApprovalSteps();
            updateRoleOptions();
        });

        // Remove a step
        $(document).on('click', '.remove-step', function() {
            $(this).closest('.approval-step').remove();
            reindexApprovalSteps();
            updateRoleOptions();
        });

        // When any role changes, update duplicates
        $(document).on('change', 'select[name^="approval_steps"]', function() {
            updateRoleOptions();
        });

        // Initial run
        reindexApprovalSteps();
        updateRoleOptions();
    });
</script>
@endsection