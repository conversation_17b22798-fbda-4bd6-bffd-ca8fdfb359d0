  @extends('common.layout')
  @section('styles')
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <style>
      .bk_date {
          width: 12% !important
      }

      .bk_shift {
          width: 7% !important
      }

      .bk_cagry {
          width: 8% !important
      }

      .bk_staff {
          width: 16% !important
      }

      .bk_status {
          width: 13% !important
      }

      .bk_details {
          width: 15% !important
      }

      .select2-container--default .select2-selection--single {
          height: 36px;
          padding-top: 2px
      }

      .select2-container--default .select2-selection--single .select2-selection__rendered {
          color: #494949 !important;
          font-size: 12px
      }

      .select2-container--default .select2-selection--single .select2-selection__placeholder,
      .select2-results__option {
          font-size: 12px !important
      }

      .ui-datepicker select.ui-datepicker-month,
      .ui-datepicker select.ui-datepicker-year {
          width: 43%;
          float: left;
      }

      .modal-header {
          color: #000000;
          background: #f2f8ff;
          border: 1px solid #dde9f7;
      }

      .modal-header .close {
          color: #000000;
          margin-right: 0;
      }

      .modal-title {
          margin: 0;
          color: #494949;
      }



      .flexRow {
          overflow: hidden;
          width: 100%;
          padding-bottom: 20px;
      }

      .ScrollRow {
          width: 140%;
          display: flex
      }

      ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
      }

      ::-webkit-scrollbar-track {
          background-color: #ffffff;
          -webkit-border-radius: 10px;
          border-radius: 10px;
      }


      ::-webkit-scrollbar-thumb {
          -webkit-border-radius: 10px;
          border-radius: 10px;
          background: #98c3f5;
      }

      .modal-header .close {
          color: #000000;
          margin-right: 0;
          position: relative;
          z-index: 10000;
      }


      @media (min-width: 768px) {
          .modal-dialog {
              width: 1200px;
              margin: 30px auto;
          }
      }

      .NesShiftTitle {
          background: #f2f8ff;
          padding: 10px;
          border-radius: 5px;
          border: 1px solid #dde9f7;
          margin: 20px;
          margin-top: 56px !important;
          font-size: 20px;
          font-weight: 600;
          color: #494949;
      }

      .alert-warning {
          font-size: 16px;
          font-weight: bold;
          color: #852204;
          background-color: #fff3cd;
          border: 1px solid #ffeeba;
          border-radius: 5px;
      }
      .amend-button-amber {
          background-color: #efa603 !important;
          border-color: #eea236 !important;
          color: #fff !important;
      }
      
      /* Pagination Styles */
      .pagination-wrapper {
          margin-top: 20px;
          padding: 20px 0;
      }
      
      .pagination {
          margin: 0;
      }
      
      .pagination .page-link {
          color: #494949;
          background-color: #fff;
          border: 1px solid #dde9f7;
          padding: 8px 12px;
          margin: 0 2px;
          border-radius: 4px;
          font-size: 14px;
      }
      
      .pagination .page-item.active .page-link {
          background-color: #007bff;
          border-color: #007bff;
          color: #fff;
      }
      
      .pagination .page-link:hover {
          background-color: #f2f8ff;
          border-color: #dde9f7;
          color: #494949;
      }
      
      .pagination .page-item.disabled .page-link {
          color: #6c757d;
          background-color: #fff;
          border-color: #dee2e6;
      }
  </style>
  @endsection


  @section('content')
  @php use Carbon\Carbon; @endphp


  <div class="Container_Fluid_Main">
      <div class="row SecTItleBrder ml0 mr0 headTitleBtm">

          <div class="col-md-9 pl0">
              <div class="GpBoxHeading no-bdr pb0 mb0">
                  <h4>
                      Archived Shifts
                  </h4>
              </div>
          </div>

          <div class="col-md-3 text-right pr0">
              <form action="" method="get">
                  <button style="position: relative;top: -2px;padding: 5px 16px 7px !important" class="btn btn-primary pull-right" id="filtercancelled">
                      <input type="checkbox" class="chkBox" name="status" value="7" id="showAllShifts"
                          style="transform: scale(1.2); 
                      transform-origin: center;
                      margin-right: 5px;position: relative;top: 2px;"
                          @if (request('status')==7) checked @endif>
                      @if (request('status') == 7)
                      Hide cancelled
                      @else
                      Show all shifts
                      @endif
                  </button>
              </form>

              <a style="position: relative;top: 7px;" href="#" class="btn-three ArchBtn" data-toggle="modal" data-target="#FiltereArchived">Filter options</a>

          </div>

      </div>
  </div>

  <div class="modal fade" id="FiltereArchived" tabindex="-1" role="dialog" aria-labelledby="FiltereArchived" aria-hidden="true">
      <div class="BookingModalSection">
          <div class="modal-content NewModalContent">
              <div class="GpBoxHeading mb0 no-bdr">
                  <h4>
                      Archived Shifts Filter
                  </h4>
              </div>
              <div class="">
                  <div class="MainBoxRow">
                      @if (@auth()->user()->portal_type == 'App\Models\Client')
                      <div class="FiltBgOne">
                          <form method="GET" action="{{ route('client.archived-booking') }}">
                              <div class="row">
                                <!-- Booking ID Filter -->
                                  <div class="col-md-3">
                                      <label for="bookingId">Booking ID</label>
                                      <input type="text" name="bookingId" id="bookingId" class="form-control"
                                          value="{{ request('bookingId') }}" placeholder="Enter Booking ID">
                                  </div>
                                  <div class="col-md-3">
                                      <label for="unitId">Unit</label>
                                      <select name="unitId" id="unitId" class="form-control">
                                          <option value="">Select Unit</option>
                                          @foreach ($unitsdropdown as $unit)
                                          <option value="{{ $unit->clientUnitId }}"
                                              {{ request('unitId') == $unit->clientUnitId ? 'selected' : '' }}>
                                              {{ $unit->name }}
                                          </option>
                                          @endforeach
                                      </select>
                                  </div>
                                   <!-- Date Range Filter -->
                                  <div class="col-md-3">
                                      <label for="dateRange">Date Range</label>
                                        <input type="text" name="dateRange" id="date_range" class="form-control" placeholder="Select the date">
                                  </div>
                                  <!-- Shift Filter -->
                                  <div class="col-md-3">
                                      <label for="shiftId">Shift</label>
                                      <select name="shiftId" id="shiftId" class="form-control">
                                          <option value="">Select Shift</option>
                                          @foreach ($shifts as $shift)
                                          <option value="{{ $shift->shiftId }}"
                                              {{ request('shiftId') == $shift->shiftId ? 'selected' : '' }}>
                                              {{ $shift->name }}
                                          </option>
                                          @endforeach
                                      </select>
                                  </div>
                                  <!-- Category Filter -->
                                  <div class="col-md-3">
                                      <label for="categoryId">Category</label>
                                      <select name="categoryId" id="categoryId" class="form-control">
                                          <option value="">Select Category</option>
                                          @foreach ($categories as $category)
                                          <option value="{{ $category->categoryId }}"
                                              {{ request('categoryId') == $category->categoryId ? 'selected' : '' }}>
                                              {{ $category->name }}
                                          </option>
                                          @endforeach
                                      </select>
                                  </div>
                                  <!-- Staff Name Filter -->
                                  <div class="col-md-3">
                                  <label for="staffId">Staff Name</label> <br>
                                  <select style="width: 100%;" name="staffId" id="staffId" class="form-control select2">
                                      <option value="">Select Staff</option>
                                      @foreach ($staffList as $staff)
                                      <option value="{{ $staff->staffId }}" {{ request('staffId') == $staff->staffId ? 'selected' : '' }}>
                                          {{ $staff->full_name }} ({{ $staff->category->name ?? 'N/A' }})
                                      </option>
                                      @endforeach
                                  </select>
                              </div>
                            <div class="col-md-6 text-right">
                                      <button type="submit" class="btn btn-filter btmt4">
                                            <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                                      </button>
                                      <a href="{{ route('client.archived-booking') }}" class="btn btn-reset btmt4">
                                          <img class="reset_img" src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                                      </a>
                                  </div>
                          </form>
                      </div>
                  </div>
                  @endif
              </div>
              <div class="Mfooter mt25">
                  <button type="button" class="btn btn-three" data-dismiss="modal">Close</button>
              </div>
          </div>
      </div>
  </div>

  </div>


  <div class="Container_Fluid_Main">
      <div style="margin-top: -17px;" class="scrolViewnsr">
          @if($bookings->isEmpty())
          <div class="alert alert-warning text-center mt-4">
              No data available for the selected filters.
          </div>
          @else
          <div class="shift_box">
              <div class="shift_bottom">
                  <div class="shift_line">
                      <div class="bk_id bold" style="width: 93px !important;">Booking ID</div>
                      <div class="bk_id bold" style="width: 9% !important;">Units</div>
                      <div style="width: 85px !important;" class="bk_date bold">Date</div>
                      <div style="width:70px !important;" class="bk_shift bold">Shift</div>
                      <div style="width:78px !important;" class="bk_cagry bold">Category</div>
                      <div class="bk_staff bold" style="width: 20% !important;">Staff</div>
                      <div style="width:60px !important;" class="bk_cagry bold">Profile</div>
                      @if (Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
                      Auth::user()->can('client_portal_booking_amend_shift'))
                      {{-- <div class="bk_amnd bold" style="width: 70px !important;">Amend</div> --}}
                      @endif
                      <div class="bk_amnd bold" style="width: 45px !important;">Log</div>
                      <div class="bk_status bold" style="width: 17% !important;">Status</div>
                      <div style="width:135px !important;text-align: right;" class="bk_details bold">Details</div>
                  </div>
                  <ul>
                      @foreach ($bookings as $key => $booking)
                      <li>
                          <div class="bk_id " style="width: 93px !important;">{{ $booking->bookingId }}</div>
                          <div class="bk_id" style="width: 9% !important;">{{ $booking->unit->name }}</div>
                          <div style="width:85px !important;" class="bk_date ">
                              {{ date('d-M-Y D', strtotime($booking->date)) }}
                          </div>
                          <div style="width:70px !important;" class="bk_shift ">{{ $booking->shift->name }}</div>
                          <div style="width:78px !important;" class="bk_cagry ">
                              {{ $booking->category->name }}
                          </div>
                          <div style="width: 20% !important;" class=" bk_staff ">
                              @if ($booking->staffId != null)
                              <img src="https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/staff/photo/{{ $booking->staff->photo }}"
                                  class="thmbImg">
                              <span>{{ $booking->staff->full_name }}</span>
                              @else
                              @if ($booking->unitStatus == 2)
                              <span class="redFont">Cancelled</span>
                              @else
                              <i class="fa fa-search idSeachStaf" aria-hidden="true"></i>
                              @endif
                              @endif
                          </div>

                          <div style="width:60px !important;text-align: center;" class="bk_cagry">
                              {!! $booking->profile !!}
                          </div>
                          {{-- @if (Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
                          Auth::user()->can('client_portal_booking_amend_shift'))
                          <div class="@if ($booking->unitAmend == 1) bk_amnded_amber @else bk_amnd @endif 
                         @if ($booking->unitStatus == 2) bk_amnded @endif"
                              style="width: 70px !important;text-align: center;">
                              <button bookid="{{ $booking->bookingId }}" token="{{ csrf_token() }}"
                                  fetch="{{ route('get.booking') }}" class="view amendNow"
                                  timeDiff="{{ $booking->totalhoursto }}"
                                  totalhrspend="{{ round($booking->totalhoursto) }}"
                                  imNotes="{{ $booking->importantNotes }}">
                                  <i class="fa fa-wrench" aria-hidden="true"></i>
                              </button>

                          </div>
                          @endif --}}
                          <div class="bk_cagry " style="width:45px !important;">
                              <a class="{{ $booking->chats->where('color', 1)->count() > 0 ? 'log_button warngBtn' : 'log_button' }}"
                                  booking_id="{{ $booking->bookingId }}" unit_id="{{ $booking->unitId }}"
                                  staff="{{ $booking->staff->full_name ?? '' }}" unit="{{ $booking->unit->alias }}"
                                  category="{{ $booking->category->name }}" shift="{{ $booking->shift->name }}"
                                  date="{{ date('d-M-Y, D', strtotime($booking->date)) }}"
                                  start="{{ date('h:i', strtotime($booking->start_time)) }}"
                                  end="{{ date('h:i', strtotime($booking->end_time)) }}">
                                  <i class="fa fa-clock-o" aria-hidden="true"></i>
                              </a>
                          </div>
                          <div class="bk_status" style="width: 17% !important;">
                              @switch ($booking->unitStatus)
                              @case(2)
                              <a href="#" class="cancel">Cancelled</a>
                              @break

                              @case(4)
                              @php

                              $endDate = $booking->date; // format: Y-m-d
                              $endTime = $booking->end_time; // format: H:i:s (24-hour)
                                $endDateTime = null;

                                if (!empty($endDate) && !empty($endTime)) {
                                    try {
                                        $endDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $endDate . ' ' . $endTime);
                                    } catch (\Exception $e) {
                                        $endDateTime = null;
                                    }
                                }
                              @endphp
                              @if ($booking->staffStatus == 3 && $endDateTime && $endDateTime->isPast())
                              @if ($booking->timesheet->image_url ?? false)
                              @if ($booking->timesheet->status == 2)
                              <a style="cursor: initial;" href="#" class="confrimd">TS Approved</a>
                              @else
                              <a style="cursor: initial;" href="#" class="confrimd">TS Uploaded</a>
                              @endif
                              @else
                              <a style="cursor: initial;"href="#" class="confrimd">Completed</a>
                              @endif
                              @else
                              <a style="cursor: initial;" href="#" class="confrimd">Confirmed</a>
                              @endif
                              @break

                              @case(1)
                              <a href="#" class="awaiting">Awaiting Approval</a>
                              @break
                              @endswitch
                              @if ($booking->bookedby && $booking->bookingWorkflows()->whereNotNull('workflow')->first())
                              <a href="#" class="BtProgress" data-toggle="modal"
                                  data-target="#ProgressModal{{ $key }}">
                                  <i class="fa fa-info-circle" aria-hidden="true"></i></a>

                              @include('common.bookings.workflowStatus')
                              @endif
                          </div>

                          <div style="width:135px !important;" class="bk_details">
                              {!! $booking->details !!}
                          </div>
                      </li>
                      @endforeach
                  </ul>
              </div>
          </div>
          
          <!-- Pagination Links -->
          <div class="row mt-4">
              <div class="col-md-6">
                  <div class="pagination-info">
                      <p class="text-muted">
                          Showing {{ $bookings->firstItem() }} to {{ $bookings->lastItem() }} of {{ $bookings->total() }} results
                      </p>
                  </div>
              </div>
              <div class="col-md-6">
                  <div class="pagination-wrapper d-flex justify-content-end">
                      {{ $bookings->links() }}
                  </div>
              </div>
          </div>
          @endif
      </div>
  </div>

  @include('unitPortal.bookingAmendModal')
  @include('unitPortal.newShiftModel')
  @include('unitPortal.bookingAmendConfirmationModal')
  @include('unitPortal.bookingUnitLogBook')

  <div class="modal" id="unableToCancelModal">
      <div class="modal-dialog" style="width: 56%;height:115%;">
          <!-- Modal content-->
          <div class="modal-content">
              <div class="modal-header header_txt">
                  <h4 class="modal-title"><b>Booking Amend</b> <button type="button" class="close"
                          data-dismiss="modal">×</button> </h4>
              </div>
              <div class="row" style=" margin: 0; ">
                  <div class="col-sm-12 ptl-20 bookDataHtml">
                  </div>

                  <div class="col-sm-12 ptl-20">
                      <div class="amd_fullwidth">
                          <h4 class="amdOK">The shift starts in less than 24 hours, Please contact our office, <span
                                  class="hil_cl">01935 315031</span> to make any changes.</h4>
                      </div>
                  </div>
                  <div class="col-sm-12">
                      <div class="text-center mb20 m-t-10 amendActionButtons">
                          <button type="button" class="amdbtn_green amendClose">OK</button>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  <div id="modal_new_requestby" class="modal">
      <!-- Modal content -->
      <div class="modal_changepass">
          <div class="modal-header">
              <span class="close closeBtnModal">&times;</span>
              <h2>Add New Contact</h2>
          </div>
          <div class="modal-body">
              <div class="inlineBlock">
                  <p>Full Name</p>
                  <input type="text" name="NewCnt_name" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Position</p>
                  <input type="text" name="NewCnt_position" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Phone</p>
                  <input type="text" name="NewCnt_phone" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Email</p>
                  <input type="text" name="NewCnt_email" class="txt_bx_modal">
              </div>
          </div>
          <div class="modal-footer">
              <input type="button" class="add_save_modal amdbtn_green" value="Save" action="#"
                  token="{{ csrf_token() }}">
              <input type="button" token="" class="amdbtn_red add_save_modal_close closeBtnModal"
                  value="Close">
          </div>
      </div>
  </div>



  @endsection
  @section('scripts')
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <script src="{{ asset('unit-portal/js/bookings.js') }}"></script>

  <script>
      $(document).ready(function() {
          $("body").on("click", ".workflow-approve-btn", function() {
              var bookingId = $(this).data('id');
              var action = $(this).data('value');
              var token = $('meta[name="csrf-token"]').attr('content');
              if (!action) {
                  var note = prompt('Add Comments');
              }
              $.ajax({
                  url: "{{ route('workflows.approve') }}",
                  type: "POST",
                  data: {
                      id: bookingId,
                      _token: token,
                      note: note,
                      action: action
                  },
                  success: function(response) {
                      Swal.fire(
                          'Success',
                          'Approval status updated successfully.',
                          'success'
                      ).then(function() {
                          location.reload();
                      });


                  },
                  error: function(xhr, status, error) {
                      Swal.fire(
                          'Error',
                          xhr.responseJSON.message,
                          'error'
                      ).then(function() {
                          location.reload();
                      });
                  }
              });
          });
      });
  </script>
  @section('scripts')
  <script src="{{ asset('unit-portal/js/booking_shift.js') }}"></script>
  <script src="{{ asset('unit-portal/js/booked-shift.js') }}?v={{ time() }}"></script>
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

  <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">

  <script>
    $(function () {
      const threeMonthsAgo = moment().subtract(3, 'months');
      const farPast = moment().subtract(10, 'years'); // Or pick a date far in the past
  
      $('#date_range').daterangepicker({
        autoApply: true,
        autoUpdateInput: false,
        locale: {
          format: 'YYYY-MM-DD',
          separator: ' to ',
        },
        opens: 'center',
        alwaysShowCalendars: true,
  
        // ✅ Restrict range from 10 years ago up to exactly 3 months ago
        minDate: farPast,
        maxDate: threeMonthsAgo
      });
  
      // ✅ Populate the input field on selection
      $('#date_range').on('apply.daterangepicker', function (ev, picker) {
        $(this).val(
          picker.startDate.format('YYYY-MM-DD') + ' to ' + picker.endDate.format('YYYY-MM-DD')
        );
      });
  
      // ✅ Clear input if user cancels
      $('#date_range').on('cancel.daterangepicker', function (ev, picker) {
        $(this).val('');
      });
    });
  </script>
  
  <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0/dist/js/select2.min.js"></script>
  <script>
      $(document).ready(function() {
          $('#staffId').select2({
              placeholder: "Select Staff",
              allowClear: true
          });
      });
  </script>
  @endsection