@extends('common.layout')

@section('styles')

<link rel="stylesheet" href="{{asset('unit-portal/css/bookshifts.css')}}">
<link rel="stylesheet" href="{{asset('unit-portal/css/daterangepicker.css')}}">
<style>
    .flexRow {
        overflow: hidden;
        width: 100%;
        padding-bottom: 20px;
    }

    .ScrollRow {
        display: flex;
        align-items: center;
        width: 100%;
        justify-content: center;
    }

    .modal-content {
        width: 100%;

    }

    .modal-dialog {
        width: 67%;
    }

    /* Enhanced Pagination Styles */
    .pagination-wrapper {
        border: 1px solid #ddd;
    }
    
    .pagination-info p {
        font-size: 14px;
        line-height: 34px; /* Match pagination button height */
    }
    
    .pagination > li > a,
    .pagination > li > span {
        padding: 8px 12px;
        margin: 0 2px;
        border-radius: 4px;
        border: 1px solid #ddd;
        color: #337ab7;
    }
    
    .pagination > li > a:hover,
    .pagination > li > a:focus {
        background-color: #eee;
        color: #23527c;
    }
    
    .pagination > .active > a,
    .pagination > .active > span {
        background-color: #337ab7;
        border-color: #337ab7;
        color: white;
    }
    
    .pagination > .disabled > span,
    .pagination > .disabled > a {
        color: #777;
        background-color: #fff;
        border-color: #ddd;
        cursor: not-allowed;
}
        .amend-button-amber {
        background-color: #efa603 !important;
        border-color: #eea236 !important;
        color: #fff !important;
    }
    
    /* Red background for cancelled shifts */
    .bk_amnded .amendNow {
        background-color: #F44336 !important;
        border-color: #d32f2f !important;
        color: #fff !important;
    }
    
    /* Amber background for amended shifts (takes precedence over cancelled) */
    .bk_amnded_amber .amendNow {
        background-color: #efa603 !important;
        border-color: #eea236 !important;
        color: #fff !important;
    }

</style>
@endsection


@section('content')

@php 
//@if(auth()->guard('unit')->user()->type != 5) 
@endphp

<div class="Container_Fluid_Main">

    <div class="row headTitleBtm">
        <div class="col-sm-12">
             <div class="GpBoxHeading no-bdr pb0"> 
                <h4>
                    All Shifts
                </h4>
            </div>
        </div>
    </div><!-- /.container-fluid -->
</div>

<div class="Container_Fluid_Main ">

<div class="MainBoxRow mb25" style="    min-height: 92px;">
<div class="add_booking">
    <div class="addBooksDivs">
        <form name="filter" action="" method="get">
            <div class="row full_width" style="background: none;">
                <div class="col-md-2">
                    <p>Unit</p>
                    <select name="unitId" id="unitId" class="form-control">
                        <option value="">Select Unit</option>
                        @foreach ($unitsdropdown as $unit)
                        <option value="{{ $unit->clientUnitId }}"
                            {{ request('unitId') == $unit->clientUnitId ? 'selected' : '' }}>
                            {{ $unit->name }}
                        </option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2" style="padding-left:0">
                    <p>Date</p>
                    <!-- Date Range Filter -->
                    <input type="text" name="date" class="txt_bx dateranger"
                        value="{{ request('date') ?? '' }}" 
                        placeholder="Please select the date" required>
                </div>
                <div class="col-md-2" style="padding:0">
                    <p>Shift</p>
                    <select name="shift" class="txt_bx">
                        <option value=""> -- Select an Option -- </option>
                        <option @if(empty(request('shift'))) selected @endif value="">All Shifts </option>
                        @foreach($shifts as $shift)
                        @if(in_array($shift->shiftId, array(1,2,3,4)))
                        <option @if(request('shift')==$shift->shiftId) selected="selected" @endif
                            value="{{$shift->shiftId}}">{{$shift->name}}</option>
                        @endif
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2">
                    <p>Staff Category</p>
                    <select name="category" class="txt_bx">
                        <option value=""> -- Select an Option -- </option>
                        <option @if(empty(request('category'))) selected @endif value="">All Category </option>
                        @foreach($categories as $category)
                        <option @if(request('category')==$category->categoryId) selected="selected" @endif
                            value="{{$category->categoryId}}">{{$category->name}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-2" style="padding-left:0">
                    <p>Status</p>
                    <select name="status" class="txt_bx">
                        <option value="7" @if(request('status')==7) selected="selected" @endif>All</option>
                        <option value="4" @if(request('status')==4) selected="selected" @endif>Confirmed</option>
                        <option value="2" @if(request('status')==2) selected="selected" @endif>Cancelled</option>
                        <option value="1" @if(request('status')==1) selected="selected" @endif>Awaiting Approval</option>
                        <option value="8" @if(request('status')==8) selected="selected" @endif>Completed</option>
                        <option value="9" @if(request('status')==9) selected="selected" @endif>TS Uploaded</option>
                        <option value="10" @if(request('status')==10) selected="selected" @endif>TS Approved</option>

                    </select>
                </div>
                <div class="col-md-2">
                    <p>&nbsp;</p>
                    <button type="submit" style="float: left;" class="btn-filter">
                        <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                    </button>
                    <a style="margin:0 4px; padding: 7px 20px;" href="{{route('booked.shifts')}}" class="rtsbtmar restBtn btn-reset">
                        <img class="reset_img" src="{{ asset('unit-portal/images/reset.svg') }}" alt="Reset">
                    </a>
                    <input type="button" style=" padding: 7px 20px;" class="btn_save add_new_booking btn-three" value="New Booking" data-toggle="modal"
                        data-target="#newShiftModel">
                </div>
            </div>
            

    </div>
</div>
</div>
        </form>


@php //@endif 
@endphp
<div class="row">
    <div class="entriesSec datecent">
        <div class="col-md-2">
             <label class="entry_lab" for="per_page">Show</label>
                    <select style="margin: 0;" class="entry_bx per_page" name="per_page" id="per_page">
                        <option value="15" @if(request('per_page', 15)==15) selected="selected" @endif>15</option>
                        <option value="20" @if(request('per_page', 15)==20) selected="selected" @endif>20</option>
                        <option value="50" @if(request('per_page', 15)==50) selected="selected" @endif>50</option>
                        <option value="100" @if(request('per_page', 15)==100) selected="selected" @endif>100</option>
                    </select><span class="entry_lab">entries</span>
        </div>
        <div class="col-md-8 datecent">
            <div class="row comingDays">
                <div class="futrRest">
                    @foreach($summary as $bookSumryItm)
                    @if(date('D',strtotime($bookSumryItm['date'])) == "Sat" ||
                    date('D',strtotime($bookSumryItm['date']))== "Sun")
                    @php $style= "style=color:red;"; @endphp
                    @else
                    @php $style="style="; @endphp
                    @endif
                    @if($bookSumryItm['selection']==1)
                    @php $selection_class="selected_warning_button"; @endphp
                    @else
                    @php $selection_class=""; @endphp
                    @endif
                    @if($bookSumryItm['count'] > 3)
                    <a
                        href="{{route('booked.shifts')}}/?date={{date('d-m-Y',strtotime($bookSumryItm['date']))}}+-+{{date('d-m-Y',strtotime($bookSumryItm['date']))}}">
                        <span class="correctDate">
                            <div class="smalHead" style="font-size:11px;"><label
                                    {{  $style }}>{{date('d',strtotime($bookSumryItm['date']))}}-{{date('D',strtotime($bookSumryItm['date']))}}
                                </label></div>
                            <button
                                class="btn btndate_success btn-xs mrs m-r-10 wdth100 {{ $selection_class }}">{{$bookSumryItm['count']}}</button>
                        </span></a>
                    @elseif($bookSumryItm['count'] <= 3) <a
                        href="{{route('booked.shifts')}}/?date={{date('d-m-Y',strtotime($bookSumryItm['date']))}}+-+{{date('d-m-Y',strtotime($bookSumryItm['date']))}}">
                        <span class="correctDate">
                            <div class="smalHead" style="font-size:11px;"><label
                                    {{  $style }}>{{date('d',strtotime($bookSumryItm['date']))}}-{{date('D',strtotime($bookSumryItm['date']))}}</label>
                            </div>
                            <button
                                class="btn btndate_warning btn-xs mrs m-r-10 wdth100 {{ $selection_class }}">{{$bookSumryItm['count']}}</button>
                        </span></a>
                        @else
                        <a
                            href="{{route('booked.shifts')}}/?date={{date('d-m-Y',strtotime($bookSumryItm['date']))}}+-+{{date('d-m-Y',strtotime($bookSumryItm['date']))}}">
                            <span class="correctDate">
                                <div class="smalHead" style="font-size:11px;"><label
                                        {{  $style }}>{{date('d',strtotime($bookSumryItm['date']))}}-{{date('D',strtotime($bookSumryItm['date']))}}</label>
                                </div>
                                <button
                                    class="btn btndate_danger btn-xs mrs m-r-10 wdth100 {{ $selection_class }}">{{$bookSumryItm['count']}}</button>
                            </span></a>
                        @endif
                        @endforeach
                </div>
            </div>
        </div>
        
        <div class="col-md-2">
            <form action="" method="get">
                <button class="btn btn-primary pull-right" id="filtercancelled">
                    <input type="checkbox" class="chkBox" name="status" value="7" id="showAllShifts" style="ttransform: scale(1.2); 
                      transform-origin: center;
                      margin-right: 5px;position: relative;top: 1px;"
                        @if(request('status')==7) checked @endif> @if(request('status')==7) Hide cancelled @else Show all shifts @endif
                </button>
            </form>
        </div>
    </div>

</div>


<div class="shift_box scrolvViewShift">
    <div class="shift_bottom">
        <div class="shift_line">
            <div style="width: 93px !important;" class="bk_id bold">Booking ID</div>
            <div style="width: 93px !important;" class="bk_id bold">Units</div>
            <div style="width: 85px !important;" class="bk_date bold">Date</div>
            <div style="width:70px !important;" class="bk_shift bold">Shift</div>
            <div style="width:78px !important;" class="bk_cagry BkCat bold">Category</div>
            <div style="width: 24% !important;" class="bk_staff BkStafN bold">Staff</div>
            <div style="width:65px !important;" class="bk_cagry bold">Profile</div>
            {{-- <div class="bk_cagry bold">Cost</div> --}}
            @if(Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
            Auth::user()->can('client_portal_booking_amend_shift'))
            <div class="bk_amnd " style="width: 70px !important;">Amend</div>
            @endif
            {{-- @can('unit_portal_all_booked_shift_amend_shift')
            <div class="bk_amnd bold" style="width: 75px !important;">Amend</div>
            @endcan --}}
            @if(Auth::user()->portal_id!=7)
            <div style="width: 45px !important;" class="bk_cagry bold">Log</div>
            @endif
            <div class="bk_status bold" style="width: 17% !important;">Status</div>
            <div class="bk_details bold" style="width:135px !important;">Details</div>
        </div>
        <ul>

            @foreach($bookings as $key => $booking)
            @if($booking->start_time != null)
            <li>
                <div style="width: 93px !important;" class="bk_id ">{{$booking->bookingId}}</div>
                <div style="width: 93px !important;" class="bk_id ">{{ $booking->unit->name }}</div>
                <div style="width: 85px !important;" class="bk_date ">{{date('d-M-Y D',strtotime($booking->date))}}
                </div>
                <div style="width:70px !important;" class="bk_shift ">{{$booking->shift->name}}</div>
                <div style="width:78px !important;" class="bk_cagry BkCat ">{{$booking->category->name}}</div>
                <div style="width: 24% !important;" class="bk_staff BkStafN">
                    @if($booking->staffId != null)
                        <img src="https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/staff/photo/{{$booking->staff->photo}}"
                        class="thmbImg">

                        <span>{{$booking->staff->full_name}}</span>
                        @if($booking->unitStatusNumber == 2) 
                            <span class="redFont">Cancelled</span>
                        @elseif($booking->inform_status == 2)
                            Acknowledged
                        @endif
                    @else 
                        @if($booking->unitStatusNumber == 2) 
                            <span class="redFont">Cancelled</span>
                        @else
                            <i class="fa fa-search idSeachStaf" aria-hidden="true"></i>
                        @endif
                    @endif
                </div>
                {{-- <div style="width:78px !important;" class=" bk_cagry ">
              <a href="#" class="exploreBtn profile"><i class="fa fa-location-arrow" aria-hidden="true"></i></a>
            </div> --}}
                <div style="width:65px !important;" class=" bk_cagry">{!!$booking->profile!!}</div>
                {{-- <div class="bk_cagry">{!!$booking->costPerShift!!}</div> --}}
                @if(Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
                Auth::user()->can('client_portal_booking_amend_shift'))
                <div class="@if(($booking->unitAmend ?? 0)==1) bk_amnded_amber @else bk_amnd @endif 
                   @if ($booking->unitStatusNumber == 2) bk_amnded @endif"
                    style="width: 70px !important;text-align: center;">
                    <button style="margin-top: 1px;" bookid="{{ $booking->bookingId }}" token="{{ csrf_token() }}"
                        fetch="{{ route('get.booking') }}" class="view amendNow @if(($booking->unitAmend ?? 0)==1) amend-button-amber @endif"
                        timeDiff="{{$booking->totalhoursto}}" totalhrspend="{{round($booking->totalhoursto)}}"
                        imNotes="{{ $booking->importantNotes }}">
                        <i class="fa fa-wrench" aria-hidden="true"></i>
                    </button>

                </div>
                @endif
                {{-- @can('unit_portal_all_booked_shift_amend_shift')
                <div style="width: 75px !important;" class=" bk_amnd">
                    <button bookid="{{$booking->bookingId}}" token="{{csrf_token()}}" fetch="{{route('get.booking')}}"
                        class="view amendNow" timeDiff="{{$booking->diff_in_minutes}}"
                        totalhrspend="{{round($booking->diff_in_minutes)}}">
                        <i class="fa fa-wrench" aria-hidden="true"></i>
                    </button>
                </div>
                @endcan --}}
                @if(Auth::user()->portal_id!=7)
                <div style="width: 45px !important;" class="bk_cagry ">
                    <a class="{{ $booking->chats->where('color', 1)->count() > 0 ? 'log_button warngBtn' : 'log_button' }}"
                        booking_id="{{ $booking->bookingId }}" unit_id="{{ $booking->unitId }}"
                        staff="{{ $booking->staff->full_name }}" unit="{{ $booking->unit->alias }}"
                        category="{{ $booking->category->name }}" shift="{{ $booking->shift->name }}"
                        date="{{ date('d-M-Y, D',strtotime($booking->date)) }}"
                        start="{{  date('h:i',strtotime($booking->start_time)) }}"
                        end="{{  date('h:i',strtotime($booking->end_time)) }}">
                        <i class="fa fa-clock-o" aria-hidden="true"></i>
                    </a>
                </div>
                @endif
                <div class="bk_status" style="width: 17% !Important;"> {!!$booking->unitStatus!!}
                    @if ( $booking->bookedby && $booking->bookingWorkflows()->whereNotNull('workflow')->first() && $booking->unitStatusNumber != 2 && $booking->unitStatusNumber != 4)
                    <a href="#" class="BtProgress" data-toggle="modal" data-target="#ProgressModal{{ $key }}">
                        <i class="fa fa-info-circle" aria-hidden="true"></i></a>
                    @include('common.bookings.workflowStatus')
                    @endif
                </div>
                <div style="width: 135px !important;" class="bk_details">
                    {!!$booking->details!!}
                </div>
            </li>
            @endif
            @endforeach
        </ul>
        
        <!-- Enhanced Pagination Section -->
        <div class="pagination-wrapper" style="margin-top: 20px; padding: 15px; background: #f9f9f9; border-radius: 5px;">
            <div class="row">
                <div class="col-md-6">
                    <div class="pagination-info">
                        <p style="margin: 0; color: #666;">
                            Showing {{ $bookings->firstItem() ?? 0 }} to {{ $bookings->lastItem() ?? 0 }} 
                            of {{ $bookings->total() }} entries
                        </p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="pgntn text-right">
                        {{$bookings->appends(request()->all())->links()}}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-2">
            <strong>Total Entries: {{ $bookings->total() }}</strong>
        </div>
    </div>
</div>
</div>

</div>

@endsection



@include('unitPortal.bookingAmendModal')
@include('unitPortal.newShiftModel')
@include('unitPortal.bookingAmendConfirmationModal')
@include('unitPortal.bookingUnitLogBook')
<div class="modal" id="unableToCancelModal">
    <div class="modal-dialog" style="width: 56%;height:115%;">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header header_txt">
                <h4 class="modal-title"><b>Booking Amend</b> <button type="button" class="close"
                        data-dismiss="modal">×</button> </h4>
            </div>
            <div class="row" style=" margin: 0; ">
                <div class="col-sm-12 ptl-20 bookDataHtml">
                </div>

                <div class="col-sm-12 ptl-20">
                    <div class="amd_fullwidth">
                        <h4 class="amdOK">The shift starts in less than 24 hours, Please contact our office, <span
                                class="hil_cl">01935 315031</span> to make any changes.</h4>
                    </div>
                </div>
                <div class="col-sm-12">
                    <div class="text-center mb20 m-t-10 amendActionButtons">
                        <button type="button" class="amdbtn_green">OK</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@section('scripts')
<script src="{{ asset('unit-portal/js/booking_shift.js') }}?v={{ time() }}"></script>
<script src="{{ asset('unit-portal/js/bookings.js') }}"></script>
<script src="{{ asset('unit-portal/js/daterangepicker.min.js') }}"></script>
<script>
    $(document).ready(function() {
        var today = moment();
        var in15Days = moment().add(15, 'days');

        $('input[name="date"]').daterangepicker({
            autoUpdateInput: false,
            // minDate: today,
            // maxDate: in15Days,
            startDate: today,
            locale: {
                format: 'DD-MM-YYYY',
                cancelLabel: 'Clear'
            }
        });
        // Show placeholder when no date is selected
        $('input[name="date"]').attr('placeholder', 'Please select the date');

        $('input[name="date"]').on('apply.daterangepicker', function(ev, picker) {
            $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format(
                'DD-MM-YYYY'));
        });

        // Clear the input field when the date range is canceled
        $('input[name="date"]').on('cancel.daterangepicker', function(ev, picker) {
            $(this).val('');
            $(this).attr('placeholder', 'Please select the date'); // Reset placeholder
        });

        // Handle per-page selector change
        $('#per_page').on('change', function() {
            // Submit the form when per-page changes
            $(this).closest('form').submit();
        });
    });
</script>
<script>
    $(document).ready(function() {
        $("body").on("click", ".workflow-approve-btn", function() {
            var bookingId = $(this).data('id');
            var action = $(this).data('value');
            var token = $('meta[name="csrf-token"]').attr('content');
            var note = prompt('Add Comments');
            $.ajax({
                url: "{{ route('workflows.approve') }}",
                type: "POST",
                data: {
                    id: bookingId,
                    _token: token,
                    note: note,
                    action: action
                },
                success: function(response) {
                    Swal.fire(
                        'Success',
                        'Approval status updated successfully.',
                        'success'
                    ).then(function() {
                        location.reload();
                    });


                },
                error: function(xhr, status, error) {
                    Swal.fire(
                        'Error',
                        xhr.responseJSON.message,
                        'error'
                    ).then(function() {
                        location.reload();
                    });
                }
            });
        });
    });
</script>
@endsection