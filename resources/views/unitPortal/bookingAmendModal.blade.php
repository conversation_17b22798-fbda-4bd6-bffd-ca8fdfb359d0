<style>
   .modal {
        z-index: 1050 !important;
    }
    .modal-backdrop {
        z-index: 1040 !important;
    }
    </style>
      <div class="modal" id="amendModal">
        <div class="modal-dialog"  style="width: 56%;height:115%;">
          <!-- Modal content-->
          <div class="modal-content">
             <div class="modal-header header_txt">
                <h4 class="modal-title"><b>Booking Amend</b> <button type="button" class="close amendModal" data-dismiss="modal">×</button>
                </h4>
             </div>
             <div class="row">
                <div class="col-sm-6"> 
                  <div class="left_amd"></div>
                </div>
                <div class="col-sm-6">
                  <div class="right_amd"></div>
                </div>
             </div>
             <div class="modal-body ">
                <div class="box box-primary">
                   <input type="hidden" name="bookingId">
                   <input type="hidden" name="amendSelectedType">
                   <div class="box-body newBookingForm">
                      <div class="row shiftRow">
                         <div class="col-sm-12">
                            <div class="text-center mb20 m-t-10 amendActionButtons">
                               <span class="amd_select">Please Select an Option : </span>
                                <button type="button" class="amdbtn_green doAmend btn-primary">Amend</button>
                               <button type="button" class="amdbtn_red doCancel btn-three">Cancel</button>
                            </div>
                         </div>
                         <br>
    
                         <!-------------- cancelation form start---------- -->
                         <div class="hidden m-t-10 cancelDetails">
                            <div class="cancelledNotesRequestedPerson">
                            <div class="col-sm-12">
                               <div class="form-group lable_style">
                                  <label>Reason for cancelling the shift</label>
                                  <select class="form-control cancelNotes" name="cancelNotes">
                                    <option value="">Select</option>
                                    <option value="Covered the shift internally">Covered the shift internally</option>
                                    <option value="Residents change their preference">Residents change their preference</option>
                                    <option value="Poor weather">Poor weather</option>
                                    <option value="Wrong shift requested">Wrong shift requested</option>
                                    <option value="Wrong date requested">Wrong date requested</option>
                                    <option value="Reason mentioned in the box">Reason mentioned in the box</option>
                                    
                                 </select>
                                  {{-- <textarea class="form-control cancelNotes" placeholder="Please type your cancel reason" name="cancelNotes"> </textarea> --}}

                                  
                               </div>
                            </div>
                            <input type="hidden" name="cancelRequestedName" value="{{ auth()->user()->name }}">
                            <div class="col-sm-12">
                               <div class="form-group lable_style">
                                  <label>Notes<span class="redText">*</span></label>
                                 <textarea class="form-control cancelNotes" placeholder="Please type your cancel reason" name="cancelNotes"> </textarea>
                               </div>
                            </div>
                         </div>
                            <div class="col-sm-12">
                               <div class="form-group lable_style">
                                  <label>Message Preview</label>
                                  <textarea class="form-control cancelPreview" readonly="readonly" name="cancelPreview"> </textarea>
                               </div>
                            </div>
                            
                            <input type="hidden" name="cancelPreviewHidden">
                            <div class="col-sm-12 text-center">
                                <div class="mtb10"><button type="button" class="btn amdbtn_red  cancelSubmit" 
                                    action="{{route('do.cancel')}}" token="{{csrf_token()}}">
                                    Submit Cancel</button></div>
                              
                               <div class="mtb10"><button type="button" class="btn amdbtn_red  confirmCancelSubmit">Submit Cancel</button></div>
                               {{-- <div class="mtb10"><button type="button" class="btn btn-secondary backToOptions">Back</button></div> --}}
                              
                            </div>
                         </div>
                         <!-------------- cancelation form end---------- -->
    
                         <!-------------- amend form start---------- -->
                         <div class="hidden m-t-10 amendDetails">
                            <div class="notesRequestedPerson">
                            <div class="col-sm-12">
                               <div class="form-group">
                                  <label>What do you like to amend ?</label>
                                  <select class="form-control cancelNotes cancelNotesamend" name="cancelNotes">
                                    <option value="">Select</option>
                                    <option value="Change shift">Change shift</option>
                                    <option value="Change start / end time">Change start / end time</option>
                                    <option value="Change date">Change date</option>
                                    <option value="Change staff">Change staff</option>
                                    <option value="Change staff category">Change staff category</option>
                                    <option value="Change reason for booking">Change reason for booking</option>
                                    <option value="Do not want to disclose">Do not want to disclose</option>
                                    <option value="Other">Other</option>
                                 </select>
                               </div>
                            </div>
                            <input type="hidden" name="cancelRequestedName" value="{{ auth()->user()->name }}">
                            <div class="col-sm-12">
                              <div class="form-group">
                                 <label>Reason<span class="redText">*</span></label>
                                 <select class="form-control" id="reasonforammend" name="reasonforammend">
                                   
                                 </select>
                              </div>
                           </div>
                           <div class="col-sm-12">
                              <div class="form-group">
                                 <label>Notes</label>
                                 <textarea class="form-control additional_text" name="additional_text" placeholder="Please enter your texts"></textarea>
                              </div>
                           </div>
                            </div>
    
                            <div class="col-sm-12">
                               <div class="form-group">
                                  <label>Message Preview</label>
                                  <textarea class="form-control amendPreview" readonly="" name="amendPreview"></textarea>
                               </div>
                            </div>
                            <input type="hidden" name="amendPreviewHidden">
                            <div class="col-sm-12 text-center">
                                <div class="mtb10">{{--
                                  <button type="button" class="btn amdbtn_red  amendSubmit" action="{{route('client.portal.do.cancel')}}" token="{{csrf_token()}}">Submit Amend</button> 
                                  --}}
                                  <button type="button" class="btn amdbtn_red  previewAmendSubmit">Submit Amend</button>
                                  {{-- <button type="button" class="btn btn-secondary backToOptions">Back</button> --}}
                                </div>
                            </div>
                         </div>
                         <!-------------- amend form end---------- -->
                      </div>
                   </div>
                </div>
             </div>
             

          </div>
       </div>
      </div>