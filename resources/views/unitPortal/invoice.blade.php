  @extends('common.layout')

  @section('styles')
  <style>
    .file_search {
      height: 27px;
      width: 27px;
    }

    .greenFont {
      color: #02a902 !important;
    }
  </style>
  @endsection


  @section('content')


  <div class="Container_Fluid_Main">
    <div class="row headTitleBtm">
      <div class="col-sm-12">
        <div class="GpBoxHeading no-bdr pb0">
          <h4>
            Unit Invoices
          </h4>
        </div>
      </div>
    </div><!-- /.container-fluid -->
 

  
<div class="MainBoxRow mb15">

<form method="GET" action="{{ route('unit.invoices') }}">
    <div class="row mb-3">
        <div class="col-md-3">
            <select name="unit_id" class="form-control">
                <option value="">All Units</option>
                @foreach($units as $unit)
                    <option value="{{ $unit->clientUnitId }}" {{ request('unit_id') == $unit->clientUnitId ? 'selected' : '' }}>
                        {{ $unit->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <div class="col-md-3">
            <select name="status" class="form-control">
                <option value="">All Status</option>
                <option value="0" {{ request('status') == '0' ? 'selected' : '' }}>Unpaid</option>
                <option value="1" {{ request('status') == '1' ? 'selected' : '' }}>Paid</option>
                <option value="2" {{ request('status') == '2' ? 'selected' : '' }}>Overdue</option>
            </select>
        </div>
        <div class="col-md-3">
            <button type="submit" class="btn btn-filter">
                <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
            </button>
            <a href="{{ route('unit.invoices') }}" class="btn btn-reset">
               <img class="reset_img" src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
            </a>
        </div>
    </div>
</form>
</div>

 </div>

  <!-- <div class="right_section"> -->
    <div class="Container_Fluid_Main">
  <div class="shift_box">
    <div class="shift_bottom">
      <div class="shift_line">
         <table class="table table-bordered table-striped" id="my-table">

                    <thead>
                        <tr>
                            <th>Invoice No</th>
                            <th>Invoice Date</th>
                            <th>Unit</th>
                            <th>Week No.</th>
                            <th>Week Ending on</th>
                            <th>Invoice Due Date</th>
                            <th>Total Amount</th>
                            <th>Status</th>
                            <th>Action</th>
                         
                        </tr>
                    </thead>
                    <tbody> 
                       @foreach($invoice as $key => $invoices)
                      <tr>
                       
                        <td>
                        
                          {{ $invoices->invoice_number }}
                        </td>
                        <td>{{ date('d-m-Y', strtotime($invoices->invoice_date)) }}</td>
                        <td>{{ $invoices->unit->name }}</td>
                        <td>{{ $invoices->week }}</td>
                        <td>{{ date('d-m-Y', strtotime($invoices->weekend)) }}</td>
                        <td>{{ date('d-m-Y', strtotime($invoices->due_date)) }}</td>
                        <td>£{{ number_format($invoices->amount,2) }}</td>
                        <td>
                          @if($invoices->status == 0)
                            <span class="redFont">Unpaid</span>
                          @else
                            <span class="greenFont">Paid</span>
                          @endif
                        </td>
                        <td>
                          @php $filenames = explode('.',$invoices->file); @endphp
                          @if(end($filenames)=='pdf')
                           <a href="{{ $invoices->pdf_url }}" target="_blank" title="Download File" style="font-size:20px" >
                              <i class="fa fa-file-pdf-o" aria-hidden="true"></i>
                            </a>
                          @else
                            <a href="{{ $invoices->pdf_url }}" target="_blank" title="No file found" style="font-size:20px">
                              <i class="fa fa-file-pdf-o" aria-hidden="true"></i>
                            </a>
                          @endif
                          <a href="{{ Storage::disk('s3')->url('invoice_files/').$invoices->excel }}" target="_blank" title="Download File" style="font-size:20px" >
                              <i class="fa fa-file-excel-o" aria-hidden="true"></i>
                            </a>
                          {{-- <a href="{{ route('invoice.pdf.download', ['unitId' => $invoices->unit->clientUnitId, 'weekenddate' => 25]) }}" target="_blank" title="Generate PDF" style="font-size:22px">
                          <i class="fa fa-file"></i>
                          </a>  
                          <a href="{{ $invoices->pdf_url }}" target="_blank" title="Generate Excel" style="font-size:22px">
                          <i class="fa fa-file-excel-o"></i>
                          </a> --}}
                        </td>
                       
                      </tr> 
                       @endforeach
                    </tbody>
                  </table>
     
      </div>
      
      <div class="pgntn"></div>
    </div>
  </div>
</div>

  @endsection