  @extends('common.layout')
  @section('styles')
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <style>
      .bk_date {
          width: 12% !important
      }

      .bk_shift {
          width: 7% !important
      }

      .bk_cagry {
          width: 8% !important
      }

      .bk_staff {
          width: 16% !important
      }

      .bk_status {
          width: 13% !important
      }

      .bk_details {
          width: 15% !important
      }

      .select2-container--default .select2-selection--single {
          height: 36px;
          padding-top: 2px
      }

      .select2-container--default .select2-selection--single .select2-selection__rendered {
          color: #494949 !important;
          font-size: 12px
      }

      .select2-container--default .select2-selection--single .select2-selection__placeholder,
      .select2-results__option {
          font-size: 12px !important
      }

      .ui-datepicker select.ui-datepicker-month,
      .ui-datepicker select.ui-datepicker-year {
          width: 43%;
          float: left;
      }

      .modal-header {}

      .modal-header .close {
          color: #000000;
          margin-right: 0;
      }

      .modal-title {
          margin: 0;
          color: #494949;
      }



      .flexRow {
          overflow: hidden;
          width: 100%;
          padding-bottom: 20px;
      }

      .ScrollRow {
          width: 140%;
          display: flex
      }

      ::-webkit-scrollbar {
          width: 10px;
          height: 10px;
      }

      ::-webkit-scrollbar-track {
          background-color: #ffffff;
          -webkit-border-radius: 10px;
          border-radius: 10px;
      }

      ::-webkit-scrollbar-thumb {
          -webkit-border-radius: 10px;
          border-radius: 10px;
          background: #98c3f5;
      }

      .modal-header .close {
          color: #000000;
          margin-right: 0;
          position: relative;
          z-index: 10000;
      }
      .amend-button-amber {
          background-color: #efa603 !important;
          border-color: #eea236 !important;
          color: #fff !important;
      }

      @media (min-width: 768px) {
          .modal-dialog {
              width: 1200px;
              margin: 30px auto;
          }
      }
  </style>
  @endsection


  @section('content')
  @php use Carbon\Carbon; @endphp






  <div class="Container_Fluid_Main mt25">
      <div class="row headTitleBtm">
          <div class="col-md-12">
              <div class="GpBoxHeading no-bdr pb0">
                  <h4>
                      Pending Approvals
                  </h4>
              </div>
          </div>
      </div>
  </div>

  <div class="Container_Fluid_Main mt25">
      {{-- <div class="add_booking" cost-fetch="{{route('get.timings')}}" token="{{csrf_token()}}" categories="#"
      date="{{date('d-m-Y')}}" shifts="#" contacts="#"> --}}
      {{-- <div class="add_booking" cost-fetch="{{ route('get.timings') }}" token="{{ csrf_token() }}"
          categories="{{ $categories }}" date="{{ date('d-m-Y') }}" shifts="{{ $shifts }}"
          contacts="{{ auth()->user()->name }}">
          <div class="addBooksDivs">

              <div class="full_width">
                  <form action="{{ route('save') }}" method="POST" id="bookingForm">
                      @csrf
                      <input type="hidden" username="{{ $autharray['name'] }}" id="authdata"
                          value="{{ $autharray['usertype'] }}">
                      <div class="">

                          <div class="col-md-12 cl_bx_padd NewRowpAdd">

                              <div class="bx_margin">
                                  <p>Date</p>
                                  <input type="text" name="date[]" class="txt_bx datepicker datevalue" value="{{ date('d-m-Y') }}"
                                      readonly="readonly" required>
                              </div>

                              <div class="bx_margin">
                                  <p>Shift</p>
                                  <select name="shift[]" class="sl_box shiftvalue" required>
                                      <option value="">Select Shift</option>
                                      @foreach($shifts as $shift)
                                      @if(in_array($shift->shiftId, array(1,2,3,4)))
                                      <option @if(request('shift')==$shift->shiftId) selected="selected" @endif
                                          value="{{$shift->shiftId}}">{{$shift->name}}</option>
                                      @endif
                                      @endforeach
                                  </select>
                              </div>

                              <div class="bx_margin">
                                  <p>Staff Category</p>
                                  <select name="category[]" class="categorySwitch  sl_box" required>
                                      <option value="">Select Category</option>
                                      @foreach ($categories as $category)
                                      <option value="{{ $category->categoryId }}">{{ $category->name }}
                                      </option>
                                      @endforeach
                                  </select>
                              </div>

                              <div class="bx_margin">
                                  <div class="">
                                      <p>Numbers</p>
                                      <select name="numbers[]" class="sl_box">

                                          @for ($i = 1; $i < 11; $i++) <option value="{{ $i }}">{{ $i }}</option>
                                              @endfor
                                      </select>
                                  </div>
                              </div>

                              <div class="bx_margin">
                                  <p>Start</p>
                                  <input type="text" name="start_time[]" class="sl_box startTime clockpicker">
                              </div>


                              <div class="bx_margin">
                                  <p>End</p>
                                  <input type="text" name="end_time[]" class="sl_box endTime clockpicker">
                              </div>
                              <div class="bx_margin">
                                  <p>Requested By</p>
                                  <input type="text" value="{{ $autharray['name'] }}" name="requestedBy[]" class="sl_box"
                                      readonly />
                              </div>
                              <div class="bx_margin">
                                  <p>Reason</p>
                                  <select name="reasonBooking[]" class="sl_box">
                                      <option value="">Select</option>
                                      <option value="1">Staff Sickness</option>
                                      <option value="2">Holiday cover</option>
                                      <option value="3">Vacant Position</option>
                                      <option value="4">New Resident admission</option>
                                      <option value="5">1 to 1 care</option>
                                      <option value="6">Extra staff requirement</option>
                                      <option value="7">Staff training day</option>
                                  </select>
                              </div>



                              <div class="bx_margin">
                                  @if ($autharray['usertype'] == 'App\Models\Client')
                                  <div class="">

                                      <p>Select Unit</p>
                                      <select name="unitId[]" class="sl_box unitId">
                                          <option value="">Select Unit</option>
                                          @foreach ($units as $unit)
                                          <option value="{{ $unit->clientUnitId }}">{{ $unit->name }}
                                          </option>
                                          @endforeach
                                      </select>
                                  </div>
                                  @endif
                              </div>

                              <div class="bx_margin">
                                  <p>Additional Notes (if any)</p>
                                  <textarea type="text" name="impNotes[]" class="sl_box impNotes"> </textarea>
                              </div>
                              <div class="bx_margin BxUrgent">
                                  <label>
                                      <input type="checkbox" name="urgent[]" class="urgentCheckbox"  checked />
                                      UB
                                  </label>
                              </div>


                          </div>

                          <div class="col-md-4 text-right prsave pull-right" style="margin-bottom: 15px;">

                              <input style="float: right;" type="button" class="add_newrow btn-three"
                                  value="Add Another Shift" manager="">
                              <input style="float: right; margin-right: 7px;" type="button" url="{{ route('save') }}"
                                  token="{{ csrf_token() }}" class="add_save BtSave btn-primary" value="Save">
                          </div>

                      </div>
                  </form>
              </div>
          </div>
      </div> --}}


      <div style="float: left; width: 100%;" class="MainBoxRow">
          <div class="row">
              <div class="col-md-12">
                  @if (@auth()->user()->portal_type == 'App\Models\Client')
                  <form method="GET" action="{{ route('client.bookings') }}">
                      <div class="FiltBgOne">
                          <div class="row flexBook">
                                <!-- Booking ID Filter -->
                                <div class="col-md-2">
                                    <label for="bookingId">Booking ID</label>
                                    <input type="text" name="bookingId" id="bookingId" class="form-control" value="{{ request('bookingId') }}" placeholder="Enter Booking ID">
                                </div>
                                <!-- Unit Filter -->
                                <div class="col-md-2">
                                    <label for="unitId">Unit</label>
                                    <select name="unitId" id="unitId" class="form-control">
                                        <option value="">Select Unit</option>
                                        @foreach ($unitsdropdown as $unit)
                                        <option value="{{ $unit->clientUnitId }}"
                                            {{ request('unitId') == $unit->clientUnitId ? 'selected' : '' }}>
                                            {{ $unit->name }}
                                        </option>
                                        @endforeach
                                    </select>
                                </div>
                                <!-- Date Range Filter -->
                                <div class="col-md-2">
                                    <label for="dateRange">Date Range</label>
                                    <input type="text" name="dateRange" id="dateRange" class="form-control" value="{{ request('dateRange') }}" placeholder="Select Date Range">
                                </div>
                                <!-- Shift Filter -->
                                <div class="col-md-2">
                                    <label for="shiftId">Shift</label>
                                    <select name="shiftId" id="shiftId" class="form-control">
                                        <option value="">Select Shift</option>
                                        @foreach ($shifts as $shift)
                                            <option value="{{ $shift->shiftId }}" {{ request('shiftId') == $shift->shiftId ? 'selected' : '' }}>
                                                {{ $shift->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <!-- Category Filter -->
                                <div class="col-md-2">
                                    <label for="categoryId">Category</label>
                                    <select name="categoryId" id="categoryId" class="form-control">
                                        <option value="">Select Category</option>
                                        @foreach ($categories as $category)
                                            <option value="{{ $category->categoryId }}" {{ request('categoryId') == $category->categoryId ? 'selected' : '' }}>
                                                {{ $category->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <!-- Staff Filter -->
                                <div class="col-md-2">
                                    <label for="staffId">Staff</label>
                                    <select name="staffId" id="staffId" class="form-control select2">
                                        <option value="">Select Staff</option>
                                        @foreach ($staff as $person)
                                            <option value="{{ $person->staffId }}" {{ request('staffId') == $person->staffId ? 'selected' : '' }}>
                                                {{ $person->full_name }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <!-- Filter and Reset Buttons -->
                              <div class="col-md-2" style="margin-top: 22px;">
                                <button type="submit" class="btn btn-filter" style="float: left;margin-right: 4px;">
                                    <img src="{{ asset('unit-portal/images/filter.svg') }}" alt="Filter">
                                </button>
                                <button type="button" class="btn btn-reset" onclick="window.location.href='{{ route('client.bookings') }}'">
                                    <img src="{{ asset('unit-portal/images/reset.svg') }}" alt=“Reset”>
                                </button>
                              </div>
                          </div>
                      </div>
                  </form>
                  @endif
              </div>
              {{-- <div class="col-md-4">
                  <form action="" method="get">
                      <button class="btn btn-three pull-right" id="filtercancelled">
                          <input type="checkbox" class="chkBox" name="status" value="7" id="showAllShifts" style="transform: scale(1.2); 
                      transform-origin: center;
                      margin-right: 5px;position: relative;top: 1px;"
                              @if(request('status')==7) checked @endif> @if(request('status')==7) Hide cancelled @else Show all shifts @endif
                      </button>
                  </form>
              </div> --}}
          </div>

      </div>

  </div>







  <div class="scrolViewnsr Container_Fluid_Main">
      @foreach ($days as $day)
      <div class="shift_box mt20">
          <div class="shift_top">
              <p>
              <h5>{{ $day }}</h5> - Details of <span>{{ $bookings->where('entry_date', $day)->count() }}
                  Shifts</span> booked
              on this date as follows</p>

          </div>

          <div class="shift_bottom">
              <div class="shift_line">

                  <div class="bk_id " style="width: 93px !important;">Booking ID</div>
                  <div class="bk_id " style="width: 9% !important;">Units</div>
                  <div style="width: 85px !important;" class="bk_date ">Date</div>
                  <div style="width:70px !important;" class="bk_shift ">Shift</div>
                  <div style="width:78px !important;" class="bk_cagry ">Category</div>
                  <div class="bk_staff " style="width: 20% !important;">Staff</div>
                  <div style="width:60px !important;" class="bk_cagry ">Profile</div>
                  @if(Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
                  Auth::user()->can('client_portal_booking_amend_shift'))
                  <div class="bk_amnd " style="width: 70px !important;">Amend</div>
                  @endif
                  <div class="bk_amnd " style="width: 45px !important;">Log</div>
                  <div class="bk_status " style="width: 17% !important;">Status</div>
                  <div style="width:135px !important;" class="bk_details ">Details</div>
              </div>
              <ul>
                  @foreach ($bookings->where('entry_date', $day)->all() as $key => $booking)


                  <li>

                      <div class="bk_id " style="width: 93px !important;">{{ $booking->bookingId }}</div>
                      <div class="bk_id" style="width: 9% !important;">{{ $booking->unit->name }}</div>
                      <div style="width:85px !important;" class="bk_date ">
                          {{ date('d-M-Y D', strtotime($booking->date)) }}
                      </div>
                      <div style="width:70px !important;" class="bk_shift ">{{ $booking->shift->name }}</div>
                      <div style="width:78px !important;" class="bk_cagry ">
                          {{ $booking->category->name }}
                      </div>
                      <div style="width: 20% !important;" class=" bk_staff ">
                          @if ($booking->staffId != null)
                          <img src="https://nursesgroup-crm.s3.eu-west-2.amazonaws.com/staff/photo/{{$booking->staff->photo}}"
                              class="thmbImg">
                          <span>{{ $booking->staff->full_name }}</span>
                          @else
                          @if ($booking->unitStatus == 2)
                          <span class="redFont">Cancelled</span>
                          @else
                          <i class="fa fa-search idSeachStaf" aria-hidden="true"></i>
                          @endif
                          @endif
                      </div>

                      <div style="width:60px !important;text-align: center;" class="bk_cagry">
                          {!! $booking->profile !!}
                      </div>
                      @if(Auth::user()->can('unit_portal_new_shift_request_amend_shift') ||
                      Auth::user()->can('client_portal_booking_amend_shift'))
                      <div class="@if($booking->unitAmend==1) bk_amnded_amber @else bk_amnd @endif 
                         @if ($booking->unitStatus == 2) bk_amnded @endif"
                          style="width: 70px !important;text-align: center;">
                          <button bookid="{{ $booking->bookingId }}" token="{{ csrf_token() }}"
                              {{-- fetch="{{ route('get.booking') }}" class="view amendNow" --}}
                              fetch="{{ route('get.booking') }}" class="view amendNow @if($booking->unitAmend==1) amend-button-amber @endif"
                              timeDiff="{{$booking->totalhoursto}}" totalhrspend="{{round($booking->totalhoursto)}}"
                              imNotes="{{ $booking->importantNotes }}">
                              <i style="position: relative;top: -2px;" class="fa fa-wrench" aria-hidden="true"></i>
                          </button>

                      </div>
                      @endif
                      <div class="bk_cagry " style="width:45px !important;">
                          <a class="{{ $booking->chats->where('color', 1)->count() > 0 ? 'log_button warngBtn' : 'log_button' }}"
                              booking_id="{{ $booking->bookingId }}" unit_id="{{ $booking->unitId }}"
                              staff="{{ $booking->staff->full_name }}" unit="{{ $booking->unit->alias }}"
                              category="{{ $booking->category->name }}" shift="{{ $booking->shift->name }}"
                              date="{{ date('d-M-Y, D',strtotime($booking->date)) }}"
                              start="{{  date('h:i',strtotime($booking->start_time)) }}"
                              end="{{  date('h:i',strtotime($booking->end_time)) }}">
                             <i style="position: relative;top: -1px;" class="fa fa-clock-o" aria-hidden="true"></i>
                          </a>
                      </div>
                      <div class="bk_status" style="width: 17% !important;">
                          @switch ($booking->unitStatus)

                          @case(2)
                          <a href="#" class="cancel">Cancelled</a>
                          @break

                          @case(4)
                          @php

                          $endDate = $booking->date; // format: Y-m-d
                          $endTime = $booking->end_time; // format: H:i:s (24-hour)
                          $endDateTime = Carbon::createFromFormat('Y-m-d H:i:s', $endDate . ' ' . $endTime);
                          @endphp
                          @if($booking->staffStatus==3 && $endDateTime->isPast())
                          @if($booking->timesheet->image_url)
                          @if($booking->timesheet->status==2)
                          <a href="#" class="confrimd">TS Approved</a>
                          @else
                          <a href="#" class="confrimd">TS Uploaded</a>
                          @endif
                          @else
                          <a href="#" class="confrimd">Completed</a>
                          @endif

                          @else
                          <a href="#" class="confrimd">Confirmed</a>
                          @endif
                          @break

                          @case(1)
                          <a href="#" class="awaiting">Awaiting Approval</a>
                          @break
                          @endswitch
                          @if ( $booking->bookedby && $booking->bookingWorkflows()->whereNotNull('workflow')->first())
                          <a href="#" class="BtProgress" data-toggle="modal" data-target="#ProgressModal{{ $key }}">
                              <i class="fa fa-info-circle" aria-hidden="true"></i></a>

                          @include('common.bookings.workflowStatus')
                          @endif
                      </div>

                      <div style="width:135px !important;" class="bk_details">
                          {!! $booking->details !!}
                      </div>
                  </li>
                  @endforeach
              </ul>
          </div>
      </div>
      @endforeach

  </div>
  @include('unitPortal.bookingAmendModal')
  @include('unitPortal.newShiftModel')
  @include('unitPortal.bookingAmendConfirmationModal')
  @include('unitPortal.bookingUnitLogBook')
  <div class="modal" id="unableToCancelModal">
      <div class="modal-dialog" style="width: 56%;height:115%;">
          <!-- Modal content-->
          <div class="modal-content">
              <div class="modal-header header_txt">
                  <h4 class="modal-title"><b>Booking Amend</b> <button type="button" class="close"
                          data-dismiss="modal">×</button> </h4>
              </div>
              <div class="modal-body">
                  <div class="row">
                      <div class="col-sm-12 bookDataHtml">
                      </div>

                      <div class="col-sm-12">
                          <div class="amd_fullwidth">
                              <h4 class="amdOK">The shift starts in less than 24 hours, Please contact our office, <span
                                      class="hil_cl">01935 315031</span> to make any changes.</h4>
                          </div>
                      </div>
                      <div class="col-sm-12">
                          <div class="text-right mb20 m-t-10 amendActionButtons">
                              <button type="button" class="amdbtn_green amendClose btn-primary">OK</button>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>
  <div id="modal_new_requestby" class="modal">
      <!-- Modal content -->
      <div class="modal_changepass">
          <div class="modal-header">
              <span class="close closeBtnModal">&times;</span>
              <h2>Add New Contact</h2>
          </div>
          <div class="modal-body">
              <div class="inlineBlock">
                  <p>Full Name</p>
                  <input type="text" name="NewCnt_name" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Position</p>
                  <input type="text" name="NewCnt_position" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Phone</p>
                  <input type="text" name="NewCnt_phone" class="txt_bx_modal">
              </div>
              <div class="inlineBlock">
                  <p>Email</p>
                  <input type="text" name="NewCnt_email" class="txt_bx_modal">
              </div>
          </div>
          <div class="modal-footer">
              <input type="button" class="add_save_modal amdbtn_green" value="Save" action="#"
                  token="{{ csrf_token() }}">
              <input type="button" token="" class="amdbtn_red add_save_modal_close closeBtnModal" value="Close">
          </div>
      </div>
  </div>



  @endsection
  @section('styles')
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.css">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.min.css" />
  <link rel="stylesheet" href="https://code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
  @endsection
  @section('scripts')
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.7.1/jquery.min.js"></script>
  <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/daterangepicker/daterangepicker.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/clockpicker/0.0.7/bootstrap-clockpicker.min.js"></script> <!-- ✅ Add this before bookings.js -->
  <script src="{{ asset('unit-portal/js/bookings.js') }}"></script>

  <script>
      $(document).ready(function() {
          $("body").on("click", ".workflow-approve-btn", function() {
              var bookingId = $(this).data('id');
              var action = $(this).data('value');
              var token = $('meta[name="csrf-token"]').attr('content');
              if (!action) {
                  var note = prompt('Add Comments');
              }
              $.ajax({
                  url: "{{ route('workflows.approve') }}",
                  type: "POST",
                  data: {
                      id: bookingId,
                      _token: token,
                      note: note,
                      action: action
                  },
                  success: function(response) {
                      Swal.fire(
                          'Success',
                          'Approval status updated successfully.',
                          'success'
                      ).then(function() {
                          location.reload();
                      });


                  },
                  error: function(xhr, status, error) {
                      Swal.fire(
                          'Error',
                          xhr.responseJSON.message,
                          'error'
                      ).then(function() {
                          location.reload();
                      });
                  }
              });
          });
      });
        $(document).ready(function() {
                $('#staffId').select2({
                    placeholder: "Select a staff member",
                    allowClear: true
                });
            });
            // date range filter
            $(document).ready(function() {
            $('#dateRange').daterangepicker({
                locale: {
                    format: 'DD-MM-YYYY'
                },
                opens: 'left',
                autoUpdateInput: false,  // don't fill the input by default
                placeholder: "Please select date", // note: daterangepicker itself does not support placeholder, use HTML attribute
                ranges: {
                    'Last 2 Months': [moment().subtract(2, 'months').startOf('month'), moment().subtract(1, 'months').endOf('month')],
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                }
            });

            $('#dateRange').on('apply.daterangepicker', function(ev, picker) {
                $(this).val(picker.startDate.format('DD-MM-YYYY') + ' - ' + picker.endDate.format('DD-MM-YYYY'));
            });

            $('#dateRange').on('cancel.daterangepicker', function(ev, picker) {
                $(this).val('');
            });
        });
  </script>
@endsection