/************ modal.css ***************/

.bk_date {
    width: 12% !important;
}

.bk_shift {
    width: 7% !important;
}

.bk_cagry {
    width: 7% !important;
}

.bk_staff {
    width: 16% !important;
}

.bk_status {
    width: 13% !important;
}

.bk_details {
    width: 10% !important;
}

.BtnWidth {
    width: 70px !important;
}

.BkCat {
    width: 7% !important;
}

.warngBtn {
    /* background: #fff !important; */
    background: #efa603 !important;

}

.modal-body {
    padding: 2px 16px;
    background: #f4f4f4;
}

.BkStafN {
    width: 19% !important;
}


/* Add Animation */

@-webkit-keyframes slideDown {
    from {
        top: -300px;
        opacity: 0
    }

    to {
        top: 200px;
        opacity: 1
    }
}

@keyframes slideDown {
    from {
        top: -300px;
        opacity: 0
    }

    to {
        top: 200px;
        opacity: 1
    }
}

@-webkit-keyframes fadeDown {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

@keyframes fadeDown {
    from {
        opacity: 0
    }

    to {
        opacity: 1
    }
}

body {
    font-family: "Nunito", sans-serif;

}


/* The Modal (background) */

.modal {
    display: none;
    /* Hidden by default */
    position: fixed;
    /* Stay in place */
    z-index: 1;
    /* Sit on top */
    padding-top: 100px;
    /* Location of the box */
    left: 0;
    top: 0;
    width: 100%;
    /* Full width */
    height: 100%;
    /* Full height */
    overflow: auto;
    /* Enable scroll if needed */
    background-color: rgb(0, 0, 0);
    /* Fallback color */
    background-color: rgba(0, 0, 0, 0.4);
    /* Black w/ opacity */
}


/* Modal Content */

.modal_changepass {
    background-color: #fefefe;
    margin: auto;
    padding: 20px 30px;
    border: 1px solid #888;
    width: 740px;
}


/* The Close Button */

.close {
    color: #aaaaaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.inp_bg {
    margin-bottom: 11px;
}

.close:hover,
.close:focus {
    color: #000;
    text-decoration: none;
    cursor: pointer;
}

.crnp {
    font-size: 14px;
    margin-bottom: 2px;
}

.chngpass {
    font-weight: 600;
    font-size: 25px;
}
.ModalShiftLog {
    width: 60%;
    margin: 30px auto;
    background: #fff;
}

.history-tl-container {
    margin: auto;
    display: block;
    position: relative
}

.history-tl-container ul.tl {
    margin: 7px 0;
    padding: 0;
    height: 380px;
    display: inline-block;
    width: 100%;
}

.history-tl-container ul.tl li {
    list-style: none;
    margin: auto;
    font-size: 15px;
    margin-left: 30px;
    min-height: 62px;
    border-left: 1px dashed #bdbdbd;
    padding: 23px 10px 0px 25px;
    position: relative;
    border-bottom: 1px dashed #bdbdbd;
}

.item-detail i {
    color: rgba(0, 0, 0, 0.5) !important;
    font-size: 12px;
}

.history-tl-container ul.tl li:last-child {
    border-bottom: 0;
}

.history-tl-container ul.tl li::before {
    position: absolute;
    left: -18px;
    top: 13px;
    content: " ";
    border: 4px solid rgba(255, 255, 255, 0.74);
    border-radius: 500%;
    background: #494949;
    height: 34px;
    width: 34px;
    transition: all 500ms ease-in-out;
}

.history-tl-container ul.tl li:hover::before {
    border-color: #258CC7;
    transition: all 1000ms ease-in-out;
}

ul.tl li .item-title {}

ul.tl li .item-detail {
    color: rgba(0, 0, 0, 0.5);
    font-size: 11px;
    float: right;
    position: relative;
    top: -6px;
}

ul.tl li .timestamp {
    position: absolute;
    left: -8px;
    text-align: right;
    font-size: 12px;
    top: 22px;
}

.timestamp i {
    color: #fff !important;
    font-size: 14px;
}

.LuftTitle {
    color: #494949;
}

.NsgOrange {
    color: #f36b22;
}

.NsProfName {
    color: #1073c3;
    font-weight: 700;
}

.TopHeadTxt {
    color: #494949;
    font-size: 22px;
    font-weight: 700;
    letter-spacing: -0.3px;
}

.TopHeadTxt span {
    color: #494949;
    font-size: 22px;
    font-weight: 700;
    letter-spacing: -0.3px;
}

.ShiftLogHeder {
    background: #efefef !important;
    color: #fff;
    padding: 30px;
}

.ShiftLogClose {
    top: -20px;
    margin-top: 0 !important;
    position: relative;
    right: -30px;
    font-weight: 500;
    color: #8e8e8e !important;
}

.ShiftLogClose:hover {
    color: #494949 !important;
}

.BtnLogNow {
    background: #4d48a7;
    color: #fff;
    border-radius: 20px;
    font-size: 12px;
    padding: 6px 35px;
    position: relative;
    top: 22px;
}

.BtnLogNow:hover {
    background: #242075;
    color: #fff;
}

.mt25 {
    margin-top: 25px;
}

.entrlab {
    font-size: 14px;
    margin-bottom: 5px;
    font-weight: 600;
    color: #383838;
}

#fixed-form-container {
    position: fixed;
    bottom: 0px;
    right: 0%;
    display: none;
    width: 400px;
    text-align: center;
    margin: 0;
}

#fixed-form-container .button {
    font-size: 1.1em;
    cursor: pointer;
    float: right;
    margin-top: -30px;
    border: 2px solid #605ca8;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    border-radius: 5px 5px 0px 0px;
    padding: 5px 20px 5px 20px;
    background-color: #605ca8;
    color: #fff;
    display: inline-block;
    text-align: center;
    text-decoration: none;
    -webkit-box-shadow: 4px 0px 5px 0px rgb(0 0 0 / 30%);
    -moz-box-shadow: 4px 0px 5px 0px rgba(0, 0, 0, 0.3);
    box-shadow: 4px 0px 5px 0px rgb(0 0 0 / 30%);
}

#fixed-form-container .body {
    background-color: #fff;
    border-radius: 5px;
    border: 2px solid #605ca8;
    margin-bottom: 2px;
    text-align: left;
    padding: 20px;
    -webkit-box-shadow: 4px 4px 5px 0px rgba(0, 0, 0, 0.3);
    -moz-box-shadow: 4px 4px 5px 0px rgba(0, 0, 0, 0.3);
    box-shadow: 4px 4px 5px 0px rgba(0, 0, 0, 0.3);
}

.inntxt {
    margin: 0;
    color: #1073c3 !important;
    font-size: 16px;
    font-weight: 600;
}

.luftp {
    margin-bottom: 0;
    line-height: 16px;
    font-size: 12px;
    margin-top: 5px;
}

.luftbkID {
    margin-bottom: 0;
    color: #605ca8;
    margin-top: 3px;
}

/*************************** select2.min.css *******************************/

.select2-container {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle
}

.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-selection--single .select2-selection__clear {
    position: relative
}

.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 20px
}

.select2-container .select2-selection--multiple {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    user-select: none;
    -webkit-user-select: none
}

.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-left: 8px;
    text-overflow: ellipsis;
    white-space: nowrap
}

.select2-container .select2-search--inline {
    float: left
}

.select2-container .select2-search--inline .select2-search__field {
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
    padding: 0
}

.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-dropdown {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    box-sizing: border-box;
    display: block;
    position: absolute;
    left: -100000px;
    width: 100%;
    z-index: 1051
}

.select2-results {
    display: block
}

.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0
}

.select2-results__option {
    padding: 6px;
    user-select: none;
    -webkit-user-select: none
}

.select2-results__option[aria-selected] {
    cursor: pointer
}

.select2-container--open .select2-dropdown {
    left: 0
}

.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-search--dropdown {
    display: block;
    padding: 4px
}

.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    box-sizing: border-box
}

.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none
}

.select2-search--dropdown.select2-search--hide {
    display: none
}

.select2-close-mask {
    border: 0;
    margin: 0;
    padding: 0;
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    min-height: 100%;
    min-width: 100%;
    height: auto;
    width: auto;
    opacity: 0;
    z-index: 99;
    background-color: #fff;
    filter: alpha(opacity=0)
}

.select2-hidden-accessible {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    -webkit-clip-path: inset(50%) !important;
    clip-path: inset(50%) !important;
    height: 1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    white-space: nowrap !important
}

.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: none;
    border-radius: 4px
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px
}

.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold
}

.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #494949
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left
}

.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto
}

.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: #eee;
    cursor: default
}

.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
    display: none
}

.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px
}

.select2-container--default .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%
}

.select2-container--default .select2-selection--multiple .select2-selection__rendered li {
    list-style: none
}

.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #999;
    margin-top: 5px;
    float: left
}

.select2-container--default .select2-selection--multiple .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-top: 5px;
    margin-right: 10px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #999;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px
}

.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #333
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice,
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder,
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
    float: right
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto
}

.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto
}

.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid black 1px;
    outline: 0
}

.select2-container--default.select2-container--disabled .select2-selection--multiple {
    background-color: #eee;
    cursor: default
}

.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
    display: none
}

.select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa
}

.select2-container--default .select2-search--inline .select2-search__field {
    background: transparent;
    border: none;
    outline: 0;
    box-shadow: none;
    -webkit-appearance: textfield
}

.select2-container--default .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto
}

.select2-container--default .select2-results__option[role=group] {
    padding: 0
}

.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #999
}

.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #ddd
}

.select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -1em;
    padding-left: 2em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -2em;
    padding-left: 3em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -3em;
    padding-left: 4em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -4em;
    padding-left: 5em
}

.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -5em;
    padding-left: 6em
}

.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #5897fb;
    color: white
}

.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px
}

.select2-container--classic .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #aaa;
    border-radius: 4px;
    outline: 0;
    background-image: -webkit-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: -o-linear-gradient(top, #fff 50%, #eee 100%);
    background-image: linear-gradient(to bottom, #fff 50%, #eee 100%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0)
}

.select2-container--classic .select2-selection--single:focus {
    border: 1px solid #5897fb
}

.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px
}

.select2-container--classic .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: 10px
}

.select2-container--classic .select2-selection--single .select2-selection__placeholder {
    color: #999
}

.select2-container--classic .select2-selection--single .select2-selection__arrow {
    background-color: #ddd;
    border: none;
    border-left: 1px solid #aaa;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
    background-image: -webkit-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #ccc 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #ccc 100%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFCCCCCC', GradientType=0)
}

.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0
}

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left
}

.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    border: none;
    border-right: 1px solid #aaa;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    left: 1px;
    right: auto
}

.select2-container--classic.select2-container--open .select2-selection--single {
    border: 1px solid #5897fb
}

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
    background: transparent;
    border: none
}

.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px
}

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #fff 0%, #eee 50%);
    background-image: -o-linear-gradient(top, #fff 0%, #eee 50%);
    background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFFFFFFF', endColorstr='#FFEEEEEE', GradientType=0)
}

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: -o-linear-gradient(top, #eee 50%, #fff 100%);
    background-image: linear-gradient(to bottom, #eee 50%, #fff 100%);
    background-repeat: repeat-x;
    filter: progid: DXImageTransform.Microsoft.gradient(startColorstr='#FFEEEEEE', endColorstr='#FFFFFFFF', GradientType=0)
}

.select2-container--classic .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
    outline: 0
}

.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid #5897fb
}

.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
    list-style: none;
    margin: 0;
    padding: 0 5px
}

.select2-container--classic .select2-selection--multiple .select2-selection__clear {
    display: none
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
    color: #888;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px
}

.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #555
}

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    float: right
}

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto
}

.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto
}

.select2-container--classic.select2-container--open .select2-selection--multiple {
    border: 1px solid #5897fb
}

.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0
}

.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0
}

.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    outline: 0
}

.select2-container--classic .select2-search--inline .select2-search__field {
    outline: 0;
    box-shadow: none
}

.select2-container--classic .select2-dropdown {
    background-color: #fff;
    border: 1px solid transparent
}

.select2-container--classic .select2-dropdown--above {
    border-bottom: none
}

.select2-container--classic .select2-dropdown--below {
    border-top: none
}

.select2-container--classic .select2-results>.select2-results__options {
    max-height: 200px;
    overflow-y: auto
}

.select2-container--classic .select2-results__option[role=group] {
    padding: 0
}

.select2-container--classic .select2-results__option[aria-disabled=true] {
    color: grey
}

.select2-container--classic .select2-results__option--highlighted[aria-selected] {
    background-color: #3875d7;
    color: #fff
}

.select2-container--classic .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px
}

.select2-container--classic.select2-container--open .select2-dropdown {
    border-color: #5897fb
}

/***************** sweetalert2.min.css *********************/

@charset "UTF-8";

@-webkit-keyframes swal2-show {
    0% {
        transform: scale(.7)
    }

    45% {
        transform: scale(1.05)
    }

    80% {
        transform: scale(.95)
    }

    100% {
        transform: scale(1)
    }
}

@keyframes swal2-show {
    0% {
        transform: scale(.7)
    }

    45% {
        transform: scale(1.05)
    }

    80% {
        transform: scale(.95)
    }

    100% {
        transform: scale(1)
    }
}

@-webkit-keyframes swal2-hide {
    0% {
        transform: scale(1);
        opacity: 1
    }

    100% {
        transform: scale(.5);
        opacity: 0
    }
}

@keyframes swal2-hide {
    0% {
        transform: scale(1);
        opacity: 1
    }

    100% {
        transform: scale(.5);
        opacity: 0
    }
}

@-webkit-keyframes swal2-animate-success-line-tip {
    0% {
        top: 1.1875em;
        left: .0625em;
        width: 0
    }

    54% {
        top: 1.0625em;
        left: .125em;
        width: 0
    }

    70% {
        top: 2.1875em;
        left: -.375em;
        width: 3.125em
    }

    84% {
        top: 3em;
        left: 1.3125em;
        width: 1.0625em
    }

    100% {
        top: 2.8125em;
        left: .875em;
        width: 1.5625em
    }
}

@keyframes swal2-animate-success-line-tip {
    0% {
        top: 1.1875em;
        left: .0625em;
        width: 0
    }

    54% {
        top: 1.0625em;
        left: .125em;
        width: 0
    }

    70% {
        top: 2.1875em;
        left: -.375em;
        width: 3.125em
    }

    84% {
        top: 3em;
        left: 1.3125em;
        width: 1.0625em
    }

    100% {
        top: 2.8125em;
        left: .875em;
        width: 1.5625em
    }
}

@-webkit-keyframes swal2-animate-success-line-long {
    0% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    65% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    84% {
        top: 2.1875em;
        right: 0;
        width: 3.4375em
    }

    100% {
        top: 2.375em;
        right: .5em;
        width: 2.9375em
    }
}

@keyframes swal2-animate-success-line-long {
    0% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    65% {
        top: 3.375em;
        right: 2.875em;
        width: 0
    }

    84% {
        top: 2.1875em;
        right: 0;
        width: 3.4375em
    }

    100% {
        top: 2.375em;
        right: .5em;
        width: 2.9375em
    }
}

@-webkit-keyframes swal2-rotate-success-circular-line {
    0% {
        transform: rotate(-45deg)
    }

    5% {
        transform: rotate(-45deg)
    }

    12% {
        transform: rotate(-405deg)
    }

    100% {
        transform: rotate(-405deg)
    }
}

@keyframes swal2-rotate-success-circular-line {
    0% {
        transform: rotate(-45deg)
    }

    5% {
        transform: rotate(-45deg)
    }

    12% {
        transform: rotate(-405deg)
    }

    100% {
        transform: rotate(-405deg)
    }
}

@-webkit-keyframes swal2-animate-error-x-mark {
    0% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    50% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    80% {
        margin-top: -.375em;
        transform: scale(1.15)
    }

    100% {
        margin-top: 0;
        transform: scale(1);
        opacity: 1
    }
}

@keyframes swal2-animate-error-x-mark {
    0% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    50% {
        margin-top: 1.625em;
        transform: scale(.4);
        opacity: 0
    }

    80% {
        margin-top: -.375em;
        transform: scale(1.15)
    }

    100% {
        margin-top: 0;
        transform: scale(1);
        opacity: 1
    }
}

@-webkit-keyframes swal2-animate-error-icon {
    0% {
        transform: rotateX(100deg);
        opacity: 0
    }

    100% {
        transform: rotateX(0);
        opacity: 1
    }
}

@keyframes swal2-animate-error-icon {
    0% {
        transform: rotateX(100deg);
        opacity: 0
    }

    100% {
        transform: rotateX(0);
        opacity: 1
    }
}

body.swal2-toast-shown .swal2-container {
    background-color: transparent
}

body.swal2-toast-shown .swal2-container.swal2-shown {
    background-color: transparent
}

body.swal2-toast-shown .swal2-container.swal2-top {
    top: 0;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-top-end,
body.swal2-toast-shown .swal2-container.swal2-top-right {
    top: 0;
    right: 0;
    bottom: auto;
    left: auto
}

body.swal2-toast-shown .swal2-container.swal2-top-left,
body.swal2-toast-shown .swal2-container.swal2-top-start {
    top: 0;
    right: auto;
    bottom: auto;
    left: 0
}

body.swal2-toast-shown .swal2-container.swal2-center-left,
body.swal2-toast-shown .swal2-container.swal2-center-start {
    top: 50%;
    right: auto;
    bottom: auto;
    left: 0;
    transform: translateY(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-center {
    top: 50%;
    right: auto;
    bottom: auto;
    left: 50%;
    transform: translate(-50%, -50%)
}

body.swal2-toast-shown .swal2-container.swal2-center-end,
body.swal2-toast-shown .swal2-container.swal2-center-right {
    top: 50%;
    right: 0;
    bottom: auto;
    left: auto;
    transform: translateY(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-bottom-left,
body.swal2-toast-shown .swal2-container.swal2-bottom-start {
    top: auto;
    right: auto;
    bottom: 0;
    left: 0
}

body.swal2-toast-shown .swal2-container.swal2-bottom {
    top: auto;
    right: auto;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-toast-shown .swal2-container.swal2-bottom-end,
body.swal2-toast-shown .swal2-container.swal2-bottom-right {
    top: auto;
    right: 0;
    bottom: 0;
    left: auto
}

body.swal2-toast-column .swal2-toast {
    flex-direction: column;
    align-items: stretch
}

body.swal2-toast-column .swal2-toast .swal2-actions {
    flex: 1;
    align-self: stretch;
    height: 2.2em;
    margin-top: .3125em
}

body.swal2-toast-column .swal2-toast .swal2-loading {
    justify-content: center
}

body.swal2-toast-column .swal2-toast .swal2-input {
    height: 2em;
    margin: .3125em auto;
    font-size: 1em
}

body.swal2-toast-column .swal2-toast .swal2-validation-message {
    font-size: 1em
}

.swal2-popup.swal2-toast {
    flex-direction: row;
    align-items: center;
    width: auto;
    padding: .625em;
    overflow-y: hidden;
    box-shadow: 0 0 .625em #d9d9d9
}

.swal2-popup.swal2-toast .swal2-header {
    flex-direction: row
}

.swal2-popup.swal2-toast .swal2-title {
    flex-grow: 1;
    justify-content: flex-start;
    margin: 0 .6em;
    font-size: 1em
}

.swal2-popup.swal2-toast .swal2-footer {
    margin: .5em 0 0;
    padding: .5em 0 0;
    font-size: .8em
}

.swal2-popup.swal2-toast .swal2-close {
    position: static;
    width: .8em;
    height: .8em;
    line-height: .8
}

.swal2-popup.swal2-toast .swal2-content {
    justify-content: flex-start;
    font-size: 1em
}

.swal2-popup.swal2-toast .swal2-icon {
    width: 2em;
    min-width: 2em;
    height: 2em;
    margin: 0
}

.swal2-popup.swal2-toast .swal2-icon::before {
    display: flex;
    align-items: center;
    font-size: 2em;
    font-weight: 700
}

@media all and (-ms-high-contrast:none),
(-ms-high-contrast:active) {
    .swal2-popup.swal2-toast .swal2-icon::before {
        font-size: .25em
    }
}

.swal2-popup.swal2-toast .swal2-icon.swal2-success .swal2-success-ring {
    width: 2em;
    height: 2em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    top: .875em;
    width: 1.375em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: .3125em
}

.swal2-popup.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: .3125em
}

.swal2-popup.swal2-toast .swal2-actions {
    flex-basis: auto !important;
    width: auto;
    height: auto;
    margin: 0 .3125em
}

.swal2-popup.swal2-toast .swal2-styled {
    margin: 0 .3125em;
    padding: .3125em .625em;
    font-size: 1em
}

.swal2-popup.swal2-toast .swal2-styled:focus {
    box-shadow: 0 0 0 .0625em #fff, 0 0 0 .125em rgba(50, 100, 150, .4)
}

.swal2-popup.swal2-toast .swal2-success {
    border-color: #a5dc86
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line] {
    position: absolute;
    width: 1.6em;
    height: 3em;
    transform: rotate(45deg);
    border-radius: 50%
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left] {
    top: -.8em;
    left: -.5em;
    transform: rotate(-45deg);
    transform-origin: 2em 2em;
    border-radius: 4em 0 0 4em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right] {
    top: -.25em;
    left: .9375em;
    transform-origin: 0 1.5em;
    border-radius: 0 4em 4em 0
}

.swal2-popup.swal2-toast .swal2-success .swal2-success-ring {
    width: 2em;
    height: 2em
}

.swal2-popup.swal2-toast .swal2-success .swal2-success-fix {
    top: 0;
    left: .4375em;
    width: .4375em;
    height: 2.6875em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line] {
    height: .3125em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip] {
    top: 1.125em;
    left: .1875em;
    width: .75em
}

.swal2-popup.swal2-toast .swal2-success [class^=swal2-success-line][class$=long] {
    top: .9375em;
    right: .1875em;
    width: 1.375em
}

.swal2-popup.swal2-toast.swal2-show {
    -webkit-animation: swal2-toast-show .5s;
    animation: swal2-toast-show .5s
}

.swal2-popup.swal2-toast.swal2-hide {
    -webkit-animation: swal2-toast-hide .1s forwards;
    animation: swal2-toast-hide .1s forwards
}

.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-tip {
    -webkit-animation: swal2-toast-animate-success-line-tip .75s;
    animation: swal2-toast-animate-success-line-tip .75s
}

.swal2-popup.swal2-toast .swal2-animate-success-icon .swal2-success-line-long {
    -webkit-animation: swal2-toast-animate-success-line-long .75s;
    animation: swal2-toast-animate-success-line-long .75s
}

@-webkit-keyframes swal2-toast-show {
    0% {
        transform: translateY(-.625em) rotateZ(2deg)
    }

    33% {
        transform: translateY(0) rotateZ(-2deg)
    }

    66% {
        transform: translateY(.3125em) rotateZ(2deg)
    }

    100% {
        transform: translateY(0) rotateZ(0)
    }
}

@keyframes swal2-toast-show {
    0% {
        transform: translateY(-.625em) rotateZ(2deg)
    }

    33% {
        transform: translateY(0) rotateZ(-2deg)
    }

    66% {
        transform: translateY(.3125em) rotateZ(2deg)
    }

    100% {
        transform: translateY(0) rotateZ(0)
    }
}

@-webkit-keyframes swal2-toast-hide {
    100% {
        transform: rotateZ(1deg);
        opacity: 0
    }
}

@keyframes swal2-toast-hide {
    100% {
        transform: rotateZ(1deg);
        opacity: 0
    }
}

@-webkit-keyframes swal2-toast-animate-success-line-tip {
    0% {
        top: .5625em;
        left: .0625em;
        width: 0
    }

    54% {
        top: .125em;
        left: .125em;
        width: 0
    }

    70% {
        top: .625em;
        left: -.25em;
        width: 1.625em
    }

    84% {
        top: 1.0625em;
        left: .75em;
        width: .5em
    }

    100% {
        top: 1.125em;
        left: .1875em;
        width: .75em
    }
}

@keyframes swal2-toast-animate-success-line-tip {
    0% {
        top: .5625em;
        left: .0625em;
        width: 0
    }

    54% {
        top: .125em;
        left: .125em;
        width: 0
    }

    70% {
        top: .625em;
        left: -.25em;
        width: 1.625em
    }

    84% {
        top: 1.0625em;
        left: .75em;
        width: .5em
    }

    100% {
        top: 1.125em;
        left: .1875em;
        width: .75em
    }
}

@-webkit-keyframes swal2-toast-animate-success-line-long {
    0% {
        top: 1.625em;
        right: 1.375em;
        width: 0
    }

    65% {
        top: 1.25em;
        right: .9375em;
        width: 0
    }

    84% {
        top: .9375em;
        right: 0;
        width: 1.125em
    }

    100% {
        top: .9375em;
        right: .1875em;
        width: 1.375em
    }
}

@keyframes swal2-toast-animate-success-line-long {
    0% {
        top: 1.625em;
        right: 1.375em;
        width: 0
    }

    65% {
        top: 1.25em;
        right: .9375em;
        width: 0
    }

    84% {
        top: .9375em;
        right: 0;
        width: 1.125em
    }

    100% {
        top: .9375em;
        right: .1875em;
        width: 1.375em
    }
}

body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
    overflow: hidden
}

body.swal2-height-auto {
    height: auto !important
}

body.swal2-no-backdrop .swal2-shown {
    top: auto;
    right: auto;
    bottom: auto;
    left: auto;
    max-width: calc(100% - .625em * 2);
    background-color: transparent
}

body.swal2-no-backdrop .swal2-shown>.swal2-modal {
    box-shadow: 0 0 10px rgba(0, 0, 0, .4)
}

body.swal2-no-backdrop .swal2-shown.swal2-top {
    top: 0;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-no-backdrop .swal2-shown.swal2-top-left,
body.swal2-no-backdrop .swal2-shown.swal2-top-start {
    top: 0;
    left: 0
}

body.swal2-no-backdrop .swal2-shown.swal2-top-end,
body.swal2-no-backdrop .swal2-shown.swal2-top-right {
    top: 0;
    right: 0
}

body.swal2-no-backdrop .swal2-shown.swal2-center {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%)
}

body.swal2-no-backdrop .swal2-shown.swal2-center-left,
body.swal2-no-backdrop .swal2-shown.swal2-center-start {
    top: 50%;
    left: 0;
    transform: translateY(-50%)
}

body.swal2-no-backdrop .swal2-shown.swal2-center-end,
body.swal2-no-backdrop .swal2-shown.swal2-center-right {
    top: 50%;
    right: 0;
    transform: translateY(-50%)
}

body.swal2-no-backdrop .swal2-shown.swal2-bottom {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%)
}

body.swal2-no-backdrop .swal2-shown.swal2-bottom-left,
body.swal2-no-backdrop .swal2-shown.swal2-bottom-start {
    bottom: 0;
    left: 0
}

body.swal2-no-backdrop .swal2-shown.swal2-bottom-end,
body.swal2-no-backdrop .swal2-shown.swal2-bottom-right {
    right: 0;
    bottom: 0
}

.swal2-container {
    display: flex;
    position: fixed;
    z-index: 1060;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    padding: .625em;
    overflow-x: hidden;
    background-color: transparent;
    -webkit-overflow-scrolling: touch
}

.swal2-container.swal2-top {
    align-items: flex-start
}

.swal2-container.swal2-top-left,
.swal2-container.swal2-top-start {
    align-items: flex-start;
    justify-content: flex-start
}

.swal2-container.swal2-top-end,
.swal2-container.swal2-top-right {
    align-items: flex-start;
    justify-content: flex-end
}

.swal2-container.swal2-center {
    align-items: center
}

.swal2-container.swal2-center-left,
.swal2-container.swal2-center-start {
    align-items: center;
    justify-content: flex-start
}

.swal2-container.swal2-center-end,
.swal2-container.swal2-center-right {
    align-items: center;
    justify-content: flex-end
}

.swal2-container.swal2-bottom {
    align-items: flex-end
}

.swal2-container.swal2-bottom-left,
.swal2-container.swal2-bottom-start {
    align-items: flex-end;
    justify-content: flex-start
}

.swal2-container.swal2-bottom-end,
.swal2-container.swal2-bottom-right {
    align-items: flex-end;
    justify-content: flex-end
}

.swal2-container.swal2-bottom-end>:first-child,
.swal2-container.swal2-bottom-left>:first-child,
.swal2-container.swal2-bottom-right>:first-child,
.swal2-container.swal2-bottom-start>:first-child,
.swal2-container.swal2-bottom>:first-child {
    margin-top: auto
}

.swal2-container.swal2-grow-fullscreen>.swal2-modal {
    display: flex !important;
    flex: 1;
    align-self: stretch;
    justify-content: center
}

.swal2-container.swal2-grow-row>.swal2-modal {
    display: flex !important;
    flex: 1;
    align-content: center;
    justify-content: center
}

.swal2-container.swal2-grow-column {
    flex: 1;
    flex-direction: column
}

.swal2-container.swal2-grow-column.swal2-bottom,
.swal2-container.swal2-grow-column.swal2-center,
.swal2-container.swal2-grow-column.swal2-top {
    align-items: center
}

.swal2-container.swal2-grow-column.swal2-bottom-left,
.swal2-container.swal2-grow-column.swal2-bottom-start,
.swal2-container.swal2-grow-column.swal2-center-left,
.swal2-container.swal2-grow-column.swal2-center-start,
.swal2-container.swal2-grow-column.swal2-top-left,
.swal2-container.swal2-grow-column.swal2-top-start {
    align-items: flex-start
}

.swal2-container.swal2-grow-column.swal2-bottom-end,
.swal2-container.swal2-grow-column.swal2-bottom-right,
.swal2-container.swal2-grow-column.swal2-center-end,
.swal2-container.swal2-grow-column.swal2-center-right,
.swal2-container.swal2-grow-column.swal2-top-end,
.swal2-container.swal2-grow-column.swal2-top-right {
    align-items: flex-end
}

.swal2-container.swal2-grow-column>.swal2-modal {
    display: flex !important;
    flex: 1;
    align-content: center;
    justify-content: center
}

.swal2-container:not(.swal2-top):not(.swal2-top-start):not(.swal2-top-end):not(.swal2-top-left):not(.swal2-top-right):not(.swal2-center-start):not(.swal2-center-end):not(.swal2-center-left):not(.swal2-center-right):not(.swal2-bottom):not(.swal2-bottom-start):not(.swal2-bottom-end):not(.swal2-bottom-left):not(.swal2-bottom-right):not(.swal2-grow-fullscreen)>.swal2-modal {
    margin: auto
}

@media all and (-ms-high-contrast:none),
(-ms-high-contrast:active) {
    .swal2-container .swal2-modal {
        margin: 0 !important
    }
}

.swal2-container.swal2-fade {
    transition: background-color .1s
}

.swal2-container.swal2-shown {
    background-color: rgba(0, 0, 0, .4)
}

.swal2-popup {
    display: none;
    position: relative;
    box-sizing: border-box;
    flex-direction: column;
    justify-content: center;
    width: 32em;
    max-width: 100%;
    padding: 1.25em;
    border: none;
    border-radius: .3125em;
    background: #fff;
    font-family: inherit;
    font-size: 1rem;
    padding-bottom: 20px;
}

.swal2-popup:focus {
    outline: 0
}

.swal2-popup.swal2-loading {
    overflow-y: hidden
}

.swal2-header {
    display: flex;
    flex-direction: column;
    align-items: center
}

.swal2-title {
    position: relative;
    max-width: 100%;
    margin: 0 0 .4em;
    padding: 0;
    color: #494949;
    font-size: 1.875em;
    font-weight: 600;
    text-align: center;
    text-transform: none;
    word-wrap: break-word
}

.swal2-actions {
    display: flex;
    z-index: 1;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    width: 100%;
    margin: 1.25em auto 0
}

.swal2-actions:not(.swal2-loading) .swal2-styled[disabled] {
    opacity: .4
}

.swal2-actions:not(.swal2-loading) .swal2-styled:hover {
    background-image: linear-gradient(rgba(0, 0, 0, .1), rgba(0, 0, 0, .1))
}

.swal2-actions:not(.swal2-loading) .swal2-styled:active {
    background-image: linear-gradient(rgba(0, 0, 0, .2), rgba(0, 0, 0, .2))
}

.swal2-actions.swal2-loading .swal2-styled.swal2-confirm {
    box-sizing: border-box;
    width: 2.5em;
    height: 2.5em;
    margin: .46875em;
    padding: 0;
    -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
    animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
    border: .25em solid transparent;
    border-radius: 100%;
    border-color: transparent;
    background-color: transparent !important;
    color: transparent;
    cursor: default;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.swal2-actions.swal2-loading .swal2-styled.swal2-cancel {
    margin-right: 30px;
    margin-left: 30px
}

.swal2-actions.swal2-loading :not(.swal2-styled).swal2-confirm::after {
    content: "";
    display: inline-block;
    width: 15px;
    height: 15px;
    margin-left: 5px;
    -webkit-animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
    animation: swal2-rotate-loading 1.5s linear 0s infinite normal;
    border: 3px solid #999;
    border-radius: 50%;
    border-right-color: transparent;
    box-shadow: 1px 1px 1px #fff
}

.swal2-styled {
    margin: .3125em;
    padding: .625em 2em;
    box-shadow: none;
    font-weight: 500
}

.swal2-styled:not([disabled]) {
    cursor: pointer
}

.swal2-styled.swal2-confirm {
    border: 0;
    border-radius: .25em;
    background: initial;
    background-color: #3b3b3b;
    color: #fff;
    font-size: 1.0625em
}

.swal2-styled.swal2-cancel {
    border: 0;
    border-radius: .25em;
    background: initial;
    background-color: #aaa;
    color: #fff;
    font-size: 1.0625em
}

.swal2-styled:focus {
    outline: 0;
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px rgba(50, 100, 150, .4)
}

.swal2-styled::-moz-focus-inner {
    border: 0
}

.swal2-footer {
    justify-content: center;
    margin: 1.25em 0 0;
    padding: 1em 0 0;
    border-top: 1px solid #eee;
    color: #545454;
    font-size: 1em
}

.swal2-image {
    max-width: 100%;
    margin: 1.25em auto
}

.swal2-close {
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0;
    justify-content: center;
    width: 1.2em;
    height: 1.2em;
    padding: 0;
    overflow: hidden;
    transition: color .1s ease-out;
    border: none;
    border-radius: 0;
    outline: initial;
    background: 0 0;
    color: #ccc;
    font-family: serif;
    font-size: 2.5em;
    line-height: 1.2;
    cursor: pointer
}

.swal2-close:hover {
    transform: none;
    background: 0 0;
    color: #f27474
}

.swal2-content {
    z-index: 1;
    justify-content: center;
    margin: 0;
    padding: 0;
    color: #545454;
    font-size: 1.125em;
    font-weight: 400;
    line-height: normal;
    text-align: center;
    word-wrap: break-word
}

.swal2-checkbox,
.swal2-file,
.swal2-input,
.swal2-radio,
.swal2-select,
.swal2-textarea {
    margin: 1em auto
}

.swal2-file,
.swal2-input,
.swal2-textarea {
    box-sizing: border-box;
    width: 100%;
    transition: border-color .3s, box-shadow .3s;
    border: 1px solid #d9d9d9;
    border-radius: .1875em;
    background: inherit;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, .06);
    color: inherit;
    font-size: 1.125em
}

.swal2-file.swal2-inputerror,
.swal2-input.swal2-inputerror,
.swal2-textarea.swal2-inputerror {
    border-color: #f27474 !important;
    box-shadow: 0 0 2px #f27474 !important
}

.swal2-file:focus,
.swal2-input:focus,
.swal2-textarea:focus {
    border: 1px solid #b4dbed;
    outline: 0;
    box-shadow: 0 0 3px #c4e6f5
}

.swal2-file::-webkit-input-placeholder,
.swal2-input::-webkit-input-placeholder,
.swal2-textarea::-webkit-input-placeholder {
    color: #ccc
}

.swal2-file::-moz-placeholder,
.swal2-input::-moz-placeholder,
.swal2-textarea::-moz-placeholder {
    color: #ccc
}

.swal2-file:-ms-input-placeholder,
.swal2-input:-ms-input-placeholder,
.swal2-textarea:-ms-input-placeholder {
    color: #ccc
}

.swal2-file::-ms-input-placeholder,
.swal2-input::-ms-input-placeholder,
.swal2-textarea::-ms-input-placeholder {
    color: #ccc
}

.swal2-file::placeholder,
.swal2-input::placeholder,
.swal2-textarea::placeholder {
    color: #ccc
}

.swal2-range {
    margin: 1em auto;
    background: inherit
}

.swal2-range input {
    width: 80%
}

.swal2-range output {
    width: 20%;
    color: inherit;
    font-weight: 600;
    text-align: center
}

.swal2-range input,
.swal2-range output {
    height: 2.625em;
    padding: 0;
    font-size: 1.125em;
    line-height: 2.625em
}

.swal2-input {
    height: 2.625em;
    padding: 0 .75em
}

.swal2-input[type=number] {
    max-width: 10em
}

.swal2-file {
    background: inherit;
    font-size: 1.125em
}

.swal2-textarea {
    height: 6.75em;
    padding: .75em
}

.swal2-select {
    min-width: 50%;
    max-width: 100%;
    padding: .375em .625em;
    background: inherit;
    color: inherit;
    font-size: 1.125em
}

.swal2-checkbox,
.swal2-radio {
    align-items: center;
    justify-content: center;
    background: inherit;
    color: inherit
}

.swal2-checkbox label,
.swal2-radio label {
    margin: 0 .6em;
    font-size: 1.125em
}

.swal2-checkbox input,
.swal2-radio input {
    margin: 0 .4em
}

.swal2-validation-message {
    display: none;
    align-items: center;
    justify-content: center;
    padding: .625em;
    overflow: hidden;
    background: #f0f0f0;
    color: #666;
    font-size: 1em;
    font-weight: 300
}

.swal2-validation-message::before {
    content: "!";
    display: inline-block;
    width: 1.5em;
    min-width: 1.5em;
    height: 1.5em;
    margin: 0 .625em;
    zoom: normal;
    border-radius: 50%;
    background-color: #f27474;
    color: #fff;
    font-weight: 600;
    line-height: 1.5em;
    text-align: center
}

@supports (-ms-accelerator:true) {
    .swal2-range input {
        width: 100% !important
    }

    .swal2-range output {
        display: none
    }
}

@media all and (-ms-high-contrast:none),
(-ms-high-contrast:active) {
    .swal2-range input {
        width: 100% !important
    }

    .swal2-range output {
        display: none
    }
}

@-moz-document url-prefix() {
    .swal2-close:focus {
        outline: 2px solid rgba(50, 100, 150, .4)
    }
}

.swal2-icon {
    position: relative;
    box-sizing: content-box;
    justify-content: center;
    width: 5em;
    height: 5em;
    margin: 1.25em auto 1.875em;
    zoom: normal;
    border: .25em solid transparent;
    border-radius: 50%;
    font-family: inherit;
    line-height: 5em;
    cursor: default;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none
}

.swal2-icon::before {
    display: flex;
    align-items: center;
    height: 92%;
    font-size: 3.75em
}

.swal2-icon.swal2-error {
    border-color: #f27474
}

.swal2-icon.swal2-error .swal2-x-mark {
    position: relative;
    flex-grow: 1
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line] {
    display: block;
    position: absolute;
    top: 2.3125em;
    width: 2.9375em;
    height: .3125em;
    border-radius: .125em;
    background-color: #f27474
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left] {
    left: 1.0625em;
    transform: rotate(45deg)
}

.swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right] {
    right: 1em;
    transform: rotate(-45deg)
}

.swal2-icon.swal2-warning {
    border-color: #facea8;
    color: #f8bb86
}

.swal2-icon.swal2-warning::before {
    content: "!"
}

.swal2-icon.swal2-info {
    border-color: #9de0f6;
    color: #3fc3ee
}

.swal2-icon.swal2-info::before {
    content: "i"
}

.swal2-icon.swal2-question {
    border-color: #c9dae1;
    color: #87adbd
}

.swal2-icon.swal2-question::before {
    content: "?"
}

.swal2-icon.swal2-question.swal2-arabic-question-mark::before {
    content: "؟"
}

.swal2-icon.swal2-success {
    border-color: #a5dc86
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line] {
    position: absolute;
    width: 3.75em;
    height: 7.5em;
    transform: rotate(45deg);
    border-radius: 50%
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=left] {
    top: -.4375em;
    left: -2.0635em;
    transform: rotate(-45deg);
    transform-origin: 3.75em 3.75em;
    border-radius: 7.5em 0 0 7.5em
}

.swal2-icon.swal2-success [class^=swal2-success-circular-line][class$=right] {
    top: -.6875em;
    left: 1.875em;
    transform: rotate(-45deg);
    transform-origin: 0 3.75em;
    border-radius: 0 7.5em 7.5em 0
}

.swal2-icon.swal2-success .swal2-success-ring {
    position: absolute;
    z-index: 2;
    top: -.25em;
    left: -.25em;
    box-sizing: content-box;
    width: 100%;
    height: 100%;
        border: 1px solid #494949;
    border-radius: 50%
}

.swal2-icon.swal2-success .swal2-success-fix {
    position: absolute;
    z-index: 1;
    top: .5em;
    left: 1.625em;
    width: .4375em;
    height: 5.625em;
    transform: rotate(-45deg)
}

.swal2-icon.swal2-success [class^=swal2-success-line] {
    display: block;
    position: absolute;
    z-index: 2;
    height: .3125em;
    border-radius: .125em;
    background-color: #a5dc86
}

.swal2-icon.swal2-success [class^=swal2-success-line][class$=tip] {
    top: 2.875em;
    left: .875em;
    width: 1.5625em;
    transform: rotate(45deg)
}

.swal2-icon.swal2-success [class^=swal2-success-line][class$=long] {
    top: 2.375em;
    right: .5em;
    width: 2.9375em;
    transform: rotate(-45deg)
}

.swal2-progress-steps {
    align-items: center;
    margin: 0 0 1.25em;
    padding: 0;
    background: inherit;
    font-weight: 600
}

.swal2-progress-steps li {
    display: inline-block;
    position: relative
}

.swal2-progress-steps .swal2-progress-step {
    z-index: 20;
    width: 2em;
    height: 2em;
    border-radius: 2em;
    background: #3085d6;
    color: #fff;
    line-height: 2em;
    text-align: center
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {
    background: #3085d6
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step {
    background: #add8e6;
    color: #fff
}

.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line {
    background: #add8e6
}

.swal2-progress-steps .swal2-progress-step-line {
    z-index: 10;
    width: 2.5em;
    height: .4em;
    margin: 0 -1px;
    background: #3085d6
}

[class^=swal2] {
    -webkit-tap-highlight-color: transparent
}

.swal2-show {
    -webkit-animation: swal2-show .3s;
    animation: swal2-show .3s
}

.swal2-show.swal2-noanimation {
    -webkit-animation: none;
    animation: none
}

.swal2-hide {
    -webkit-animation: swal2-hide .15s forwards;
    animation: swal2-hide .15s forwards
}

.swal2-hide.swal2-noanimation {
    -webkit-animation: none;
    animation: none
}

.swal2-rtl .swal2-close {
    right: auto;
    left: 0
}

.swal2-animate-success-icon .swal2-success-line-tip {
    -webkit-animation: swal2-animate-success-line-tip .75s;
    animation: swal2-animate-success-line-tip .75s
}

.swal2-animate-success-icon .swal2-success-line-long {
    -webkit-animation: swal2-animate-success-line-long .75s;
    animation: swal2-animate-success-line-long .75s
}

.swal2-animate-success-icon .swal2-success-circular-line-right {
    -webkit-animation: swal2-rotate-success-circular-line 4.25s ease-in;
    animation: swal2-rotate-success-circular-line 4.25s ease-in
}

.swal2-animate-error-icon {
    -webkit-animation: swal2-animate-error-icon .5s;
    animation: swal2-animate-error-icon .5s
}

.swal2-animate-error-icon .swal2-x-mark {
    -webkit-animation: swal2-animate-error-x-mark .5s;
    animation: swal2-animate-error-x-mark .5s
}

@-webkit-keyframes swal2-rotate-loading {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

@keyframes swal2-rotate-loading {
    0% {
        transform: rotate(0)
    }

    100% {
        transform: rotate(360deg)
    }
}

@media print {
    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) {
        overflow-y: scroll !important
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown)>[aria-hidden=true] {
        display: none
    }

    body.swal2-shown:not(.swal2-no-backdrop):not(.swal2-toast-shown) .swal2-container {
        position: static !important
    }
}


/******************  coreadmin.css *******************/

.select2-container .select2-selection--single {
    box-sizing: border-box;
    height: 34px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 35px;
    font-size: 12px;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    margin-top: 5px;
}

.datepicker.dropdown-menu {
    visibility: visible;
    opacity: 1;
    width: auto;
}

p.error {
    display: none;
    color: #f00;
}

.modal-header {
    background: #e8e7e7;
    color: #494949;
        padding: 13px 30px;
}

.modal-header .close {
    color: #fff;
}

.modal-title {
    margin: 0;
    line-height: 1.42857143;
    color: #494949;
    font-weight: 700;
    position: relative;
    top: 2px;
    font-size: 24px;
}

.m-t-25 {
    margin-top: 25px !important;
}

.m-r-5 {
    margin-right: 5px;
}

.outerLoadr {
    background: rgba(0, 0, 0, 0.8);
    position: fixed;
    top: 0;
    right: 0;
    display: block;
    bottom: 0;
    left: 0;
    z-index: 9999;
}

#loader {
    border: 16px solid #f3f3f3;
    border-radius: 50%;
    border-top: 16px solid #3498db;
    width: 120px;
    height: 120px;
    -webkit-animation: spin 2s linear infinite;
    animation: spin 2s linear infinite;
    margin-left: 50px;
    z-index: 9999;
    position: absolute;
    margin-top: 50px;
}

.p5 {
    padding: 8px 0px 0px 10px;
    margin-top: 50px;
    color: #f9f6f6;
    text-align: center;
    font-weight: bold;
    text-transform: uppercase;
}

.dateItem {
    margin-bottom: 10px;
}

span.selMonthName {
    margin-right: 10px;
}

.font-wht {
    color: #fff !important;
}

#global-loader {
    position: fixed;
    z-index: 50000;
    background: url(../images/loader.gif) no-repeat 50% 50% rgb(24, 117, 187);
    left: 0;
    top: 0px;
    padding-top: 380px;
    right: 0;
    bottom: 0;
    margin: 0 auto;
    color: #fff !important;
    text-align: center;
    font-size: 20px;
}

#message {
    position: relative;
    top: -40px;
    max-width: 40%;
    left: 20%;
}

p.bdgtPos {
    position: absolute;
    top: 35px;
    left: 18%;
    font-style: italic;
    font-weight: bold;
    font-size: 12px;
}

.LoadCL {
    color: #fff !important;
    font-size: 22px !important;
}

.chatIcn {
    float: left;
}

.colImg {
    width: 40%;
    float: left;
    margin-top: 10px;
}

.colImg img {
    border-radius: 35%;
}

.calander {
    float: left;
    margin-top: 10px;
}

.colData {
    width: 60%;
    float: left;
}

.colData p {
    font-size: 15px;
    font-style: italic;
}

.mngrinfo {
    float: left;
    width: 100%;
}

.events {
    float: left;
    width: 100%;
}

.events .head {
    font-size: 17px;
    text-align: center;
    padding: 5px 0px 10px 0;
}

.events .itmEvnts {
    width: 100%;
    float: left;
}

.itmEvnts ul li {
    font-size: 14px;
    font-weight: 700;
}

.itmEvnts ul {
    padding: 0 0px 0px 15px;
}

.btn.btn-xs.mrs.cnf {
    background: #29b711;
    color: #fff;
    float: left;
    border: none;
    padding: 6px;
}

a.imPnts {
    float: left;
}

.impNts {
    font-size: 30px;
    float: left;
    height: 20px;
    color: #f00;
    margin-left: 8px;
    cursor: pointer;
}

.redClr {
    color: #f00;
}

.redClrStyle {
    font-weight: bold;
    color: rgb(151, 7, 7);
}

.correctDate {
    width: 42px;
}

.p-r-0 {
    padding-right: 0px !important;
}

.btn.btn-xs.mrs.unbl {
    background: #337ab7;
    color: #fff;
    border: none;
    padding: 6px;
}

.btn.btn-xs.mrs.cnld {
    background: #de1846;
    color: #fff;
    float: left;
    border: none;
    padding: 6px;
}

.bookTable thead tr {
    background: #5dce5d !important;
}

.blue {
    background-color: #3472f7;
    color: #ffffff;
    border-color: #3472f7;
}

.datepicker-dropdown td:last-child,
.datepicker-dropdown td:nth-last-child(2) {
    color: #de1e47;
    font-style: italic;
    font-weight: bold;
}

ul.smallHd {
    list-style: none;
    margin-left: 0;
    padding-left: 10px;
    color: #2828e8;
    font-size: 18px;
    padding-right: 10px;
}

.quickInfo {
    margin-left: 10px;
    margin-top: 135px;
    float: left;
}

.font10 {
    font-size: 10px !important;
}

.bookTable thead tr th {
    color: #000 !important;
}

.bookTable>tbody>tr>td {
    border: 1px solid #f4f4f4;
}

.col-md-6.bordrNice {
    border: 1px solid;
    margin-bottom: 10px;
    margin-left: 10px;
    padding: 5px;
    height: 450px;
    width: 49%;
}

.date {
    font-size: 23px;
    width: 100%;
    margin: 10px 0 10px 0;
}

.m-t {
    margin: 10px 0 10px 0;
}

.m-r-20 {
    min-width: 60%;
    float: left;
}

.btn-sm {
    font-size: 12px;
    border-radius: 3px;
    padding: 3px 7px;
}

.amt {
    font-size: 19px;
    font-weight: 800;
}

.p-l-20 {
    padding-left: 20px;
}

.amt-sm {
    font-size: 16px;
}

.highcharts-exporting-group,
.highcharts-credits {
    display: none;
}

.alert-msg {
    border: 2px solid #0e790e;
    padding: 4px;
    background: #ec6262;
    margin-left: 30px;
    color: #fff;
    padding: 10px;
}

ul.smallHdr {
    list-style: none;
    margin-left: 0;
    padding-left: 10px;
    color: #ffffff;
    font-size: 18px;
    padding-right: 10px;
}

ul.smallHdr li,
ul.smallHd li {
    display: inline;
}

.m-t-20 {
    margin-top: 20px !important;
}

.bgDarkBlue {
    background: #0db5b5;
    border-radius: 9px;
    margin: 0px;
}

.btn-orange {
    border-color: #f7f7f8 !important;
    color: #000000 !important;
    border-radius: 5px !important;
    background: orange !important;
    padding: 6px;
    border: none;
}

.btn-red {
    border-color: #f00 !important;
    color: #000000 !important;
    border-radius: 5px !important;
    background: red !important;
    padding: 6px;
    border: none;
}

#searchReset {
    background-color: #f79700;
    border-color: #3472f7;
    color: #3472f7;
}

#searchReset a:hover {
    background-color: yellow !important;
}

.openBookingModal {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.openBookingModal a:hover {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.openBookingModal:focus,
.openBookingModal:hover,
.openBookingModal.active {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.setBudget {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.setBudget a:hover {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.setBudget:focus,
.setBudget:hover,
.setBudget.active {
    border-color: #3472f7;
    background-color: #3472f7;
    color: #ffffff;
}

.amendPreview {
    color: #3472f7;
}

.textMsg {
    border: 1px solid #E3E3E3;
    width: 120%;
    padding: 5px;
    text-align: left;
    height: 50px;
    color: #ec6262;
}

.buttonRed {
    color: #ffffff;
    background-color: #FF4A55;
}

.buttonGreen {
    color: #ffffff;
    background-color: #87CB16;
}


/* CALANDER */

ul {
    list-style-type: none;
}

.month {
padding: 70px 25px;
    width: 100%;
    background: #6b6969;
    text-align: center;
    color: #fff;
}

.month ul {
    margin: 0;
    padding: 0;
}

.month ul li {
    color: white;
    font-size: 20px;
    text-transform: uppercase;
    letter-spacing: 3px;
}

.month .prev {
    float: left;
    padding-top: 10px;
}

.month .next {
    float: right;
    padding-top: 10px;
}

.weekdays {
    margin: 0;
    padding: 10px 0;
    background-color: #ddd;
}

.weekdays li {
    display: inline-block;
    width: 12.6%;
    color: #666;
    text-align: center;
}

.days {
    padding: 10px 0;
    background: none;
    margin: 0;
}

.days li {
    list-style-type: none;
    display: inline-block;
    width: 12.6%;
    text-align: center;
    margin-bottom: 5px;
    font-size: 12px;
    color: #fff;
}

.days li .active {
    padding: 5px;
    background: #1abc9c;
    color: white !important
}


/* Add media queries for smaller screens */

@media screen and (max-width:720px) {

    .weekdays li,
    .days li {
        width: 13.1%;
    }
}

@media screen and (max-width: 420px) {

    .weekdays li,
    .days li {
        width: 12.5%;
    }

    .days li .active {
        padding: 2px;
    }
}

@media screen and (max-width: 290px) {

    .weekdays li,
    .days li {
        width: 12.2%;
    }
}


/* CALANDER */

.m-t-10 {
    margin-top: 10px !important;
}

.tr-bg-red {
    background-color: #ef9a9a !important;
}


/* AMEND BODY BG Color */

.body-bg-color {
    background-color: #F5F5F5;
}

.row.comingDays {
    z-index: 999;
}

.UpcomingDays {
    float: left;
    margin-right: 8px;
    background: #cec5c5;
    padding: 6px 10px 5px 6px;
    margin-top: 15px;
    border-radius: 4px;
}

.futrRest {
    float: left;
    margin-top: -5px;
}

.futrRest span {
    float: left;
    margin: 5px;
}

.entriesSec {
    display: inline-block;
    padding: 15px 7px;
    width: 98.2%;
    margin: 14px;
    background: #dedede;
    margin-top: 0;
    margin-bottom: 22px;
}

.smalHead {
    font-size: 12px;
    font-style: italic;
}

.m-b-10 {
    margin-bottom: 10px;
}

#newChatModal .modal-header {
    background: #70acaa !important;
}

.wdth100 {
    width: 100% !important;
}

.msgItm {
    margin-bottom: 5px;
    width: 100%;
    float: left;
}

.chatMsg.text-right {
    background: #b5c4e8;
    padding: 10px;
    float: right;
    display: inline-block;
    border-radius: 10px;
}

.chatMsg.text-left {
    background: #e8b5b5;
    padding: 10px;
    display: inline-block;
    border-radius: 10px;
}

.unread {
    background: #ee7e33 !important;
}

.p-l-5 {
    padding-left: 5px !important;
}

.redText {
    color: #FF4A55;
}

.textBlue {
    color: #6a2fc3;
}

.orangeMsg {
    color: #f29d33;
}

.divBg {
    background-color: #70acaa;
    height: 40px;
    color: #f1f1f1;
    padding-left: 25px;
    padding-top: 10px;
}

tbody.bookedDetials tr td {
    border: 1px solid !important;
}

#newChatModal .modal-content {
    /* background: #9ca09c; */
    height: auto;
    max-height: 450px;
}

table {
    width: 100%;
}

.table thead th {
    text-align: left;
    padding: 8px;
}

.table tbody {
    margin-bottom: 10px;
    font-size: 13px;
}

tbody tr td {
    padding: 8px;
}

.table-bordered {
    border: 1px solid #f4f4f4;
    width: 100%;
    border-spacing: 0;
    border-collapse: collapse;
}

.container {
    border: 2px solid #FF69B4;
    background-color: #FFB6C1;
    border-radius: 5px;
    padding: 10px;
    margin: 10px 0;
    width: auto;
}

.entry_bx {
    width: 120px;
    background: #fff;
    border: 1px solid #737373;
    border-radius: 5px;
    padding: 5px 10px;
    color: #494949;
    font-weight: 600;
        height: 34px;
    margin-left: 3px;
    margin-right: 6px;
    margin-top: 5px;
    resize: none;
    font-size: 12px;
}

.entry_lab {
    font-size: 12px;
}

.ngChat {
    text-align: right;
    float: right;
    width: 60%;
}


/* Darker chat container */

.darker {
    float: left;
    width: 60%;
    border-color: #0000A0;
    background-color: #ADD8E6;
}


/* Clear floats */

.container::after {
    content: "";
    clear: both;
    display: table;
}


/* Style images */

.container img {
    float: left;
    max-width: 60px;
    width: 100%;
    margin-right: 20px;
    border-radius: 50%;
}


/* Style the right image */

.container img.right {
    float: right;
    margin-left: 20px;
    margin-right: 0;
}


/* Style time text */

.time-right {
    float: right;
    color: #aaa;
}


/* Style time text */

.time-left {
    float: left;
    color: #999;
}

.amendButton,
.cancelAmendButton {
    margin-right: 20px;
    margin-left: 20px;
}

.mb20 {
    margin-bottom: 20px;
}

.bgRed {
    background-color: #FF4A55 !important;
}

.amendModalBtn {
    width: 90px;
    color: #000;
    height: 50px;
}

.bd-example-modal-lg .modal-dialog {
    display: table;
    position: relative;
    margin: 0 auto;
    top: calc(50% - 24px);
}

.bd-example-modal-lg .modal-dialog .modal-content {
    background-color: transparent;
    border: none;
}

.Needlfet {
    position: relative;
    left: -2px;
}

.needleico {
    height: 15px;
}

/**************  amend.css ********************/

.pgntn {
    float: left;
    width: 100%;
    margin-bottom: 10px;
}

.pagination li {
    width: 41px !important;
    display: inline;
    border-bottom: none !important;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px;
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #494949;
    border-color: #494949;
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
    margin-left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border: none;
    font-size: 20px;
    color: #8e8e8e;
    padding-top: 0;
    margin-top: 9px;
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border: none;
    font-size: 20px;
    color: #8e8e8e;
    padding-top: 0;
    margin-top: 9px;
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 0px 0;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #494949;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 200px;
    margin: 13px 4px;
    width: 30px;
    font-size: 12px;
    height: 30px;
    text-align: center;
}

.modal-header .close {
    margin-top: 4px;
    font-size: 28px !important;
    margin-right: 19px;
    opacity: 9;
}

.D_inline li {
    display: inline !important;
    line-height: 46px;
}

.amdOK {
    line-height: 10px;
    font-size: 16px;
}

.ptl-20 {
    padding-top: 17px;
    padding-left: 25px;
}

.amd_fullwidth {
    padding: 10px;
    height: auto;
    margin: 0 auto;
    margin-bottom: 10px;
    border: 1px solid #ddd;
}

.header_txt {

}

.modal-title b {
    margin: 0;
    font-size: 23px;
    line-height: 37px;
}

.left_amdimg img {
    width: 46px;
    height: 46px;
    border-radius: 200px;
}

.left_amdimg {
    float: left;
}

ul.smallHdr {
    list-style: none;
    margin-left: 0;
        margin-bottom: 16px;
    padding-left: 60px;
    color: #000000;
    font-size: 15px;
    line-height: 22px
}

.left_amd {
    min-height: 150px;
    height: auto;
    margin-bottom: 0;
    margin: 17px;
    box-shadow: 1px 2px 5px #e9e9e9;
    padding: 20px;
}

.right_amd {
    min-height: 150px;
    height: auto;
    margin-bottom: 0;
        margin: 17px;
    box-shadow: 1px 2px 5px #e9e9e9;
    padding: 20px;
}

ul.smallHdr li {
    display: inherit;
}

.add_not {
    padding-left: 60px;
    font-size: 15px;
}

.right_amd_padd {
    padding-left: 60px;
    line-height: 22px;
}

.modal-backdrop {
    z-index: 0 !important;
}

.modal-content {
    width: 100% !important;
}

.amd_select {
    font-size: 22px;
    font-weight: 600;
    padding-right: 28px;
}

.modal-footer {
    padding: 12px 20px 20px;
    border-top: 1px solid #e5e5e5;
    background: #f7f7f7;
}
.ml3{
margin-left: 3px;
}

.foot_pOne {
    font-size: 15px;
    margin-bottom: 0;
    margin-top: 3px;
    text-align: left;
}

.hil_cl {
    font-weight: 600;
    color: #1875bb;
    font-size: 15px;
}

.foot_pTwo {
    margin-bottom: 0;
    color: #eb8001;
    font-size: 18px;
    font-weight: 600;
}

button.amdbtn_green.amendModalBtn.amendButton.btn-orange {
    outline: none;
    box-shadow: none;
}

.mtb10 {
    margin: 10px 0;
}

.lable_style label {
    font-size: 15px;
    margin-bottom: 6px;
}

.c_detailsp {
    padding: 12px 12px 12px;
    border-bottom: 1px solid #c7c7c7;
    font-size: 14px;
}

.bgRed {
    background-color: #f02828 !important;
}

b,
strong {
    font-weight: 600 !important;
}

.cancel_icon {
    width: 22px;
    height: 22px;
    float: left;
    margin-right: 10px;
    margin-bottom: 15px;
}

.cancel_icon img {
    width: 100%;
}

/*************************flatpickr.min.css********************/

.flatpickr-calendar {
    background: transparent;
    opacity: 0;
    display: none;
    text-align: center;
    visibility: hidden;
    padding: 0;
    -webkit-animation: none;
    animation: none;
    direction: ltr;
    border: 0;
    font-size: 14px;
    line-height: 24px;
    border-radius: 5px;
    position: absolute;
    width: 307.875px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    -ms-touch-action: manipulation;
    touch-action: manipulation;
    background: #fff;
    -webkit-box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08);
    box-shadow: 1px 0 0 #e6e6e6, -1px 0 0 #e6e6e6, 0 1px 0 #e6e6e6, 0 -1px 0 #e6e6e6, 0 3px 13px rgba(0, 0, 0, 0.08)
}

.flatpickr-calendar.open,
.flatpickr-calendar.inline {
    opacity: 1;
    max-height: 640px;
    visibility: visible
}

.flatpickr-calendar.open {
    display: inline-block;
    z-index: 99999
}

.flatpickr-calendar.animate.open {
    -webkit-animation: fpFadeInDown 300ms cubic-bezier(.23, 1, .32, 1);
    animation: fpFadeInDown 300ms cubic-bezier(.23, 1, .32, 1)
}

.flatpickr-calendar.inline {
    display: block;
    position: relative;
    top: 2px
}

.flatpickr-calendar.static {
    position: absolute;
    top: calc(100% + 2px)
}

.flatpickr-calendar.static.open {
    z-index: 999;
    display: block
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+1) .flatpickr-day.inRange:nth-child(7n+7) {
    -webkit-box-shadow: none !important;
    box-shadow: none !important
}

.flatpickr-calendar.multiMonth .flatpickr-days .dayContainer:nth-child(n+2) .flatpickr-day.inRange:nth-child(7n+1) {
    -webkit-box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6;
    box-shadow: -2px 0 0 #e6e6e6, 5px 0 0 #e6e6e6
}

.flatpickr-calendar .hasWeeks .dayContainer,
.flatpickr-calendar .hasTime .dayContainer {
    border-bottom: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0
}

.flatpickr-calendar .hasWeeks .dayContainer {
    border-left: 0
}

.flatpickr-calendar.hasTime .flatpickr-time {
    height: 40px;
    border-top: 1px solid #e6e6e6
}

.flatpickr-calendar.noCalendar.hasTime .flatpickr-time {
    height: auto
}

.flatpickr-calendar:before,
.flatpickr-calendar:after {
    position: absolute;
    display: block;
    pointer-events: none;
    border: solid transparent;
    content: '';
    height: 0;
    width: 0;
    left: 22px
}

.flatpickr-calendar.rightMost:before,
.flatpickr-calendar.arrowRight:before,
.flatpickr-calendar.rightMost:after,
.flatpickr-calendar.arrowRight:after {
    left: auto;
    right: 22px
}

.flatpickr-calendar.arrowCenter:before,
.flatpickr-calendar.arrowCenter:after {
    left: 50%;
    right: 50%
}

.flatpickr-calendar:before {
    border-width: 5px;
    margin: 0 -5px
}

.flatpickr-calendar:after {
    border-width: 4px;
    margin: 0 -4px
}

.flatpickr-calendar.arrowTop:before,
.flatpickr-calendar.arrowTop:after {
    bottom: 100%
}

.flatpickr-calendar.arrowTop:before {
    border-bottom-color: #e6e6e6
}

.flatpickr-calendar.arrowTop:after {
    border-bottom-color: #fff
}

.flatpickr-calendar.arrowBottom:before,
.flatpickr-calendar.arrowBottom:after {
    top: 100%
}

.flatpickr-calendar.arrowBottom:before {
    border-top-color: #e6e6e6
}

.flatpickr-calendar.arrowBottom:after {
    border-top-color: #fff
}

.flatpickr-calendar:focus {
    outline: 0
}

.flatpickr-wrapper {
    position: relative;
    display: inline-block
}

.flatpickr-months {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex
}

.flatpickr-months .flatpickr-month {
    background: transparent;
    color: rgba(0, 0, 0, 0.9);
    fill: rgba(0, 0, 0, 0.9);
    height: 34px;
    line-height: 1;
    text-align: center;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    -webkit-box-flex: 1;
    -webkit-flex: 1;
    -ms-flex: 1;
    flex: 1
}

.flatpickr-months .flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    text-decoration: none;
    cursor: pointer;
    position: absolute;
    top: 0;
    height: 34px;
    padding: 10px;
    z-index: 3;
    color: rgba(0, 0, 0, 0.9);
    fill: rgba(0, 0, 0, 0.9)
}

.flatpickr-months .flatpickr-prev-month.flatpickr-disabled,
.flatpickr-months .flatpickr-next-month.flatpickr-disabled {
    display: none
}

.flatpickr-months .flatpickr-prev-month i,
.flatpickr-months .flatpickr-next-month i {
    position: relative
}

.flatpickr-months .flatpickr-prev-month.flatpickr-prev-month,
.flatpickr-months .flatpickr-next-month.flatpickr-prev-month {
    /*
      /*rtl:begin:ignore*/
    left: 0
        /*
      /*rtl:end:ignore*/
}

/*
      /*rtl:begin:ignore*/
/*
      /*rtl:end:ignore*/
.flatpickr-months .flatpickr-prev-month.flatpickr-next-month,
.flatpickr-months .flatpickr-next-month.flatpickr-next-month {
    /*
      /*rtl:begin:ignore*/
    right: 0
        /*
      /*rtl:end:ignore*/
}

/*