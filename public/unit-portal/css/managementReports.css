.pgntn {
    float: left;
    width: 100%;
    margin-bottom: 10px;
}

.pagination li {
    width: 41px !important;
    display: inline;
    border-bottom: none !important;
}

.pagination {
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 4px;
}

select.txt_bx {
    float: left;
    font-size: 14px;
    color: #000 !important;
    background-size: 18px;
    border: 1px solid #0d4875;
    border-radius: 5px;
    padding: 0px !important;
    cursor: pointer;
    margin-right: 10px;
}

.pagination>.active>a,
.pagination>.active>a:focus,
.pagination>.active>a:hover,
.pagination>.active>span,
.pagination>.active>span:focus,
.pagination>.active>span:hover {
    z-index: 3;
    color: #fff;
    cursor: default;
    background-color: #337ab7;
    border-color: #337ab7;
}

.pagination>li>a,
.pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 0px 0;
    margin-left: -1px;
    line-height: 1.42857143;
    color: #337ab7;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 233px;
    margin: 13px 4px;
    width: 35px;
    font-size: 15px;
    height: 35px;
    text-align: center;
}

.pagination>li:last-child>a,
.pagination>li:last-child>span {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border: none;
    font-size: 27px;
    color: #8e8e8e;
    padding-top: 0;
    margin-top: 9px;
}

.pagination>li:first-child>a,
.pagination>li:first-child>span {
    margin-left: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    border: none;
    font-size: 27px;
    color: #8e8e8e;
    padding-top: 0;
    margin-top: 9px;
}

.bk_status {
    width: 6% !important;
}

.bk_cagry {
    width: 6% !important;
}

.fntBold {
    font-weight: bold;
    font-size: 18px;
}

.fntBold15 {
    font-size: 15px;
    font-weight: bold;
}

a.btn.btn-danger.btn-xs.mrs,
.btn.btn-success.btn-xs.mrs {
    border: 1px solid;
    background: #174d78;
    padding: 8px;
    float: left;
    border-radius: 6px;
}

.pagination>li>a:focus,
.pagination>li>a:hover,
.pagination>li>span:focus,
.pagination>li>span:hover {
    z-index: 2;
    color: #fff;
    background-color: #337ab7;
    border-color: #337ab7;
}

.btn-danger span,
.btn-success span {
    color: #fff;
    margin-top: 10px;
}

.graphPage {
    width: 100%;
    float: left;
}

.leftSideGrph,
.rightSideGraph {

}
.table-striped>tbody>tr:nth-of-type(even) {
    background-color: #f9f9f9 ! IMPORTANT;
}
.table-striped tbody tr {
    background-color: #fff !important;
}
.table>thead>tr>th {
    vertical-align: bottom;
    border-bottom: 2px solid #ddd;
    color: #494949 !important;
    background: #f4f4f4;
}

.graphPage td {
    padding: 7px;
}

.month {
    padding: 2px 15px;
    width: 100%;
    background: #e9e9e9;
    color: #fff;
    text-align: center;
}

.entry_bx {
    width: 80px;
    background: #fff;
    border: none;
    border-radius: 3px;
    font-weight: 600;
    margin-right: 30px;
}

.entry_lab {
    font-size: 14px;
}

.EntryStafW {
    width: 320px;
}

.shift_box {
    margin: 24px 0 30px 0;
}