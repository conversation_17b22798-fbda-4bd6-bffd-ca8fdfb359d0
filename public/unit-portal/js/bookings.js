$(document).ready(function () {
    $(".datepicker").val('');
    localStorage.setItem("categories", $(".add_booking").attr('categories'));
    localStorage.setItem("shifts", $(".add_booking").attr('shifts'));
    localStorage.setItem("contacts", $(".add_booking").attr('contacts'));
    $(".add_booking").removeAttr('categories').removeAttr('shifts').removeAttr('contacts');

    // Initially hide all urgent booking checkboxes
    $('.BxUrgent, .UrgBkng').hide();

    // $(".scroller_sk").customScrollbar();
    // $("#fixed-thumb-size-demo").customScrollbar({fixedThumbHeight: 50, fixedThumbWidth: 60});

    $(".datepicker").datepicker({
        changeMonth: true,
        changeYear: true,
        numberOfMonths: 3,
        dateFormat: 'dd-mm-yy D',
        firstDay: 1,
        minDate: 0

    });

    $('.clockpicker').clockpicker({
        placement: 'bottom',
        align: 'left',
        autoclose: true,
    });

    $('.exploreBtn').click(function () {
        Swal.fire({
            position: 'top-end',
            icon: 'warning',
            title: 'We are working on this module..Coming Soon....',
            showConfirmButton: false,
            timer: 1500
        })
    });

    $(document).on('change', ".categorySwitch,.shiftSel", function () {
        var fetch = $('.add_booking').attr('cost-fetch');
        var token = $('.add_booking').attr('token');
        var $row = $(this).closest('.full_width');
        var shift = $row.find('.shiftvalue').val();
        var category = $row.find('.categorySwitch').val();
        var element = $(this);
    
        if (category !== "" && shift !== "") {
            $.ajax({
                type: 'POST',
                url: fetch,
                data: {
                    _token: token,
                    date: shift, // if this is wrong, adjust accordingly
                    shift: shift,
                    category: category,
                },
                success: function (response) {
                    $row.find(".startTime").val(response.start);
                    $row.find(".endTime").val(response.end);
                    $row.find(".costBox").attr('price', response.cost);
                    
                    // ✅ Check urgency after time is updated
                    checkUrgency($row, response.start);
                }
            });
        }
    });
    
    /**
     * Check if urgent booking checkbox should be visible based on the 36-hour rule
     * Shows the checkbox only when the shift starts within the next 36 hours
     */
    function checkUrgency($row, timeStr) {
        let dateStr = $row.find('input[name="date[]"]').val(); // format: dd-mm-yyyy D
        let checkbox = $row.find('.urgentCheckbox');
        let urgentContainer = $row.find('.BxUrgent, .UrgBkng'); // Handle both class names
    
        if (!dateStr || !timeStr) {
            urgentContainer.hide();
            checkbox.prop('checked', false);
            return;
        }
    
        let cleanedDateStr = dateStr.trim().split(" ")[0]; // remove weekday
        let [day, month, year] = cleanedDateStr.split("-");
    
        let startDateTime = new Date(`${year}-${month}-${day}T${timeStr}:00`);
    
        if (isNaN(startDateTime)) {
            urgentContainer.hide();
            checkbox.prop('checked', false);
            return;
        }
    
        let now = new Date();
        let diffInHours = (startDateTime - now) / (1000 * 60 * 60);
    
        // Show checkbox only if shift starts within next 36 hours
        if (diffInHours <= 36 && diffInHours > 0) {
            urgentContainer.show();
            checkbox.prop('checked', false).prop('disabled', false);
        } else {
            urgentContainer.hide();
            checkbox.prop('checked', false);
        }
    }
    
    $(document).on('change', 'input[name="start_time[]"], input[name="date[]"]', function () {
        const $row = $(this).closest('.full_width');
        const timeStr = $row.find('input[name="start_time[]"]').val();
        checkUrgency($row, timeStr);
    });
    
    $('.add_save').click(function () {
        //disable the button to prevent multiple clicks and add text please wait
        $('.add_save').prop("disabled", true);
        $('.add_save').val('Please wait...');
        const userType = $('#authdata').val();
        const $form = $('#bookingForm');

        const getFilteredValues = (selector) => {
            return $form.find(selector).map(function () {
                const value = $(this).val()?.trim();
                return value === "" ? null : value;
            }).get().filter(value => value !== null);
        };

        // Get urgent booking checkbox values (returns array of 1 for checked, 0 for unchecked)
        const getUrgentValues = () => {
            return $form.find("input[name='urgent[]']").map(function () {
                return $(this).is(':checked') ? 1 : 0;
            }).get();
        };

        const payload = {
            _token: $(this).attr('token'),
            date: getFilteredValues("input[name='date[]']"),
            shift: getFilteredValues(".shiftvalue"),
            category: getFilteredValues(".categorySwitch"),
            numbers: getFilteredValues("select[name='numbers[]']"),
            startTimes: getFilteredValues("input[name='start_time[]']"),
            endTimes: getFilteredValues("input[name='end_time[]']"),
            requestedBy: getFilteredValues("input[name='requestedBy[]']"),
            reason: getFilteredValues("select[name='reasonBooking[]']"),
            impNotes: getFilteredValues(".impNotes"),
            urgent: getUrgentValues(), // Add urgent booking data
            unitId: userType === "App\\Models\\Client" ? getFilteredValues("select[name='unitId[]']") : []
        };

        // ✅ Debug logs (optional)
        console.log(payload);

        // ✅ Optional: Basic Validation
        const requiredFields = ['date', 'shift', 'category', 'startTimes', 'endTimes', 'requestedBy'];
        const missing = requiredFields.some(field => payload[field].length === 0);
        if (missing) {
            alert("Please fill in all required fields before submitting.");
            $('.add_save').prop("disabled", false);
            $('.add_save').val('Save');
            return;
        }

        // ✅ Submit via AJAX
        $.ajax({
            type: 'POST',
            url: $(this).attr('url'),
            data: payload,
            success: function (response) {
                if (response.status === 400) {
                    alert(response.message);
                    return;
                }
                alert("Booking saved successfully!");

                $("input[name='date[]']").val('');
                // $("input[name='start_time[]']").val('');
                //$("input[name='end_time[]']").val('');
                // $("input[name='requestedBy[]']").val('');
                $("select[name='numbers[]']").val('');
                $("select[name='reasonBooking[]']").val('');
                $("select[name='unitId[]']").val('');
                $("select[name='shift[]']").val('');
                $("select[name='category[]']").val('');
                $("textarea[name='impNotes[]']").val('');
                $("input[name='urgent[]']").prop('checked', false); // Clear urgent checkboxes
                $('.BxUrgent, .UrgBkng').hide(); // Hide urgent containers
                $("input[name='date[]']").datepicker("refresh");
                $("input[name='start_time[]']").clockpicker("refresh");
                $("input[name='end_time[]']").clockpicker("refresh");
                $('.add_save').prop("disabled", false);
                $('.add_save').val('Save');
                location.reload();
            },
            error: function (xhr) {
                console.error(xhr.responseText);
                alert("Something went wrong. Please try again.");
                $('.add_save').prop("disabled", false);
                $('.add_save').val('Save');
            }
        });
    });

    $('.add_newrow').click(function () {
        const auth = $('#authdata').val();
        const shiftOptions = $('.shiftvalue').html();
        const categoryOptions = $('.categorySwitch').html();
        const unitOptions = $('.unitId').html();
        const loggedInManager = $('#authdata').attr('username');
    
        const numberOptions = Array.from({ length: 10 }, (_, i) =>
            `<option value="${i + 1}">${i + 1}</option>`
        ).join('');
    
        const reasonOptions = `
            <option value="">Select</option>
            <option value="1">Staff Sickness</option>
            <option value="2">Holiday cover</option>
            <option value="3">Vacant Position</option>
            <option value="4">New Resident admission</option>
            <option value="5">1 to 1 care</option>
            <option value="6">Extra staff requirement</option>
            <option value="7">Staff training day</option>
        `;
    
        let html = `
            <div class="full_width dynamic-row NewRowDynamic">
                <div class="col-md-12 cl_bx_padd" style="padding-right: 0;padding-left: 19px; display:flex;">
                    <div class="fby_one w10Percntge AutMar" style="width: 170px !important;">
                        <input type="text" name="date[]" class="txt_bx datepicker" value="">
                    </div>
                    <div class="fby_one w10Percntge AutMar" style="width: 7% !important;">
                        <select name="shift[]" class="txt_bx shiftSel sl_box shiftvalue">${shiftOptions}</select>
                    </div>
                    <div class="fby_one w10Percntge AutMar" style="width: 12% !important;">
                        <select name="category[]" class="txt_bx categorySwitch">${categoryOptions}</select>
                    </div>
                    <div class="sby_one AutMar">
                        <select name="numbers[]" class="txt_bx">${numberOptions}</select>
                    </div>
                    <div class="fby_one w10Percntge AutMar">
                        <input placeholder="Start" type="text" name="start_time[]" class="txt_bx startTime clockpicker">
                    </div>
                    <div class="fby_one w10Percntge AutMar">
                        <input placeholder="End" type="text" name="end_time[]" class="txt_bx endTime clockpicker">
                    </div>
                    <div class="fby_one AutMar" style="width: 15%;">
                        <input type="text" value="${loggedInManager}" name="requestedBy[]" class="txt_bx" readonly>
                    </div>
                    <div style="width:auto" class="sby_one AutMar">
                        <select name="reasonBooking[]" class="txt_bx">${reasonOptions}</select>
                    </div>`;
    
        if (auth === 'App\\Models\\Client') {
            html += `
                <div class="col-md-2 pl-0 pr-0 AutMar">
                    <div class="fby_one w10Percntge w100 ">
                        <select name="unitId[]" class="sl_box unitId">
                            ${unitOptions}
                        </select>
                    </div>
                </div>`;
        }
    
        html += `
                    <div class="fby_one AutMar">
                        <textarea placeholder="Additional Notes" name="impNotes[]" class="txt_bx"></textarea>
                    </div>
                    <div class="fby_one UrgBkng AutMar" style="width: 34px;margin-left: 11px;">
                        <label>
                            <input type="checkbox" name="urgent[]" class="urgentCheckbox"  /> <br>
                            UB
                        </label>
                    </div>
                    <div class="" style="width: auto; float: right; font-size: 23px; padding: 0px;">
                        <button type="button" class="closeBtn ClsBtnAddShift">X</button>
                    </div>
                </div> <!-- end cl_bx_padd -->
            </div> <!-- end full_width -->
        `;
    
        $(".prsave").before(html);
    
        const $newRow = $(".dynamic-row").last();
    
        $newRow.find('.datepicker').datepicker({
            changeMonth: true,
            changeYear: true,
            dateFormat: 'dd-mm-yy',
            firstDay: 1,
            minDate: 0
        });
    
        $newRow.find('.clockpicker').clockpicker({
            placement: 'bottom',
            align: 'left',
            autoclose: true
        });
    
        // Initially hide the urgent booking checkbox for new rows
        $newRow.find('.UrgBkng').hide();
    
        // ✅ Attach manual change listeners
        $newRow.find('input[name="start_time[]"], input[name="date[]"]').on('change', function () {
            const timeStr = $newRow.find('input[name="start_time[]"]').val();
            checkUrgency($newRow, timeStr);
        });
    
        $newRow.find('select[name="category[]"], select[name="shift[]"]').on('change', function () {
            setTimeout(() => {
                const timeStr = $newRow.find('input[name="start_time[]"]').val();
                checkUrgency($newRow, timeStr);
            }, 500);
        });
    
        // Optional: Initial check if values are preset
        const initialTime = $newRow.find('input[name="start_time[]"]').val();
        if (initialTime) {
            checkUrgency($newRow, initialTime);
        }
    });
    

    $(document).on('change', '.reqstedBy', function () {
        if ($(this).val() == 0) {
            var index = $('.reqstedBy').index(this);
            $('.inlineBlock input').val('');
            $("#modal_new_requestby").css('display', 'block').attr('index', index);
        }
    });

    $(".closeBtnModal").click(function () {
        $("#modal_new_requestby").css('display', 'none');
    });

    $(document).on('click', '.closeBtn', function () {
        $(this).closest('.full_width').remove();
    })

    $(".add_save_modal").click(function () {
        var name = $('.inlineBlock input[name=NewCnt_name]').val();
        var position = $('.inlineBlock input[name=NewCnt_position]').val();
        var phone = $('.inlineBlock input[name=NewCnt_phone]').val();
        var email = $('.inlineBlock input[name=NewCnt_email]').val();

        $("#global-loader").html('<h2>Loading...Please wait..</h2>').show();

        var action = $(this).attr('action');
        var token = $(this).attr('token');

        $.ajax({
            type: 'POST',
            url: action,
            data: {
                _token: token,
                name: name,
                position: position,
                phone: phone,
                email: email
            },
            success: function (response) {
                $("#modal_new_requestby").css('display', 'none');
                $("#global-loader").html('').hide();
                localStorage.setItem("contacts", JSON.stringify(response.contacts));
                $('.addBooksDivs .full_width').each(function (index, element) {
                    if ($(".modal").attr('index') == index) {
                        var contacts = JSON.parse(localStorage.getItem("contacts"));
                        var html = ``;
                        for (var i = 0; i < contacts.length; i++) {
                            html += `<option value="` + contacts[i].clientUnitPhoneId + `">` + contacts[i].fullName + `</option>`;
                        }
                        html += `<option value="0" class="newClassOptn"> + Add New</option>`;
                        $(this).find('.reqstedBy').html(html).select({ minimumResultsForSearch: -1, placeholder: 'Select an Option' });
                    }
                });
            }
        });
    });

    $('.amendNow').click(function () {
        var bookId = $(this).attr('bookid');
        var fetch = $(this).attr('fetch');
        var token = $(this).attr('token');
        var timeDiff = $(this).attr('totalhrspend');
        $('.cancelConfirm').attr('bookid', bookId);
        
        // Reset modal state before loading new data
        $('.amend-status-note, .cancel-status-note').remove(); // Remove any existing status notes
        $('.amendDetails').addClass('hidden');
        $('.cancelDetails').addClass('hidden');
        $('.amendActionButtons').removeClass('hidden');
        
        $.ajax({
            type: 'POST',
            url: fetch,
            data: {
                "_token": token,
                "bookingId": bookId,
            },
            beforeSend: function () { $("#global-loader").show(); },
            success: function (response) { 
                if (!response || !response.booking) {
                    console.error("Error: Response or booking data is missing");
                    alert("Error: Booking data is not available");
                    return;
                }

                // Extracting Data with Fallbacks
                var booking = response.booking;
                var shift = booking.shift?.name ?? "Unknown Shift";
                var staffName = booking.staff?.forname && booking.staff?.surname
                    ? `${booking.staff.forname} ${booking.staff.surname}`
                    : "Unassigned";
                var preview = `${shift} | ${staffName}`;
                var requestedName = booking.requestedbyrelation?.fullName ?? "Not Provided";
                var additionalNotes = booking.importantNotes ?? "Nil";

                var startDate = `${booking.date} ${booking.start_time}`;
                var nowTime = moment(response.now);
                var startBookDate = moment(startDate);
                var duration = moment.duration(startBookDate.diff(nowTime)).asHours();
                var rightHead = ``;
                var leftHead = `<span class="left_amdimg">
                                    <img src="https://www.nursesgroupadmin.co.uk/storage/app/client/1646842964_7yetR4Yh40GloL1yoZi2CkLT16mvz17TwQoVKbYV.jpg">
                                </span>`;

                if (duration < 24) {
                    leftHead += `<ul class="smallHdr D_inline">
                                    <li class="unitName"><b>${booking.unit?.name ?? "No Unit"}</b></li> |
                                    <li class="bookId">${booking.bookingId}</li> |
                                    <li class="bookingDate">${booking.booking_date_human ?? "Unknown Date"}</li> |
                                    <li class="categoryName">${[booking.unit?.alias, booking.category?.name, shift].filter(Boolean).join(" | ")}</li>
                                 </ul>`;
                    $('.bookDataHtml').html(leftHead);
                    $('#unableToCancelModal').show();
                } else {
                    leftHead += `<ul class="smallHdr">
                    <li class="unitName"><b>` + response.booking.unit.name + `</b></li>
                    <li class="bookId">` + response.booking.bookingId + `</li>
                    <li class="shiftname">` + response.booking.shift.name + `</li>
                    <li class="bookingDate">` + response.booking.booking_date_human + `</li>
                    <li class="categoryName">` + response.booking.unit.alias + ` |  ` + response.booking.category.name + ` |  ` + response.booking.shift.name + `</li>
                 </ul>`;

                    if (response.booking.importantNotes !=null) {
                        leftHead += `<h5 class="add_not"><b>Additional Notes : </b><span class="bookingAdditionalNotes">`+response.booking.importantNotes+`</span></h5>`;
                    }
                    if (response.booking.bookedby != null) {
                        var requestedName = response.booking.bookedby.name;
                    } else {
                        var requestedName = '';
                    }
                    rightHead += `<span class="left_amdimg">
                <img src="https://www.nursesgroupadmin.co.uk/storage/app/client/1646842964_7yetR4Yh40GloL1yoZi2CkLT16mvz17TwQoVKbYV.jpg">
                </span>
                <div class="right_amd_padd">
                    <h5><b>Booking Created by : </b><span class="bookingCreatedBy">` + requestedName + `</span></h5>
                    <h5><b>Booking created on : </b><span class="bookingStaffAssigned"></span></h5>
                    <h5><b>Remaining time to Start : </b><span class="remainingTimetoStart">` + timeDiff + ` Hrs</span></h5>
                </div>`;
                    $('.left_amd').html(leftHead);
                    $('.right_amd').html(rightHead);

                    $(".amendDetails").addClass('hidden');
                    $(".cancelDetails").addClass("hidden");
                    $(".amendButton").removeClass("btn-orange");
                    $(".amendButton").removeClass("hidden");
                    $(".cancelButton").removeClass("btn-red");

                    $(".amendSubmitButton").addClass('hidden');
                    $(".cancelSubmit").addClass('hidden');
                    $("#amendModal .cancelButton").removeClass("hidden");
                    $('#amendModal .amendSubmitButton').addClass("hidden");
                    $('#amendModal .amendNotes').attr('readonly', false);
                    $("#amendModal select[name=amendRequestedName]").attr('readonly', false);
                    $("#amendModal select[name=amendDetailsRequestedName]").attr('readonly', false);
                    $('#amendModal textarea[name=cancelNotes]').attr('readonly', false);

                    $('#amendModal .bookingCreatedBy').html(requestedName);

                    if (response?.booking?.created_at) {
                        $('#amendModal .bookingStaffAssigned').html(
                            moment(response.booking.created_at, "YYYY-MM-DD HH:mm:ss").format("DD-MM-YYYY HH:mm")
                        );
                    } else {
                        $('#amendModal .bookingStaffAssigned').html("N/A");
                        console.warn("created_at not found in response:", response);
                    }

                    $('#amendModal').modal('show');

                    if (response && response.booking && response.booking.unitStatus === 2) {
                        // Booking is already cancelled - hide all action buttons and show read-only cancellation details
                        $("#amendModal .cancelButton").addClass("hidden");
                        $("#amendModal .amendButton").addClass("hidden");
                        $('.amendActionButtons').addClass('hidden'); // Hide main action buttons area
                        $('#amendModal .amendDetails').addClass("hidden");
                        $('#amendModal .cancelDetails').addClass("hidden"); // Hide the cancellation form
                        
                        // Hide all form elements for cancelled bookings
                        $('#amendModal select[name=amendRequestedName]').closest('.form-group').addClass('hidden');
                        $('#amendModal .amendNotes').closest('.form-group').addClass('hidden');
                        $('#amendModal .cancelNotes').closest('.form-group').addClass('hidden');
                        $('#amendModal textarea[name=cancelPreview]').closest('.form-group').addClass('hidden');
                        $('#amendModal .cancelledNotesRequestedPerson').addClass('hidden');
                        
                        $(".amendSubmitButton").addClass('hidden');
                        $(".cancelSubmit").addClass('hidden');
                        
                        // Add a note indicating this booking is already cancelled with details
                        if (!$('.cancel-status-note').length) {
                            var cancelInfo = `
                                <div class="cancel-status-note alert alert-warning" style="margin: 10px 15px; font-size: 14px; padding: 15px;">
                                    <div style="text-align: center; margin-bottom: 15px;">
                                        <strong>This booking has been cancelled.</strong>
                                    </div>
                                    <div style="background: #fff; padding: 10px; border-radius: 5px; border-left: 4px solid #f0ad4e;">
                                        <strong>Cancellation Details:</strong><br>
                                        <strong>Requested by:</strong> ${response.booking.cancelRequestedBy || 'N/A'}<br>
                                        <strong>Reason:</strong> ${response.booking.cancelNotes || 'No reason provided'}<br>
                                        <strong>Date:</strong> ${response.booking.cancelDate || 'N/A'} ${response.booking.cancelTime || ''}
                                    </div>
                                </div>`;
                            $('.amendActionButtons').before(cancelInfo);
                        }
                    } else {
                        if (response.amend != null) {
                            // Show existing amend details but still allow cancel option
                            $('#amendModal .cancelDetails').addClass("hidden");
                            $('#amendModal select[name=amendDetailsRequestedName] select').val(requestedName);
                            $('#amendModal select[name=amendDetailsRequestedName]').attr('readonly', true)
                            $("#amendModal textarea[name=amendPreview]").val(response.amend.preview);
                            $("#amendModal textarea[name=amendDetailNotes]").val(response.amend.notes).attr("readonly", true);
                            $("#amendModal .amend-save").addClass('hidden');
                            
                            // Keep action buttons visible to allow cancel option
                            $(".amendActionButtons").removeClass("hidden");
                            $(".amendDetails").addClass("hidden");
                            
                            // Add a note indicating this booking has been amended
                            if (!$('.amend-status-note').length) {
                                $('.amendActionButtons').after('<div class="amend-status-note alert alert-info" style="margin: 10px 15px; font-size: 12px; padding: 8px 12px;"><strong>Note:</strong> This booking has been amended. You can still cancel if needed.</div>');
                            }
                        } else {
                            $('#amendModal input[name=amendDetailsRequestedName]').val('').attr("readonly", false);
                            $("#amendModal textarea[name=amendDetailNotes]").val('').attr("readonly", false);
                            $("#amendModal textarea[name=cancelPreview]").val("Hi Nurses Group, Please Cancel Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview + " because of ");
                            $("#amendModal textarea[name=amendPreview]")
                            .val("Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " 
                                + response.booking.category.name + ", " + preview  );
                            $("#amendModal input[name=cancelPreviewHidden]").val("Hi Nurses Group, Please Cancel Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview + " because of ");
                            $("#amendModal input[name=amendPreviewHidden]").val("Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview);
                            $("#amendModal .amend-save").addClass('hidden');
                            $(".amendActionButtons").removeClass("hidden");
                            $(".amendDetails").addClass("hidden");
                        }
                    }

                    $('#amendModal input[name=bookingId]').val(response.booking.bookingId);
                    $('#amendModal .bookId').html(response.booking.bookingId);
                    $('#amendModal .bookingDate').html(response.booking.day);
                    $('#amendModal .unitName').html(response.booking.unit.name);
                    $('#amendModal .categoryName').html(response.booking.category.name);
                    $('#amendModal .shiftName').html(shift);
                    $('#amendModal .bookingAdditionalNotes').html(additionalNotes);

                }
            },
            error: function (xhr, status, error) {
                console.error("AJAX Error:", status, error);
                alert("An error occurred while fetching booking details.");
                $("#global-loader").hide();
            }
        });
    });
    $('#amendModal').on('shown.bs.modal', function () {
        function updateAmendPreview() {
            const basePreview = $("textarea[name=amendPreview]").val().split('\n')[0]; // first line only
            const whatToAmend = $("select.cancelNotesamend option:selected").val() || "N/A";
            const reason = $("select[name=reasonforammend] option:selected").val() || "N/A";
            const notes = $("textarea[name=additional_text]").val() || "N/A";
            const requestedBy = $("select[name=amendDetailsRequestedName] option:selected").text()
                             || $("input[name=cancelRequestedName]").val() || "N/A";
        
            const amendDetails = `Request for ${whatToAmend}, ${reason}, Notes: ${notes}, Requested By: ${requestedBy}`;
            const fullPreview = `${basePreview}\n${amendDetails}`;
        
            $("textarea[name=amendPreview]").val(fullPreview);
        }

        function updateCancelPreview() {
            const basePreview = $("input[name=cancelPreviewHidden]").val() || "";
            const cancelReason = $("select[name=cancelNotes] option:selected").text() || "";
            const reasonDetail = $("#reasonforammend option:selected").text() || "";
            const notes = $("textarea[name=cancelDetailNotes]").val() || "";
            const requestedBy = $("select[name=cancelRequestedName] option:selected").text() || "N/A";
        
            let cancelDetails = basePreview;
            if (cancelReason && cancelReason !== "Select") {
                cancelDetails += ", " + cancelReason;
            }
            if (reasonDetail && reasonDetail !== "Select") {
                cancelDetails += ", " + reasonDetail;
            }
            if (notes) {
                cancelDetails += ", Notes: " + notes;
            }
            cancelDetails += ", Thank You, " + requestedBy;
        
            $("textarea[name=cancelPreview]").val(cancelDetails);
        }
    
        // Trigger preview on all input changes for amendments
        $("select.cancelNotesamend").off().on('change', updateAmendPreview);
        $("select[name=reasonforammend], #reasonforammend").off().on('change', function() {
            updateAmendPreview();
            updateCancelPreview(); // Also update cancel preview as it uses the same dropdown
        });
        $("textarea[name=additional_text]").off().on('keyup', updateAmendPreview);
        $("select[name=amendDetailsRequestedName]").off().on('change', updateAmendPreview);

        // Trigger preview on all input changes for cancellations
        $("select[name=cancelNotes]").off().on('change', updateCancelPreview);
        $("#reasonforammend").off().on('change', updateCancelPreview);
        $("textarea[name=cancelDetailNotes]").off().on('keyup', updateCancelPreview);
        $("select[name=cancelRequestedName]").off().on('change', updateCancelPreview);
    
        // Run once initially
        updateAmendPreview();
        updateCancelPreview();
    });

    /*    --------------   Amend   start--------             */
    $('.doAmend').click(function () {
        $('.amendDetails').removeClass('hidden');
        $('.previewAmendSubmit').removeClass('hidden');
        $('.cancelDetails').addClass('hidden');
        $('.amendActionButtons').addClass('hidden'); // Hide action buttons when amending
        $('.amend-status-note').hide(); // Hide the status note during amend
        $(".notesRequestedPerson").removeClass("hidden");
    });
    $('.amendClose,.close').click(function () {
        $('#unableToCancelModal').hide();
        // Reset modal state when closing
        $('.amendDetails').addClass('hidden');
        $('.cancelDetails').addClass('hidden');
        $('.amendActionButtons').removeClass('hidden');
        $('.amend-status-note, .cancel-status-note').remove(); // Remove status notes when closing
    });

    $(".amendNotes").on('keyup', function (event) {
        var data = $(this).val();
        var textPreview = $("input[name=amendPreviewHidden]").val();
        var amendRequestedName = $("select[name=amendRequestedName] option:selected").text();
        $("textarea[name=amendPreview]").val(textPreview + ", " + data + ", Thank You, " + amendRequestedName);
    });

    $("select[name=amendRequestedName]").on('change', function (event) {
        var data = $("textarea[name=amendDetailNotes]").val();
        var textPreview = $("input[name=amendPreviewHidden]").val();
        var amendRequestedName = $("select[name=amendRequestedName] option:selected").text();
        $("textarea[name=amendPreview]").val(textPreview + ", " + data + ", Thank You, " + amendRequestedName);
    });


    $('.previewAmendSubmit').click(function () {
        // Get base booking details from hidden input
        var basePreview = $('#amendModal input[name="amendPreviewHidden"]').val();
    
        // Collect new values
        var cancelNote = $('#amendModal .cancelNotesamend').val();
        var reasonForAmend = $('#amendModal select[name="reasonforammend"]').val();
        var additionalText = $('#amendModal textarea[name="additional_text"]').val();
        var requestedBy = $('#amendModal input[name="cancelRequestedName"]').val();
    
        // Create complete preview (not appending)
        var finalPreview = basePreview + ", Request for " + (cancelNote || 'N/A') + 
                          ", " + (reasonForAmend || 'N/A') + 
                          ", Notes: " + (additionalText || 'N/A') + 
                          ", Requested By: " + (requestedBy || 'N/A');
    
        // Set the updated content in the textarea and hidden input
        $('#amendModal textarea[name="amendPreview"]').val(finalPreview);
        $('#amendModal input[name="amendPreviewHidden"]').val(finalPreview);
    
        // Proceed with original modal logic
        $('#amendModal').modal('hide');
        $("#bookingAmendConfirmMsgModal").modal('show');
        $("#bookingAmendConfirmMsgModal .modal-header").removeClass('bgRed');
        $("#bookingAmendConfirmMsgModal .modal-header h4 b").text("BOOKING AMEND CONFIRMATION");
        $(".amendConfirmDetails").removeClass("hidden");
        $(".cancelConfirmDetails").addClass("hidden");
        $(".noticeMsg").addClass("hidden");
        $("#bookingAmendConfirmMsgModal .confirm").addClass("amend-save");
        $("#bookingAmendConfirmMsgModal .confirm").text("Confirm Amend").removeClass("cancelConfirm");
        $("#bookingAmendConfirmMsgModal .confimationDetials").removeClass('hidden');
        $(".methodAction").html("Amend Confirm Summary");
    
        var shiftDetails = $("#amendModal .smallHdr").text();
        $(".shiftDetails").html(shiftDetails);
    
        var amendRequestedName = $("#amendModal select[name=amendDetailsRequestedName] option:selected").text();
        var amendRequestedNameID = $("#amendModal select[name=amendDetailsRequestedName] option:selected").val();
        $(".reqPersonAmend").text(amendRequestedName);
        $(".reqPersonIDAmend").html(amendRequestedNameID);
    
        var notes = $('#amendModal .bookingAdditionalNotes').text();
        if (notes) {
            $(".confirm_amendnote").removeClass("hidden");
            $(".notesAmend").html(notes);
        } else {
            $(".confirm_amendnote").addClass("hidden");
        }
    
        // Display the final preview
        $(".previewAmend").html(finalPreview);
    });
    

    $(document).on('click', '.amend-save', function (e) {
        $(".amend-save").prop("disabled", "true");
        var bookId = $(this).attr('bookid');
        var action = $(this).attr('amend-action');
        var token = $(this).attr('token');
        var previewAmend = $('#bookingAmendConfirmMsgModal .previewAmend').text();
        var amendDetailNotes = $('#amendModal textarea[name=amendDetailNotes]').val();
        var reqPersonIDAmend = $('#bookingAmendConfirmMsgModal .reqPersonIDAmend').text();
        $.ajax({
            type: 'POST',
            url: action,
            data: {
                "_token": token,
                "amendPreview": previewAmend,
                "amendNotes": amendDetailNotes,
                "bookingId": bookId,
                "amendRequestedName": reqPersonIDAmend,
            },
            beforeSend: function () {
                $("#global-loader").show();
            },
            complete: function () {
                $('#bookingAmendModal').modal('hide');
            },
            success: function (response) {
                $("#global-loader").hide();
                location.reload();
            }
        });
    });
    /*    --------------   Amend   end--------             */

    /*    --------------   Cancellation   start--------             */
    $('.doCancel').click(function () {
        $('.cancelDetails').removeClass('hidden');
        $('.amendDetails').addClass('hidden');
        $('.amendActionButtons').addClass('hidden'); // Hide action buttons when cancelling
        $('.amend-status-note').hide(); // Hide the status note during cancellation
        $(".cancelledNotesRequestedPerson").removeClass("hidden");
    })

    // Remove old individual event handlers as they're now handled in the modal shown event
    // $(".cancelNotes").on('change', function (event) { ... });
    // $(".reasonforammend").on('change', function (event) { ... });
    // $("select[name=cancelRequestedName]").on('change', function (event) { ... });

    $(".confirmCancelSubmit").on('click', function () {
        $('#amendModal').modal('hide');
        $("#bookingAmendConfirmMsgModal").modal('show');
        $("#bookingAmendConfirmMsgModal .modal-header").addClass('bgRed');
        $("#bookingAmendConfirmMsgModal .modal-header h4 b").text("BOOKING CANCELLATION CONFIRMATION");
        $(".amendConfirmDetails").addClass("hidden");
        $(".cancelConfirmDetails").removeClass("hidden");
        $(".noticeMsg").addClass("hidden");

        $("#bookingAmendConfirmMsgModal .confirm").removeClass("amend-save");
        $("#bookingAmendConfirmMsgModal .confirm").text("Confirm Cancellation").addClass("cancelConfirm");
        $("#bookingAmendConfirmMsgModal .confimationDetials").removeClass('hidden');
        $(".methodAction").html("Cancellation Summary");
        var bookingCreatedBy = $('#amendModal .bookingCreatedBy').text();
        var shiftDetails = $("#amendModal .smallHdr").text();
        var bookingStaffAssigned = $('#amendModal .bookingStaffAssigned').text();
        var remainingTimetoStart = $('#amendModal .remainingTimetoStart').text();
        var cancelNotes = $('#amendModal textarea[name=cancelNotes]').val();
        var amendPreview = $('#amendModal textarea[name=cancelPreview]').val();
        var notes = $('#amendModal .bookingAdditionalNotes').text();
        var bookingID = $('#amendModal input[name=bookingId]').val();
        var amendRequestedName = $(".amendRequestedName option:selected").text();
        var amendRequestedNameID = $(".amendRequestedName option:selected").val();

        if (notes) {
            $(".confirm_cancelnote").removeClass("hidden");
            $(".notes").html(notes);
        } else {
            $(".confirm_cancelnote").addClass("hidden");

        }
        $(".shiftDetails").html(shiftDetails);
        $(".createdBy").html(bookingCreatedBy);
        $(".assignedStaff").html(bookingStaffAssigned);
        $(".remainingTime").html(remainingTimetoStart);
        $(".reqPerson").html(amendRequestedName);
        $(".reqPersonID").html(amendRequestedNameID);
        $(".notes").html(notes);
        $(".previewCancel").text(amendPreview);
        $(".bookingId").html(bookingID);
        $(".cancelNotes").html(cancelNotes);

    });


    $(document).on('click', '.cancelConfirm', function (e) {
        var bookId = $(this).attr('bookid');
        var action = $(this).attr('cancel-action');
        var token = $(this).attr('token');
        $.ajax({
            type: 'POST',
            url: action,
            data: {
                "_token": token,
                "bookingId": bookId,
                'notes': $('.cancelNotes').val(),
                'requestedName': $("select[name=amendRequestedName] option:selected").text(),
            },
            beforeSend: function () { $("#global-loader").show(); },
            success: function (response) {
                window.location.reload();
                $("#global-loader").hide();
            }
        });
    });

    $(document).on('click', '.amendModal', function (e) {

        $('#amendModal').modal('hide');

        return false;
    });
    $(document).on('click', '.bookingAmendConfirmMsgModal', function (e) {

        $('#bookingAmendConfirmMsgModal').modal('hide');

        return false;
    });


    /*    --------------   Cancellation   end--------             */

    $('.log_button').click(function () {
        var dataUrl = $("#BookingUnitLogBook").attr('get-url');
        var headHtml = `<span class=""> ` + $(this).attr('unit') + `</span> <span class=""> ` + $(this).attr('category') + `</span>  <span class=""> ` + $(this).attr('staff') + `</span> | <span class=""> ` + $(this).attr('date') + `</span> | <span class=""> ` + $(this).attr('shift') + `: ` + $(this).attr('start') + ` - ` + $(this).attr('end') + ` </span> `;
        $('.chat_contents').html('');
        $('#BookingUnitLogBook .TopHeadTxt').html(headHtml);

        var token = $("#BookingUnitLogBook").attr('token');
        var booking_id = $(this).attr('booking_id');
        $('#unit_id').val($(this).attr('unit_id'));
        $('#unit_name').val($(this).attr('unit'));
        $('#booking_id').val($(this).attr('booking_id'));
        $('#BookingUnitLogBook').show();
        if (booking_id != "") {
            $.ajax({
                type: 'POST',
                url: dataUrl,
                data: {
                    _token: token,
                    booking_id: booking_id,
                },
                success: function (response) {
                    var datas = response.data;
                    var chat_list = ``;

                    for (var i = 0; i < datas.length; i++) {
                        // Inline style check for color value "one"
                        var inlineStyle = ''; 
                        if (datas[i].color == 1) { 
                            inlineStyle = 'background-color: #f2f28a !important;';  // __Set the inline style directly (changed line)__
                        }
                        if (datas[i].type == 1) {
                            chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                                      <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                                      </div>
                                      <div class="item-title  colorchange_chat"> <span class="LuftTitle"> ` + datas[i].author_name + `(<b>${datas[i].user?.role?.name}</b>) : </span> ` + datas[i].content + `</div>
                                      <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` + datas[i].date_human + `</div>
                                    </li>`;
                        } else {
                            chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                                  <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                                  </div>
                                  <div class="item-title"><span class="NsgOrange"> Nurses Group : </span> ` + datas[i].content + `</div>
                                  <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` + datas[i].date_human + `</div>
                                </li>`;
                        }
                    }
                    $('.chat_contents').html(chat_list);
                }
            });
        }



    });

    $(document).on('submit', '#chat_form', function (e) {
        e.preventDefault();
        var dataUrl = $(this).attr('fetch');
        var token = $(this).attr('token');
        var content = $("#content").val();
        if(content == '') {
            return false;
        }
        var author_id = $("#author_id").val();
        var booking_id = $("#booking_id").val();
        var unit_id = $("#unit_id").val();
        var unitName = $("#BookingUnitLogBook").attr('unit-name');
        var html = `<li class="tl-item" ng-repeat="item in retailer_history">
                  <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                  </div>
                  <div class="item-title"> <span class="LuftTitle"> ` + unitName + `: </span> ` + content + ` </div>
                  <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> Just Now </div>
                </li>`;
        $('#BookingUnitLogBook ul').prepend(html);
        $('#BookingUnitLogBook #content').val('');
        $.ajax({
            type: "POST",
            url: dataUrl,
            data: {
                '_token': token,
                'unit_id': unit_id,
                'booking_id': booking_id,
                'author': author_id,
                'content': content
            },
            success: function (data) { 
                // Refresh the page after successful chat save
                setTimeout(function() {
                    location.reload();
                }, 1000); 
            },

        });

    });

    $('.BookingUnitLogBook').click(function (e) {
        $("#BookingUnitLogBook").hide(); return false;
    });

    $(document).on('change', '.cancelNotes', function (e) {
        
        let value  = $(this).val();
        let option = $(this).val();

        switch (value) {
            case 'Change shift':
                                 option=    '<option value="">Select</option>'+
                                    '<option value="Change the shifts to Early">Change the shifts to Early</option>'+
                                    '<option value="Change the shifts to Late">Change the shifts to Late</option>'+
                                    '<option value="Change the shifts to Longday">Change the shifts to Longday</option>'+
                                    '<option value="Change the shifts to Night">Change the shifts to Night</option>';
                break;
            case 'Change date':
                                    option=    '<option value="">Select</option>'+
                                    '<option value="Change the same shifts to different date">Change the same shifts to different date</option>';
                break;
            case 'Change start / end time':

                                                             option=    '<option value="">Select</option>'+
                                    '<option value="Early finish of the shift">Early finish of the shift</option>'+
                                    '<option value="End shift later">End shift later</option>'+
                                    '<option value="Late start of the shift">Late start of the shift</option>'+
                                    '<option value="Start early than standard time">Start early than standard time</option>'+
                                    '<option value="Change staff category">Change staff category</option>';
                                                 
                break;
            case 'Change category':
                                                 option=    '<option value="">Select</option>'+
                                    '<option value="Change to an RGN">Change to an RGN</option>'+
                                    '<option value="Change to an HCA">Change to an HCA</option>'+
                                    '<option value="Change to an SHCA">Change to an SHCA</option>'+
                                    '<option value="Change to an Other">Change to an Other</option>';
                break;
            case 'Change staff':
                                                 option=    '<option value="">Select</option>'+
                                    '<option value="Need another gender">Need another gender</option>'+
                                    '<option value="Need different skill set">Need different skill set</option>'+
                                    '<option value="Require special skills">Require special skills</option>'
                                   ;
                break;
            case 'Change reason for booking':
                                                 option=    '<option value="">Select</option>'+
                                    '<option value="Staff Sickness">Staff Sickness</option>'+
                                    '<option value="Holiday cover">Holiday cover</option>'+
                                    '<option value="Vacant Position">Vacant Position</option>'+
                                    '<option value="New Resident admission">New Resident admission</option>'+
                                    '<option value="Change staff category">Change staff category</option>'+
                                    '<option value="1 to 1 care">1 to 1 care</option>'+
                                    '<option value="Extra staff requirement">Extra staff requirement</option>'+
                                    '<option value="Staff training day">Staff training day</option>';
                break;
            case 'Do not want to disclose':
                                                 option=    '<option value="">Select</option>'+
                                    '<option value="Do not want to disclose">Do not want to disclose</option>';
                break;
              
                case 'Change staff category':
                                                     option=    '<option value="">Select</option>'+
                                    '<option value="Change to an RGN">Change to an RGN</option>'+
                                    '<option value="Change to a HCA">Change to a HCA</option>'+
                                    '<option value="Change to a SHCA">Change to a SHCA</option>'+
                                    '<option value="Change to other">Change to other</option>';
                break; 
                case 'Other':
                                                     option=    '<option value="Other">Other</option>';
                break;    
            default:
                break;
        }           
                    $('#reasonforammend').empty();   
                    $('#reasonforammend').append(option);                   
       
    });
    $('#showAllShifts').click(function() {
        $('#filtercancelled').click();
        return false;
    });

});
