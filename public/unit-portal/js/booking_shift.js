$(document).ready(function () {
    // $(".datepicker_add").datepicker({
    //     changeMonth: true,
    //     changeYear: true,
    //     numberOfMonths: 3,
    //     dateFormat: 'dd-mm-yy DD',
    //     firstDay: 1,
    //     minDate: 0
    // });
    /*  $('select').select2({ minimumResultsForSearch: -1, placeholder: 'Select an Option' }); */
    // $('.clockpicker').clockpicker({
    //     placement: 'bottom',
    //     align: 'left',
    //     autoclose: true,
    // });
        initializeDatePicker('.datepicker_add');

    // Initially hide all urgent booking checkboxes
    $('.BxUrgent, .UrgBkng').hide();

    $('#showAllShifts').click(function() {
        $('#filtercancelled').click();
        return false;
  });
        $('#showAllShiftsArchive').on('change', function () {
            const isChecked = $(this).is(':checked');
            const urlParams = new URLSearchParams(window.location.search);

            if (isChecked) {
                urlParams.set('status', '7'); // Show all shifts
            } else {
                urlParams.delete('status'); // Remove the status filter
            }

            window.location.search = urlParams.toString();
        });
});


$(document).on('click', '.closeBtn', function () {
    $(this).closest('.full_width').remove();
})

$('.add_newrow').click(function () {

    var shiftdata = $('.shiftSel').html();
    var $temp = $('<div>' + shiftdata + '</div>');
    $temp.find('[data-select2-id="12"]').removeAttr('data-select2-id');
    shiftdata = $temp.html();
    console.log(shiftdata);
    var categorydata = $('.categorySwitch').html();
    console.log(categorydata);
    // Get unit data from existing select
    var unitdata = $('select[name="unitId[]"]').first().html();
    console.log('Unit data:', unitdata);
    var loggedInManager = $('input[name="requestedBy[]"]').first().val();

    var html = `<div class="full_width" style=";margin-top: 10px;">

          <div class="fby_one w10Percntge">
            <input type="text" name="date[]" class="txt_bx datepicker_add" >
          </div>
          <div class="fby_one w10Percntge">
            <select name="shift[]" class="txt_bx shiftSel shiftvalue">`;
    html += shiftdata;
    html += `</select>
          </div>
          <div class="fby_one w10Percntge">
            <select name="category[]" class="txt_bx categorySwitch">`;

    html += categorydata;

    html += `</select>
          </div>
          <div class="sby_one">
            <select name="numbers[]" class="txt_bx">`;
    for (var i = 1; i < 11; i++) {
        html += `<option value="` + i + `">` + i + `</option>`;
    }
    html += `</select>
          </div>
          <div class="fby_one w10Percntge">
            <input type="text" name="start_time[]" class="txt_bx startTime clockpicker">
          </div>
          <div class="fby_one w10Percntge">
            <input type="text" name="end_time[]" class="txt_bx endTime clockpicker">
          </div>
          <div class="fby_one" style="width: 13%;">
            <input type="text" value="` + loggedInManager + `" name="requestedBy[]" class="txt_bx" readonly/>
          </div>
          <div class="fby_one" style="width: 9%">
            <select name="reasonBooking[]" class="txt_bx" >
              <option value="1">Staff Sickness</option>
              <option value="2">Holiday cover</option>
              <option value="3">Vacant Position</option>
              <option value="4">New Resident admission</option>
              <option value="5">1 to 1 care</option>
              <option value="6">Extra staff requirement</option>
              <option value="7">Staff training day</option>
            </select>
          </div>
          <div class="fby_one w10Percntge">
            <select name="unitId[]" class="txt_bx unitId">`;
    
    // Add unit options
    if (unitdata) {
        html += unitdata;
    } else {
        html += `<option value="">Select Unit</option>`;
    }
    
    html += `</select>
          </div>
          <div class="fby_one">
            <input type="text" name="impNotes[]" class="txt_bx impNotes">
          </div>
          <div class="bx_margin BxUrgent" style="display: none;">
            <label>
              <input type="checkbox" name="urgent[]" class="urgentCheckbox" />
              UB
            </label>
          </div>
          <div class="fby_one" style="width: auto;float: left;font-size: 16px;padding: 6px;margin-left: 4px;"><button class="closeBtn">X</button></div>
      </div>`;
    $(".addnew_button").before(html);
    $(".addBooksNewDivs .full_width:last-child()").find('.datepicker_add').datepicker({
        changeMonth: true,
        changeYear: true,
        numberOfMonths: 3,
        dateFormat: 'dd-mm-yy DD',
        firstDay: 1,
        minDate: 0 
    });

    //$(".addBooksNewDivs .full_width:last-child()").find('select').select2({ minimumResultsForSearch: -1, placeholder: 'Select an Option' });
    $(".addBooksNewDivs .full_width:last-child()").find('.clockpicker').clockpicker({
        placement: 'bottom',
        align: 'left',
        autoclose: true,
    });

    // Initialize urgent booking checkbox as hidden for new rows
    $(".addBooksNewDivs .full_width:last-child()").find('.BxUrgent').hide();


});
// Function to initialize datepicker
function initializeDatePicker(selector) {
    $(selector).datepicker({
        changeMonth: true,
        changeYear: true,
        numberOfMonths: 3,
        dateFormat: 'dd-mm-yy DD',
        firstDay: 1,
        minDate: 0
    });
}

// Use event delegation for datepicker on dynamically added elements
$(document).on('focus', '.datepicker_add:not(.hasDatepicker)', function() {
    initializeDatePicker(this);
    $(this).datepicker('show');
});

// Use event delegation for clockpicker on dynamically added elements
$(document).on('focus', '.clockpicker:not(.clockpicker-input)', function() {
    $(this).clockpicker({
        placement: 'bottom',
        align: 'left',
        autoclose: true,
    });
    $(this).clockpicker('show');
});

$(document).on('change', ".categorySwitch,.shiftSel", function () {
    var fetch = $('.addNewBookingDiv').attr('cost-fetch');
    var token = $('.addNewBookingDiv').attr('token');
    var shift = $(this).closest('.full_width').find('.shiftSel').val();
    var category = $(this).closest('.full_width').find('.categorySwitch').val();
    var element = $(this);

    if (category != "" && shift != "") {
        $.ajax({
            type: 'POST',
            url: fetch,
            data: {
                _token: token,
                date: shift,
                shift: shift,
                category: category,
            },
            success: function (response) {
                element.closest('.full_width').find(".startTime").val(response.start);
                element.closest('.full_width').find(".endTime").val(response.end);
                element.closest('.full_width').find(".costBox").attr('price', response.cost);
                
                // Check urgency after time is updated
                checkUrgency(element.closest('.full_width'), response.start);
            }
        });
    }
});
$('.add_save_new_booking').click(function () {
    $("#global-loader").html('<h2 class="LoadCL">Loading...Please wait..</h2>').show();
    
    // Debug: Count rows
    var rowCount = $('.addBooksNewDivs .full_width').length;
    console.log('Total rows found:', rowCount);
    
    // Collect data from all rows using a more specific approach
    var date = [];
    var shift = [];
    var category = [];
    var numbers = [];
    var startTimes = [];
    var endTimes = [];
    var requestedBy = [];
    var reason = [];
    var impNotes = [];
    var unitId = [];
    var urgent = []; // Add urgent array
    
    // Iterate through each row individually
    $('.addBooksNewDivs .full_width').each(function(index) {
        var $row = $(this);
        
        var dateVal = $row.find("input[name='date[]']").val() || '';
        var shiftVal = $row.find("select[name='shift[]']").val() || '';
        var categoryVal = $row.find("select[name='category[]']").val() || '';
        var numbersVal = $row.find("select[name='numbers[]']").val() || '';
        var startTimeVal = $row.find("input[name='start_time[]']").val() || '';
        var endTimeVal = $row.find("input[name='end_time[]']").val() || '';
        var requestedByVal = $row.find("input[name='requestedBy[]']").val() || '';
        var reasonVal = $row.find("select[name='reasonBooking[]']").val() || '';
        var impNotesVal = $row.find("input[name='impNotes[]']").val() || '';
        var unitIdVal = $row.find("select[name='unitId[]']").val() || '';
        var urgentVal = $row.find("input[name='urgent[]']").is(':checked') ? 1 : 0; // Add urgent value
        
        console.log('Row ' + (index + 1) + ':', {
            date: dateVal,
            shift: shiftVal,
            category: categoryVal,
            numbers: numbersVal,
            startTime: startTimeVal,
            endTime: endTimeVal,
            requestedBy: requestedByVal,
            reason: reasonVal,
            impNotes: impNotesVal,
            unitId: unitIdVal,
            urgent: urgentVal // Add to log
        });
        
        // Add values to arrays
        date.push(dateVal);
        shift.push(shiftVal);
        category.push(categoryVal);
        numbers.push(numbersVal);
        startTimes.push(startTimeVal);
        endTimes.push(endTimeVal);
        requestedBy.push(requestedByVal);
        reason.push(reasonVal);
        impNotes.push(impNotesVal);
        unitId.push(unitIdVal);
        urgent.push(urgentVal); // Add urgent to arrays
    });

    console.log('Final arrays:', {
        date: date,
        shift: shift,
        category: category,
        numbers: numbers,
        startTimes: startTimes,
        endTimes: endTimes,
        requestedBy: requestedBy,
        reason: reason,
        impNotes: impNotes,
        unitId: unitId,
        urgent: urgent
    });

    var url = $(this).attr('url');
    var token = $(this).attr('token');

    $.ajax({
        type: 'POST',
        url: url,
        data: {
            _token: token,
            date: date,
            shift: shift,
            category: category,
            numbers: numbers,
            startTimes: startTimes,
            endTimes: endTimes,
            requestedBy: requestedBy,
            reason: reason,
            impNotes: impNotes,
            unitId: unitId,
            urgent: urgent
        },
        success: function (response) {
            console.log('Server response:', response);
            location.reload();
        },
        error: function(xhr, status, error) {
            console.error('AJAX Error:', error);
            console.error('Response Text:', xhr.responseText);
            $("#global-loader").hide();
            alert('Error occurred while saving. Please check console for details.');
        }
    });
});

$(document).on("click", ".add_new_booking", function () {

    $('#newShiftModel').modal();
});
$('.log_button').click(function () {
    var dataUrl = $("#BookingUnitLogBook").attr('get-url');
    var headHtml = `<span class=""> ` + $(this).attr('unit') + `</span> <span class=""> ` + $(this).attr('category') + `</span>  <span class=""> ` + $(this).attr('staff') + `</span> | <span class=""> ` + $(this).attr('date') + `</span> | <span class=""> ` + $(this).attr('shift') + `: ` + $(this).attr('start') + ` - ` + $(this).attr('end') + ` </span> `;
    $('.chat_contents').html('');
    $('#BookingUnitLogBook .TopHeadTxt').html(headHtml);

    var token = $("#BookingUnitLogBook").attr('token');
    var booking_id = $(this).attr('booking_id');
    $('#unit_id').val($(this).attr('unit_id'));
    $('#unit_name').val($(this).attr('unit'));
    $('#booking_id').val($(this).attr('booking_id'));
    $('#BookingUnitLogBook').show();
    if (booking_id != "") {
        $.ajax({
            type: 'POST',
            url: dataUrl,
            data: {
                _token: token,
                booking_id: booking_id,
            },
            success: function (response) {
                var datas = response.data;
                var chat_list = ``;

                for (var i = 0; i < datas.length; i++) {
                    // Inline style check for color value "one"
                    var inlineStyle = ''; 
                    if (datas[i].color == 1) { 
                        inlineStyle = 'background-color: yellow !important;';  // __Set the inline style directly (changed line)__
                    }
                    if (datas[i].type == 1) {
                        chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                                  <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                                  </div>
                                  <div class="item-title colorchange_chat"> <span class="LuftTitle"> ` + datas[i].unit_name + ` : </span> ` + datas[i].content + `</div>
                                  <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` + datas[i].date_human + `</div>
                                </li>`;
                    } else {
                        chat_list += `<li class="tl-item" ng-repeat="item in retailer_history" style="${inlineStyle}">
                              <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
                              </div>
                              <div class="item-title"><span class="NsgOrange"> Nurses Group : </span> ` + datas[i].content + `</div>
                              <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> ` + datas[i].date_human + `</div>
                            </li>`;
                    }
                }
                $('.chat_contents').html(chat_list);
            }
        });
    }



});

$(document).on('submit', '#chat_form', function (e) {
    e.preventDefault();
    var dataUrl = $(this).attr('fetch');
    var token = $(this).attr('token');
    var content = $("#content").val();
    var author_id = $("#author_id").val();
    var booking_id = $("#booking_id").val();
    var unit_id = $("#unit_id").val();
    var unitName = $("#BookingUnitLogBook").attr('unit-name');
    var html = `<li class="tl-item" ng-repeat="item in retailer_history">
              <div class="timestamp"> <i class="fa fa-comments" aria-hidden="true"></i>
              </div>
              <div class="item-title"> <span class="LuftTitle"> ` + unitName + `: </span> ` + content + ` </div>
              <div class="item-detail"><i class="fa fa-clock-o" aria-hidden="true"></i> Just Now </div>
            </li>`;
    $('#BookingUnitLogBook ul').prepend(html);
    $('#BookingUnitLogBook #content').val('');
    $.ajax({
        type: "POST",
        url: dataUrl,
        data: {
            '_token': token,
            'unit_id': unit_id,
            'booking_id': booking_id,
            'author': author_id,
            'content': content
        },
        success: function (data) { },
    });

});

$('.amendNow').click(function () {
    var bookId = $(this).attr('bookid');
    var fetch = $(this).attr('fetch');
    var token = $(this).attr('token');
    var timeDiff = $(this).attr('timeDiff');
    $('.cancelConfirm').attr('bookid', bookId);
    $.ajax({
        type: 'POST',
        url: fetch,
        data: {
            "_token": token,
            "bookingId": bookId,
        },
        beforeSend: function () { $("#global-loader").show(); },
        success: function (response) {
            if (response.booking.staff.forname != '') {
                var shift = response.booking.shift.name + " | " + response.booking.staff.forname + " " + response.booking.staff.surname;
                var preview = response.booking.shift.name + ", " + response.booking.staff.forname + " " + response.booking.staff.surname;
            } else {
                var shift = response.booking.shift.name;
                var preview = response.booking.shift.name;
            }
            if (response.booking.requestedby != null) {
                var requestedName = response.booking.requestedby.fullName;
            } else {
                var requestedName = '';
            }

            if (response.booking.comments == null) {
                var additionalNotes = 'Nil';
            } else {
                var additionalNotes = response.booking.comments;
            }
            var startDate = response.booking.date + " " + response.start_time;
            var nowTime = moment(response.now);
            var startBookDate = moment(startDate);
            var duration = moment.duration(startBookDate.diff(nowTime)).asHours();
            var rightHead = ``;
            var leftHead = `<span class="left_amdimg">
                <img src="` + response.booking.unit.image_link + `">
             </span>`;

            if (duration < 24) {
                leftHead += `<ul class="smallHdr D_inline">
                <li class="unitName"><b>` + response.booking.unit.name + `</b></li> |
                <li class="bookId">` + response.booking.bookingId + `</li> |
                <li class="bookingDate">` + response.booking.booking_date_human + `</li> |
                <li class="categoryName">` + response.booking.unit.alias + ` |  ` + response.booking.category.name + ` |  ` + response.booking.shift.name + `</li>
             </ul>`;
                $('.bookDataHtml').html(leftHead);
                $('#unableToCancelModal').modal();
            } else {
                leftHead += `<ul class="smallHdr">
                <li class="unitName"><b>` + response.booking.unit.name + `</b></li>
                <li class="bookId">` + response.booking.bookingId + `</li>
                <li class="bookingDate">` + response.booking.booking_date_human + `</li>
                <li class="categoryName">` + response.booking.unit.alias + ` |  ` + response.booking.category.name + ` |  ` + response.booking.shift.name + `</li>
             </ul>`;
                if (response.booking.importantNotes) {
                    leftHead += `<h5 class="add_not"><b>Additional Notes : </b><span class="bookingAdditionalNotes">` + response.booking.importantNotes + `</span></h5>`;
                }
                if (response.booking.requestedbyrelation != null) {
                    var requestedName = response.booking.requestedbyrelation.fullName;
                } else {
                    var requestedName = '';
                }
                rightHead += `<span class="left_amdimg">
             <img src="https://nursesgroupadmin.co.uk/app/storage/app/client/1601055834_jZI5bp9jWQo2sjj3EVEMlCeiL9MQ6oMak8WXdrtl.jpg">
             </span>
             <div class="right_amd_padd">
                <h5><b>Booking Created by : </b><span class="bookingCreatedBy">` + requestedName + `</span></h5>
                <h5><b>Booking created on : </b><span class="bookingStaffAssigned"></span></h5>
                <h5><b>Remaining time to Start : </b><span class="remainingTimetoStart">` + timeDiff + ` Hrs</span></h5>
             </div>`;
                $('.left_amd').html(leftHead);
                $('.right_amd').html(rightHead);



                if (response.booking.unitStatus == 2) {
                    /* $("#amendModal .doAmend").addClass("hidden");
                     $("#amendModal .doCancel").addClass("hidden");*/
                    $('#amendModal .amendDetails').addClass("hidden");
                    $('#amendModal .cancelDetails').removeClass("hidden");
                    $('#amendModal .confirmCancelSubmit').addClass("hidden");
                    $(".amendActionButtons").addClass("hidden");
                    $(".cancelledNotesRequestedPerson").addClass("hidden");
                    $('#amendModal select[name=amendRequestedName]').val(response.booking.cancelRequestedBy).attr('readonly', true);
                    $('#amendModal .amendNotes').val(response.booking.cancelNotes).attr('readonly', true);
                    $('#amendModal .cancelNotes').val(response.booking.cancelNotes).attr('readonly', true);
                    $("#amendModal textarea[name=cancelPreview]").val("Hi Nurses Group, Please Cancel Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview + " because of " + response.booking.cancelNotes + ", Thank You ");

                    $(".amendSubmit").addClass('hidden');
                    $(".cancelSubmit").addClass('hidden');

                } else {
                    if (response.amend != null) {
                        $('#amendModal .cancelDetails').addClass("hidden");
                        $('#amendModal .previewAmendSubmit').addClass("hidden");
                        $(".cancelSubmit").addClass("hidden");
                        $(".notesRequestedPerson").addClass("hidden");
                        $("#amendModal textarea[name=amendPreview]").val(response.amend.preview);
                        $("#amendModal textarea[name=amendDetailNotes]").val(response.amend.notes).attr("readonly", true);
                        //$("#amendModal .amend-save").addClass('hidden');
                        $(".amendActionButtons").addClass("hidden");
                        $(".amendDetails").removeClass("hidden");

                    } else {
                        $('#amendModal input[name=amendDetailsRequestedName]').val('').attr("readonly", false);
                        $("#amendModal textarea[name=amendDetailNotes]").val('').attr("readonly", false);
                        $("#amendModal textarea[name=cancelPreview]").val("Hi Nurses Group, Please Cancel Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview + " because of ");
                        $("#amendModal textarea[name=amendPreview]").val("Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview);
                        $("#amendModal input[name=cancelPreviewHidden]").val("Hi Nurses Group, Please Cancel Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview + " because of ");
                        $("#amendModal input[name=amendPreviewHidden]").val("Booking ID:" + response.booking.bookingId + ", " + response.booking.day + ", " + response.booking.category.name + ", " + preview);
                        //$("#amendModal .amend-save").addClass('hidden');
                        $(".amendActionButtons").removeClass("hidden");
                        $(".amendDetails").addClass("hidden");
                    }
                }
                $('#amendModal').modal();
                $('#amendModal .bookingStaffAssigned').html(moment(response.booking.created_at, "YYYY-MM-DD HH:mm:ii").format("DD-MM-YYYY HH:mm"));
            }
            $("#global-loader").hide();
        }
    });
});
$('.BookingUnitLogBook').click(function (e) {
    $("#BookingUnitLogBook").hide(); return false;
});

/**
 * Check if urgent booking checkbox should be visible based on the 36-hour rule
 * Shows the checkbox only when the shift starts within the next 36 hours
 */
function checkUrgency($row, timeStr) {
    let dateStr = $row.find('input[name="date[]"]').val(); // format: dd-mm-yyyy DD or dd-mm-yyyy D
    let checkbox = $row.find('.urgentCheckbox');
    let urgentContainer = $row.find('.BxUrgent, .UrgBkng'); // Handle both class names

    if (!dateStr || !timeStr) {
        urgentContainer.hide();
        checkbox.prop('checked', false);
        return;
    }

    // Handle both 'dd-mm-yy DD' and 'dd-mm-yy D' formats
    let cleanedDateStr = dateStr.trim().split(" ")[0]; // remove weekday
    let [day, month, year] = cleanedDateStr.split("-");
    
    // Handle 2-digit year format from datepicker
    if (year.length === 2) {
        year = "20" + year;
    }

    let startDateTime = new Date(`${year}-${month}-${day}T${timeStr}:00`);

    if (isNaN(startDateTime)) {
        urgentContainer.hide();
        checkbox.prop('checked', false);
        return;
    }

    let now = new Date();
    let diffInHours = (startDateTime - now) / (1000 * 60 * 60);

    // Show checkbox only if shift starts within next 36 hours
    if (diffInHours <= 36 && diffInHours > 0) {
        urgentContainer.show();
        checkbox.prop('disabled', false);
        // Don't automatically check - let user decide
    } else {
        urgentContainer.hide();
        checkbox.prop('checked', false);
    }
}

// Event handlers for urgent booking logic
$(document).on('change', 'input[name="date[]"], input[name="start_time[]"]', function () {
    const $row = $(this).closest('.full_width');
    const timeStr = $row.find('input[name="start_time[]"]').val();
    checkUrgency($row, timeStr);
});