/**
 * Dashboard Unit and Financial Year Filter Functionality
 * Handles dropdown interactions and AJAX requests for dynamic filtering
 */

class DashboardFilters {
    constructor() {
        this.unitDropdown = document.getElementById('unit-filter');
        this.financialYearDropdown = document.getElementById('financial-year-filter');
        this.init();
    }

    init() {
        if (this.unitDropdown) {
            this.unitDropdown.addEventListener('change', this.handleUnitChange.bind(this));
        }
        
        if (this.financialYearDropdown) {
            this.financialYearDropdown.addEventListener('change', this.handleFinancialYearChange.bind(this));
        }
        
        // Initialize financial year dropdown state
        this.updateFinancialYearDropdown();
        
        // If unit is already selected on page load, ensure financial year dropdown is enabled
        this.initializeFromPageState();
    }
    
    /**
     * Initialize dropdowns based on current page state (URL parameters)
     */
    initializeFromPageState() {
        const selectedUnit = this.unitDropdown?.value;
        
        if (selectedUnit && this.financialYearDropdown) {
            // If unit is selected but financial year dropdown is disabled, enable it
            if (this.financialYearDropdown.disabled && this.financialYearDropdown.options.length <= 1) {
                this.loadFinancialYears(selectedUnit);
            } else {
                this.financialYearDropdown.disabled = false;
            }
        }
    }

    /**
     * Handle unit selection change
     */
    async handleUnitChange() {
        const selectedUnitId = this.unitDropdown.value;
        
        if (!selectedUnitId) {
            this.resetFinancialYearDropdown();
            this.triggerDataRefresh();
            return;
        }

        await this.loadFinancialYears(selectedUnitId);
        this.triggerDataRefresh();
    }

    /**
     * Load financial years for selected unit via AJAX
     */
    async loadFinancialYears(unitId) {
        try {
            this.showLoadingState();
            
            const response = await fetch(`/dashboard/financial-years?unit_id=${unitId}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            this.populateFinancialYears(data.financial_years);
            
        } catch (error) {
            console.error('Error loading financial years:', error);
            this.showErrorState();
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * Populate financial year dropdown with fetched data
     */
    populateFinancialYears(financialYears) {
        if (!this.financialYearDropdown) return;

        // Clear existing options except the first one
        this.financialYearDropdown.innerHTML = '<option value="">Select Financial Year</option>';

        if (financialYears && financialYears.length > 0) {
            financialYears.forEach(year => {
                const option = document.createElement('option');
                option.value = year;
                const nextYearShort = String((parseInt(year) + 1) % 100).padStart(2, '0');
                option.textContent = `${year}-${nextYearShort}`;
                this.financialYearDropdown.appendChild(option);
            });

            this.financialYearDropdown.disabled = false;
        } else {
            this.resetFinancialYearDropdown();
        }
    }

    /**
     * Reset financial year dropdown to initial state
     */
    resetFinancialYearDropdown() {
        if (!this.financialYearDropdown) return;
        
        this.financialYearDropdown.innerHTML = '<option value="">Select Financial Year</option>';
        this.financialYearDropdown.disabled = true;
    }

    /**
     * Update financial year dropdown based on current unit selection
     */
    updateFinancialYearDropdown() {
        const selectedUnit = this.unitDropdown?.value;
        
        if (!selectedUnit) {
            this.resetFinancialYearDropdown();
        }
    }

    /**
     * Show loading state for financial year dropdown
     */
    showLoadingState() {
        if (!this.financialYearDropdown) return;
        
        this.financialYearDropdown.innerHTML = '<option value="">Loading...</option>';
        this.financialYearDropdown.disabled = true;
    }

    /**
     * Hide loading state
     */
    hideLoadingState() {
        // Loading state is hidden when options are populated
    }

    /**
     * Show error state
     */
    showErrorState() {
        if (!this.financialYearDropdown) return;
        
        this.financialYearDropdown.innerHTML = '<option value="">Error loading years</option>';
        this.financialYearDropdown.disabled = true;
    }

    /**
     * Get current filter values
     */
    getCurrentFilters() {
        return {
            unitId: this.unitDropdown?.value || null,
            financialYear: this.financialYearDropdown?.value || null
        };
    }

    /**
     * Set filter values programmatically
     */
    setFilters(unitId = null, financialYear = null) {
        if (this.unitDropdown && unitId) {
            this.unitDropdown.value = unitId;
            this.handleUnitChange();
        }

        if (this.financialYearDropdown && financialYear) {
            // Wait for unit change to complete then set financial year
            setTimeout(() => {
                this.financialYearDropdown.value = financialYear;
            }, 500);
        }
    }

    /**
     * Trigger data refresh based on current filters
     */
    triggerDataRefresh() {
        const filters = this.getCurrentFilters();
        
        // Show loading state on dashboard components
        this.showDashboardLoading();
        
        // Refresh the entire page with the new filters to update all data
        this.refreshPageWithFilters(filters);
        
        // Dispatch custom event for other dashboard components to listen to
        const event = new CustomEvent('dashboardFiltersChanged', {
            detail: filters
        });
        document.dispatchEvent(event);
    }

    /**
     * Refresh page with current filter values
     */
    refreshPageWithFilters(filters) {
        const url = new URL(window.location.href);
        
        // Clear existing filter parameters
        url.searchParams.delete('unit_id');
        url.searchParams.delete('financial_year');
        
        // Add current filter values
        if (filters.unitId) {
            url.searchParams.set('unit_id', filters.unitId);
        }
        if (filters.financialYear) {
            url.searchParams.set('financial_year', filters.financialYear);
        }
        
        // Redirect to the updated URL
        window.location.href = url.toString();
    }

    /**
     * Show loading state on dashboard components
     */
    showDashboardLoading() {
        // Add loading overlay to main content areas
        const dashboardCards = document.querySelectorAll('.header_cards .card, .booking_card_grid .card');
        dashboardCards.forEach(card => {
            card.style.opacity = '0.6';
            card.style.pointerEvents = 'none';
        });
        
        // Show loading message
        const loadingMessage = document.createElement('div');
        loadingMessage.id = 'dashboard-loading';
        loadingMessage.innerHTML = `
            <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                        background: rgba(255,255,255,0.9); padding: 20px; border-radius: 8px; 
                        box-shadow: 0 4px 6px rgba(0,0,0,0.1); z-index: 9999;">
                <div style="text-align: center;">
                    <div style="display: inline-block; width: 20px; height: 20px; border: 3px solid #f3f3f3; 
                                border-top: 3px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                    <div style="margin-top: 10px;">Updating dashboard...</div>
                </div>
            </div>
            <style>
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            </style>
        `;
        document.body.appendChild(loadingMessage);
    }

    /**
     * Refresh dashboard data via AJAX (optional)
     */
    async refreshDashboardData(filters) {
        try {
            const params = new URLSearchParams();
            if (filters.unitId) params.append('unit_id', filters.unitId);
            if (filters.financialYear) params.append('financial_year', filters.financialYear);
            
            const response = await fetch(`/dashboard/filtered-data?${params}`, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                },
            });

            if (!response.ok) {
                throw new Error('Network response was not ok');
            }

            const data = await response.json();
            
            // Dispatch event with the filtered data
            const event = new CustomEvent('dashboardDataRefreshed', {
                detail: data
            });
            document.dispatchEvent(event);
            
        } catch (error) {
            console.error('Error refreshing dashboard data:', error);
        }
    }

    /**
     * Handle financial year selection change
     */
    handleFinancialYearChange() {
        this.triggerDataRefresh();
    }
}

// Initialize dashboard filters when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new DashboardFilters();
});

// Export for potential external use
window.DashboardFilters = DashboardFilters;
