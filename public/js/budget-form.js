/**
 * Budget Form Main Module
 * Coordinates all budget form functionality
 */

window.BudgetForm = {
    /**
     * Initialize the budget form
     */
    init: function() {
        this.setupGlobalVariables();
        this.bindEvents();
        this.initializeModules();
        this.setupTooltips();
        this.enableRealTimeCalculations();
        this.setupFormValidation();
    },

    /**
     * Setup global variables from Laravel
     */
    setupGlobalVariables: function() {
        // These will be set from the Blade template
        window.routes = window.routes || {};
        window.csrfToken = window.csrfToken || $('meta[name="csrf-token"]').attr('content');
        window.unitId = window.unitId || '';
        window.financialYear = window.financialYear || '';
    },

    /**
     * Bind main form events
     */
    bindEvents: function() {
        // Financial year selector with debugging
        const financialYearDropdown = $('#financial_year');
        console.log('Financial year dropdown found:', financialYearDropdown.length > 0);

        if (financialYearDropdown.length > 0) {
            financialYearDropdown.on('change', this.handleFinancialYearChange.bind(this));
            console.log('Financial year change handler bound successfully');
        } else {
            console.error('Financial year dropdown not found!');
        }

        // Annual budget form calculations using data attributes
        $('[data-calculation-input]').on('input', this.calculateTotalAllocation.bind(this));

        // Week number selector
        $('#week_number').on('change', this.handleWeekNumberChange.bind(this)).trigger('change');
    },

    /**
     * Handle financial year change
     */
    handleFinancialYearChange: function() {
        console.log('Financial year change triggered!');

        const selectedYear = $('#financial_year').val();
        const unitId = window.unitId;

        console.log('Selected year:', selectedYear);
        console.log('Unit ID:', unitId);

        if (selectedYear && unitId) {
            // Construct the URL directly since the route template might have issues
            const baseUrl = window.location.origin;
            const newUrl = `${baseUrl}/budget/allocation/edit/${unitId}/${selectedYear}`;
            console.log('Navigating to:', newUrl);

            // Add a small delay to ensure the console logs are visible
            setTimeout(() => {
                window.location.href = newUrl;
            }, 100);
        } else {
            console.error('Missing required data:', { selectedYear, unitId });
            alert('Error: Missing required data for navigation. Check console for details.');
        }
    },

    /**
     * Calculate total weekly allocation
     */
    calculateTotalAllocation: function() {
        const weeklyBudget = parseFloat($('#weekly_budget').val()) || 0;
        const weeklyAllowance = parseFloat($('#weekly_allowance').val()) || 0;
        const specialAllowance = parseFloat($('#special_allowance').val()) || 0;
        const total = weeklyBudget + weeklyAllowance + specialAllowance;
        $('#total_weekly_allocation').val(total.toFixed(2));
    },

    /**
     * Handle week number change
     */
    handleWeekNumberChange: function() {
        const selectedOption = $('#week_number').find('option:selected');
        $('#week_start_date').val(selectedOption.data('start'));
        $('#week_end_date').val(selectedOption.data('end'));
    },

    /**
     * Initialize all modules
     */
    initializeModules: function() {
        // Initialize modules if they exist
        if (window.BudgetAutoGenerate) {
            BudgetAutoGenerate.init();
        }

        if (window.BudgetRecalculate) {
            BudgetRecalculate.init();
        }

        if (window.BudgetEdit) {
            BudgetEdit.init();
        }

        if (window.BudgetTransfer) {
            BudgetTransfer.init();
        }
    },

    /**
     * Setup tooltips
     */
    setupTooltips: function() {
        // Initialize Bootstrap tooltips
        $('[data-toggle="tooltip"]').tooltip();

        // Add tooltip content for columns with (i) indicator
        $('th:contains("(i)")').attr('data-toggle', 'tooltip')
            .attr('title', 'Click for more information');

        $('th:contains("Total Weekly Utilisation (i)")').attr('data-toggle', 'tooltip')
            .attr('title', 'The amount spent or utilized from the total weekly allocation');

        $('th:contains("Internal Transfers (i)")').attr('data-toggle', 'tooltip')
            .attr('title', 'Funds transferred internally between weeks');
    },

    /**
     * Show loading overlay
     */
    showLoading: function(message = 'Loading...') {
        if (!$('#budget-loading-overlay').length) {
            $('body').append(`
                <div id="budget-loading-overlay" style="
                    position: fixed;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background: rgba(0,0,0,0.5);
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    z-index: 9999;
                    color: white;
                    font-size: 18px;
                ">
                    <div>
                        <i class="fas fa-spinner fa-spin fa-2x"></i>
                        <div style="margin-top: 10px;">${message}</div>
                    </div>
                </div>
            `);
        }
    },

    /**
     * Hide loading overlay
     */
    hideLoading: function() {
        $('#budget-loading-overlay').remove();
    },

    /**
     * Refresh page data
     */
    refreshData: function() {
        this.showLoading('Refreshing data...');
        setTimeout(() => {
            location.reload();
        }, 500);
    },

    /**
     * Export table data (future enhancement)
     */
    exportData: function(format = 'csv') {
        // Implementation for data export
        console.log('Export functionality to be implemented');
    },

    /**
     * Print table (future enhancement)
     */
    printTable: function() {
        // Implementation for printing
        window.print();
    },

    /**
     * Test alert styles (temporary function for demonstration)
     */
    testAlerts: function() {
        alert('Testing alerts - Both simple and auto-hide alerts should now look identical!');

        // Test all alert types with auto-hide functionality
        setTimeout(() => {
            console.log('Showing SUCCESS alert (auto-hide)');
            BudgetUtils.showAlert('✅ SUCCESS - Green text with auto-hide after 5 seconds', 'success');
        }, 500);

        setTimeout(() => {
            console.log('Showing ERROR alert (auto-hide)');
            BudgetUtils.showAlert('❌ ERROR - Red text with auto-hide after 5 seconds', 'error');
        }, 1000);

        setTimeout(() => {
            console.log('Showing WARNING alert (auto-hide)');
            BudgetUtils.showAlert('⚠️ WARNING - Orange text with auto-hide after 5 seconds', 'warning');
        }, 1500);

        setTimeout(() => {
            console.log('Showing INFO alert (auto-hide)');
            BudgetUtils.showAlert('ℹ️ INFO - Blue text with auto-hide after 5 seconds', 'info');
        }, 2000);

        setTimeout(() => {
            console.log('Showing PRIMARY alert (auto-hide)');
            BudgetUtils.showAlert('🔵 PRIMARY - Dark blue text with auto-hide after 5 seconds', 'primary');
        }, 2500);

        setTimeout(() => {
            console.log('Showing SECONDARY alert (auto-hide)');
            BudgetUtils.showAlert('⚫ SECONDARY - Gray text with auto-hide after 5 seconds', 'secondary');
        }, 3000);

        // Show a simple alert for comparison (manual dismiss)
        setTimeout(() => {
            console.log('Showing simple alert for comparison (manual dismiss)');
            BudgetUtils.showSimpleAlert('🔄 SIMPLE ALERT - Same styling but manual dismiss only', 'info');
        }, 3500);
    },

    /**
     * Enhanced annual budget validation
     */
    validateAnnualBudget: function() {
        const annualBudgetInput = $('#annual_budget');
        const value = parseFloat(annualBudgetInput.val());

        // Clear previous validation
        annualBudgetInput.removeClass('is-invalid is-valid');

        if (isNaN(value) || value <= 0) {
            annualBudgetInput.addClass('is-invalid');
            BudgetUtils.showAlert('Annual budget must be a positive number.', 'error');
            return false;
        }

        if (value > 10000000) { // 10 million limit
            annualBudgetInput.addClass('is-invalid');
            BudgetUtils.showAlert('Annual budget seems unusually high. Please verify the amount.', 'warning');
            return false;
        }

        annualBudgetInput.addClass('is-valid');
        return true;
    },

    /**
     * Enhanced form validation
     */
    validateForm: function() {
        let isValid = true;

        // Validate annual budget
        if (!this.validateAnnualBudget()) {
            isValid = false;
        }

        // Validate financial year
        const financialYear = $('#financial_year').val();
        if (!financialYear) {
            BudgetUtils.showAlert('Please select a financial year.', 'error');
            isValid = false;
        }

        return isValid;
    },

    /**
     * Enhanced save functionality with validation
     */
    saveWithValidation: function() {
        if (!this.validateForm()) {
            return false;
        }

        // Show confirmation dialog
        const annualBudget = $('#annual_budget').val();
        const financialYear = $('#financial_year').val();

        const confirmMessage = `Are you sure you want to save the annual budget of £${BudgetUtils.formatNumber(annualBudget)} for financial year ${financialYear}?`;

        if (!confirm(confirmMessage)) {
            return false;
        }

        // Submit the form
        $('#budgetForm').submit();
        return true;
    },

    /**
     * Real-time budget calculations
     */
    enableRealTimeCalculations: function() {
        // Listen for changes in calculation inputs
        $('[data-calculation-input]').on('input', this.performRealTimeCalculation.bind(this));
    },

    /**
     * Perform real-time calculation
     */
    performRealTimeCalculation: function() {
        const annualBudget = parseFloat($('#annual_budget').val()) || 0;
        const numberOfWeeks = parseInt($('#number_of_weeks').val()) || 52;

        if (annualBudget > 0 && numberOfWeeks > 0) {
            const weeklyBudget = annualBudget / numberOfWeeks;

            // Show preview calculation
            this.showCalculationPreview(annualBudget, numberOfWeeks, weeklyBudget);
        }
    },

    /**
     * Show calculation preview
     */
    showCalculationPreview: function(annualBudget, numberOfWeeks, weeklyBudget) {
        let previewHtml = `
            <div class="alert alert-info mt-2" id="calculation-preview">
                <h6><i class="fa fa-calculator"></i> Budget Calculation Preview</h6>
                <div class="row">
                    <div class="col-md-4">
                        <strong>Annual Budget:</strong><br>
                        £${BudgetUtils.formatNumber(annualBudget)}
                    </div>
                    <div class="col-md-4">
                        <strong>Number of Weeks:</strong><br>
                        ${numberOfWeeks} weeks
                    </div>
                    <div class="col-md-4">
                        <strong>Weekly Budget:</strong><br>
                        £${BudgetUtils.formatNumber(weeklyBudget)}
                    </div>
                </div>
            </div>
        `;

        // Remove existing preview
        $('#calculation-preview').remove();

        // Add new preview
        $('#annual_budget').closest('.form-group').after(previewHtml);
    },

    /**
     * Setup form validation
     */
    setupFormValidation: function() {
        // Add validation on form submit
        $('#budgetForm').on('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
                return false;
            }
        });

        // Add real-time validation for annual budget
        $('#annual_budget').on('blur', this.validateAnnualBudget.bind(this));
    }
};

// Initialize when document is ready
$(document).ready(function() {
    BudgetForm.init();

    // Debug: Log all AJAX requests to identify problematic ones
    $(document).ajaxSend(function(event, xhr, settings) {
        console.log('AJAX Request:', settings.url);
    });

    // Debug: Log all AJAX errors to identify the source
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        console.log('AJAX Error Debug:', {
            url: settings.url,
            status: xhr.status,
            error: thrownError,
            readyState: xhr.readyState
        });
    });
});



// Global success handler for AJAX requests
$(document).ajaxSuccess(function(event, xhr, settings) {
    console.log('AJAX Success:', settings.url);
});
