/**
 * Budget Edit Module
 * Handles inline editing of budget rows
 */

window.BudgetEdit = {
    /**
     * Initialize edit functionality
     */
    init: function() {
        this.bindEvents();
    },

    /**
     * Bind event handlers
     */
    bindEvents: function() {
        // Use data attributes for button actions
        $(document).on('click', '[data-action="edit-row"]', this.handleEditRow.bind(this));
        $(document).on('click', '[data-action="save-row"]', this.handleSaveRow.bind(this));
        $(document).on('click', '[data-action="cancel-row"]', this.handleCancelRow.bind(this));
    },

    /**
     * Handle edit row button click
     */
    handleEditRow: function(event) {
        const row = $(event.target).closest('tr');
        const cells = row.find('td');

        // Get current values
        const currentValues = this.extractCurrentValues(cells);

        // Replace cells with input fields
        this.createEditInputs(cells, currentValues);

        // Add event listeners for automatic calculations
        this.bindCalculationEvents(row);
    },

    /**
     * Extract current values from row cells
     */
    extractCurrentValues: function(cells) {
        return {
            weekNumber: cells.eq(1).text().replace('Week ', ''),
            endDate: BudgetUtils.formatDateForInput(cells.eq(2).text()),
            weeklyBudget: BudgetUtils.parseCurrency(cells.eq(3).text()),
            weeklyAllowance: BudgetUtils.parseCurrency(cells.eq(4).text()),
            specialAllowance: BudgetUtils.parseCurrency(cells.eq(5).text()),
            internalTransfers: BudgetUtils.parseCurrency(cells.eq(6).text()),
            totalAllocation: BudgetUtils.parseCurrency(cells.eq(7).text()),
            totalUtilisation: BudgetUtils.parseCurrency(cells.eq(8).text()),
            percentUtilisation: parseFloat(cells.eq(9).text().replace('%', '').replace(',', '')) || 0,
            weeklyUnutilisedFund: BudgetUtils.parseCurrency(cells.eq(10).text()),
            consolidatedUnutilisedFund: BudgetUtils.parseCurrency(cells.eq(11).text()),
            balanceFund: BudgetUtils.parseCurrency(cells.eq(12).text()),
            notes: cells.eq(13).text()
        };
    },

    /**
     * Create edit input fields
     */
    createEditInputs: function(cells, values) {
        cells.eq(2).html(`<input type="date" class="form-control" value="${values.endDate}" readonly>`);
        cells.eq(3).html(`<input type="number" step="0.01" min="0" class="form-control edit-weekly-budget" value="${values.weeklyBudget}" readonly>`);
        cells.eq(4).html(`<input type="number" step="0.01" min="0" class="form-control edit-weekly-allowance" value="${values.weeklyAllowance}" readonly>`);
        cells.eq(5).html(`<input type="number" step="0.01" min="0" class="form-control edit-special-allowance" value="${values.specialAllowance}">`);
        cells.eq(6).html(`<input type="number" step="0.01" class="form-control edit-internal-transfers" value="${values.internalTransfers}">`);
        cells.eq(7).html(`<input type="number" step="0.01" min="0" class="form-control edit-total-allocation" value="${values.totalAllocation}" readonly>`);
        cells.eq(8).html(`<input type="number" step="0.01" min="0" class="form-control edit-total-utilisation" value="${values.totalUtilisation}" readonly>`);
        cells.eq(9).html(`<input type="number" step="0.01" min="0" max="100" class="form-control edit-percent-utilisation" value="${values.percentUtilisation}" readonly>`);
        cells.eq(10).html(`<input type="number" step="0.01" class="form-control edit-weekly-unutilised" value="${values.weeklyUnutilisedFund}" readonly>`);
        cells.eq(11).html(`<input type="number" step="0.01" class="form-control edit-consolidated-unutilised" value="${values.consolidatedUnutilisedFund}" readonly>`);
        cells.eq(12).html(`<input type="number" step="0.01" class="form-control edit-balance-fund" value="${values.balanceFund}" readonly>`);
        cells.eq(13).html(`<textarea class="form-control">${values.notes}</textarea>`);

        // Replace edit button with save/cancel buttons - use data attributes
        cells.eq(14).html(`
            <button type="button" class="btn btn-sm btn-success" data-action="save-row" data-button-type="save-row">Save</button>
            <button type="button" class="btn btn-sm btn-danger" data-action="cancel-row" data-button-type="cancel-row">Cancel</button>
        `);
    },

    /**
     * Bind calculation events for edit mode
     */
    bindCalculationEvents: function(row) {
        row.find('.edit-special-allowance, .edit-internal-transfers')
           .on('input', () => this.calculateEditRowTotals(row));

        row.find('.edit-total-utilisation')
           .on('input', () => this.calculateEditRowUtilisation(row));
    },

    /**
     * Calculate totals in edit mode
     */
    calculateEditRowTotals: function(row) {
        const cells = row.find('td');

        const weeklyBudget = parseFloat(cells.eq(3).find('input').val()) || 0;
        const weeklyAllowance = parseFloat(cells.eq(4).find('input').val()) || 0;
        const specialAllowance = parseFloat(cells.eq(5).find('input').val()) || 0;
        const internalTransfers = parseFloat(cells.eq(6).find('input').val()) || 0;

        const totalAllocation = weeklyBudget + weeklyAllowance + specialAllowance + internalTransfers;
        cells.eq(7).find('input').val(totalAllocation.toFixed(2));

        this.calculateEditRowUtilisation(row);
    },

    /**
     * Calculate utilisation in edit mode
     */
    calculateEditRowUtilisation: function(row) {
        const cells = row.find('td');

        const totalAllocation = parseFloat(cells.eq(7).find('input').val()) || 0;
        const totalUtilisation = parseFloat(cells.eq(8).find('input').val()) || 0;

        // Calculate percentage
        const percentUtilisation = totalAllocation > 0 ? (totalUtilisation / totalAllocation) * 100 : 0;
        cells.eq(9).find('input').val(percentUtilisation.toFixed(2));

        // Calculate weekly unutilised fund
        const weeklyUnutilised = totalAllocation - totalUtilisation;
        cells.eq(10).find('input').val(weeklyUnutilised.toFixed(2));

        this.calculateEditRowBalance(row);
    },

    /**
     * Calculate balance in edit mode
     */
    calculateEditRowBalance: function(row) {
        const cells = row.find('td');

        const consolidatedUnutilised = parseFloat(cells.eq(11).find('input').val()) || 0;
        const internalTransfers = parseFloat(cells.eq(6).find('input').val()) || 0;

        const balanceFund = consolidatedUnutilised + internalTransfers;
        cells.eq(12).find('input').val(balanceFund.toFixed(2));
    },

    /**
     * Handle save row button click
     */
    handleSaveRow: function(event) {
        const row = $(event.target).closest('tr');
        const id = row.data('id');
        const cells = row.find('td');

        // Get values from input fields
        const formData = this.extractFormData(cells);

        // Validate data
        if (!this.validateRowData(formData)) {
            return;
        }

        BudgetUtils.showLoading($(event.target), 'Saving...');

        // Send AJAX request to update weekly budget
        $.ajax({
            url: window.routes.updateWeekly,
            type: 'POST',
            data: {
                _token: window.csrfToken,
                id: id,
                ...formData
            },
            success: this.handleSaveSuccess.bind(this),
            error: this.handleSaveError.bind(this, $(event.target))
        });
    },

    /**
     * Extract form data from edit inputs
     */
    extractFormData: function(cells) {
        const weekInterval = parseInt($('#week_interval').val()) || 7;
        const endDate = cells.eq(2).find('input').val();
        const endDateObj = new Date(endDate);
        const startDateObj = new Date(endDateObj);
        startDateObj.setDate(endDateObj.getDate() - (weekInterval - 1));
        const formattedStartDate = startDateObj.toISOString().split('T')[0];

        return {
            week_start_date: formattedStartDate,
            week_end_date: endDate,
            weekly_budget: parseFloat(cells.eq(3).find('input').val()) || 0,
            weekly_allowance: parseFloat(cells.eq(4).find('input').val()) || 0,
            special_allowance: parseFloat(cells.eq(5).find('input').val()) || 0,
            internal_transfers: parseFloat(cells.eq(6).find('input').val()) || 0,
            total_weekly_allocation: parseFloat(cells.eq(7).find('input').val()) || 0,
            total_weekly_utilisation: parseFloat(cells.eq(8).find('input').val()) || 0,
            percent_utilisation: parseFloat(cells.eq(9).find('input').val()) || 0,
            weekly_unutilised_fund: parseFloat(cells.eq(10).find('input').val()) || 0,
            notes: cells.eq(13).find('textarea').val()
        };
    },

    /**
     * Validate row data
     */
    validateRowData: function(data) {
        if (data.weekly_budget < 0) {
            BudgetUtils.showAlert('Weekly budget cannot be negative.', 'error');
            return false;
        }

        if (data.weekly_allowance < 0) {
            BudgetUtils.showAlert('Weekly allowance cannot be negative.', 'error');
            return false;
        }

        if (!data.week_end_date) {
            BudgetUtils.showAlert('Week end date is required.', 'error');
            return false;
        }

        return true;
    },

    /**
     * Handle save success response
     */
    handleSaveSuccess: function(response) {
        if (response.success) {
            BudgetUtils.showAlert('Row updated successfully.', 'success');
            setTimeout(() => location.reload(), 1000);
        }
    },

    /**
     * Handle save error response
     */
    handleSaveError: function(button, xhr) {
        BudgetUtils.hideLoading(button, 'Save');
        BudgetUtils.handleAjaxError(xhr, 'An error occurred while updating the weekly budget. Please try again.');
    },

    /**
     * Handle cancel row button click
     */
    handleCancelRow: function() {
        location.reload();
    }
};
