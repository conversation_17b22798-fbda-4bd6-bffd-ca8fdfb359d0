/**
 * Shift Cover Report DataTable Implementation
 * Uses Yajra DataTables for server-side processing
 */

$(document).ready(function() {
    'use strict';

    // Initialize variables
    let table;
    let initialTotal = 0;
    let finalTotal = 0;

    // Initialize DataTable
    function initializeDataTable() {
        table = $('#shift-cover-table').DataTable({
            processing: true,
            serverSide: true,
            responsive: true,
            ajax: {
                url: '/reports/shift-cover-report/data',
                data: function(d) {
                    // Add filter parameters
                    d.unit_id = $('#unit_id').val();
                    d.start_date = $('#start_date').val();
                    d.end_date = $('#end_date').val();
                },
                beforeSend: function() {
                    showLoading();
                },
                complete: function() {
                    hideLoading();
                },
                error: function(xhr, error, thrown) {
                    hideLoading();
                    console.error('DataTable Error:', error, thrown);
                    
                    // Show user-friendly error message
                    toastr.error('Failed to load data. Please try again.', 'Error');
                }
            },
            columns: [
                { 
                    data: 'bookingId', 
                    name: 'bookingId',
                    title: 'Booking ID',
                    className: 'text-center'
                },
                { 
                    data: 'account_code', 
                    name: 'account_code',
                    title: 'Account Code',
                    className: 'text-center',
                    orderable: false,
                    searchable: false
                },
                { 
                    data: 'date', 
                    name: 'date',
                    title: 'Date',
                    className: 'text-center'
                },
                { 
                    data: 'shift_name', 
                    name: 'shift_name',
                    title: 'Shift'
                },
                { 
                    data: 'category_name', 
                    name: 'category_name',
                    title: 'Category'
                },
                { 
                    data: 'staff_name', 
                    name: 'staff_name',
                    title: 'Staff'
                },
                { 
                    data: 'start_time', 
                    name: 'start_time',
                    title: 'Start Time',
                    className: 'text-center'
                },
                { 
                    data: 'end_time', 
                    name: 'end_time',
                    title: 'End Time',
                    className: 'text-center'
                },
                { 
                    data: 'hours_worked', 
                    name: 'hours_worked',
                    title: 'Hours',
                    className: 'text-center',
                    orderable: false,
                    searchable: false
                },
                { 
                    data: 'created_at', 
                    name: 'created_at',
                    title: 'Shift Booked On',
                    className: 'text-center'
                },
                { 
                    data: 'reason_text', 
                    name: 'reason_text',
                    title: 'Reason for Booking'
                },
                { 
                    data: 'initial_cost', 
                    name: 'initial_cost',
                    title: 'Initial Amount',
                    className: 'text-end'
                },
                { 
                    data: 'unit_final_cost', 
                    name: 'unit_final_cost',
                    title: 'Final Amount',
                    className: 'text-end'
                }
            ],
            order: [[2, 'desc']], // Order by date descending
            pageLength: 25,
            lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "All"]],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
                 '<"row"<"col-sm-12"tr>>' +
                 '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
            language: {
                processing: '<div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div>',
                emptyTable: 'No shift cover data available',
                zeroRecords: 'No matching records found',
                info: 'Showing _START_ to _END_ of _TOTAL_ entries',
                infoEmpty: 'Showing 0 to 0 of 0 entries',
                infoFiltered: '(filtered from _MAX_ total entries)',
                lengthMenu: 'Show _MENU_ entries',
                search: 'Search:',
                paginate: {
                    first: 'First',
                    last: 'Last',
                    next: 'Next',
                    previous: 'Previous'
                }
            },
            drawCallback: function(settings) {
                // Calculate totals after each draw
                calculateTotals();
                
                // Initialize tooltips if any
                $('[data-bs-toggle="tooltip"]').tooltip();
            },
            initComplete: function() {
                console.log('DataTable initialized successfully');
            }
        });
    }

    // Calculate totals from visible data
    function calculateTotals() {
        initialTotal = 0;
        finalTotal = 0;

        // Get all data from current page
        table.rows({page: 'current'}).every(function() {
            const data = this.data();
            
            // Parse currency values
            const initial = parseFloat(data.initial_cost.replace(/[£,]/g, '')) || 0;
            const final = parseFloat(data.unit_final_cost.replace(/[£,]/g, '')) || 0;
            
            initialTotal += initial;
            finalTotal += final;
        });

        // Update footer and summary cards
        updateTotalDisplays();
    }

    // Update total displays
    function updateTotalDisplays() {
        const formattedInitial = '£' + initialTotal.toLocaleString('en-GB', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
        
        const formattedFinal = '£' + finalTotal.toLocaleString('en-GB', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });

        // Update footer
        $('#initial-total').text(formattedInitial);
        $('#final-total').text(formattedFinal);

        // Update summary cards
        $('#summary-initial').text(formattedInitial);
        $('#summary-final').text(formattedFinal);
    }

    // Show loading overlay
    function showLoading() {
        $('#loading-overlay').show();
    }

    // Hide loading overlay
    function hideLoading() {
        $('#loading-overlay').hide();
    }

    // Apply filters
    function applyFilters() {
        if (table) {
            table.ajax.reload();
        }
    }

    // Reset filters
    function resetFilters() {
        $('#filter-form')[0].reset();
        applyFilters();
    }

    // Validate date range
    function validateDateRange() {
        const startDate = $('#start_date').val();
        const endDate = $('#end_date').val();

        if (startDate && endDate && startDate > endDate) {
            toastr.warning('End date must be after start date', 'Invalid Date Range');
            return false;
        }
        return true;
    }

    // Export functionality
    function exportToExcel() {
        if (!table) return;

        // Get current filter parameters
        const params = new URLSearchParams({
            unit_id: $('#unit_id').val() || '',
            start_date: $('#start_date').val() || '',
            end_date: $('#end_date').val() || '',
            export: 'excel'
        });

        // Create download link
        const url = `/reports/shift-cover-report/export?${params.toString()}`;
        window.open(url, '_blank');
    }

    function exportToPDF() {
        if (!table) return;

        // Get current filter parameters
        const params = new URLSearchParams({
            unit_id: $('#unit_id').val() || '',
            start_date: $('#start_date').val() || '',
            end_date: $('#end_date').val() || '',
            export: 'pdf'
        });

        // Create download link
        const url = `/reports/shift-cover-report/export?${params.toString()}`;
        window.open(url, '_blank');
    }

    // Event Handlers
    $('#apply-filters').on('click', function() {
        if (validateDateRange()) {
            applyFilters();
        }
    });

    $('#reset-filters').on('click', function() {
        resetFilters();
    });

    $('#export-excel').on('click', function() {
        exportToExcel();
    });

    $('#export-pdf').on('click', function() {
        exportToPDF();
    });

    // Auto-apply filters on Enter key
    $('#filter-form input, #filter-form select').on('keypress change', function(e) {
        if (e.type === 'change' || e.which === 13) {
            if (validateDateRange()) {
                applyFilters();
            }
        }
    });

    // Set default date range (last 30 days)
    function setDefaultDateRange() {
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        $('#end_date').val(today.toISOString().split('T')[0]);
        $('#start_date').val(thirtyDaysAgo.toISOString().split('T')[0]);
    }

    // Initialize everything
    function init() {
        console.log('Initializing Shift Cover Report...');
        
        // Set default date range
        setDefaultDateRange();
        
        // Initialize DataTable
        initializeDataTable();
        
        console.log('Shift Cover Report initialized successfully');
    }

    // Start initialization
    init();

    // Global error handler for AJAX requests
    $(document).ajaxError(function(event, xhr, settings, thrownError) {
        if (settings.url.includes('shift-cover-report')) {
            hideLoading();
            console.error('AJAX Error:', thrownError);
        }
    });
});
