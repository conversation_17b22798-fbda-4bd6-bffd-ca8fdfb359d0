/* Dashboard Filter Styles */
.dashboard-filters {
    margin-bottom: 20px;
}

.filter-card {
    background: #fff;
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.filter-card .card-header {
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    padding: 1rem 1.25rem;
}

.filter-card .card-title {
    color: #5a5c69;
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.filter-card .card-body {
    padding: 1.25rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
    margin-bottom: 0.5rem;
    display: block;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #6e707e;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d1d3e2;
    border-radius: 0.35rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #6e707e;
    background-color: #fff;
    border-color: #bac8f3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-control:disabled {
    background-color: #eaecf4;
    opacity: 1;
    cursor: not-allowed;
}

.form-control option {
    padding: 8px;
}

/* Loading state styles */
.form-control.loading {
    background-image: url("data:image/svg+xml,%3csvg width='20' height='20' viewBox='0 0 20 20' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill='%236c757d' d='M10 3.5a6.5 6.5 0 106.5 6.5h-1.5A5 5 0 1010 5V3.5z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .filter-card .card-body {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1.5rem;
    }
}

/* Button styles for future enhancements */
.filter-btn {
    background-color: #4e73df;
    border-color: #4e73df;
    color: #fff;
    padding: 0.5rem 1rem;
    border-radius: 0.35rem;
    font-weight: 400;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    font-size: 1rem;
    line-height: 1.5;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-btn:hover {
    background-color: #2e59d9;
    border-color: #2653d4;
}

.filter-btn:disabled {
    opacity: 0.65;
    cursor: not-allowed;
}

/* Error state styles */
.form-control.error {
    border-color: #e74a3b;
}

.error-message {
    color: #e74a3b;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Success state styles */
.form-control.success {
    border-color: #1cc88a;
}
